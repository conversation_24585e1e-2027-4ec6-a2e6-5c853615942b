{
  "include": ["remix.env.d.ts", "**/*.ts", "**/*.tsx", "./codegen.ts", ".react-router/types/**/*"],
  "compilerOptions": {
    "lib": ["DOM", "DOM.Iterable", "ES2022"],
    "types": ["@react-router/node", "vite/client"],
    "isolatedModules": true,
    "esModuleInterop": true,
    "module": "ESNext",
    "jsx": "react-jsx",
    "moduleResolution": "Bundler",
    "resolveJsonModule": true,
    "target": "ES2022",
    "strict": true,
    "allowJs": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "baseUrl": ".",
    "rootDirs": [".", "./.react-router/types"],
    "paths": {
      "@/*": ["./app/*"],
      "@components/*": ["./app/components/*"],
      "@lib/*": ["./app/lib/*"],
      "@features/*": ["./app/features/*"]
    },
    // Remix takes care of building everything in `remix build`.
    "noEmit": true
  }
}
