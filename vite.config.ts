import { reactRouter } from "@react-router/dev/vite"
import { defineConfig } from "vite"
import tsconfigPaths from "vite-tsconfig-paths"

export default defineConfig({
  plugins: [
    reactRouter(),
    // reactRouter({
    //   future: {
    //     unstable_optimizeDeps: true,
    //     v3_fetcherPersist: true,
    //     v3_relativeSplatPath: true,
    //     v3_throwAbortReason: true,
    //   },
    // }),
    tsconfigPaths(),
  ],
  ssr: {
    noExternal: ["use-immer"],
  },
  server: {
    port: 3001,
  },
})
