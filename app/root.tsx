import { type LinksFunction } from "react-router"
import {
  Links,
  Meta,
  NavLink,
  Outlet,
  Scripts,
  ScrollRestoration,
  isRouteErrorResponse,
  useNavigate,
  useNavigation,
  useRouteError,
} from "react-router"
import { Toaster } from "react-hot-toast"
import { useEffect, useRef, useState } from "react"
import NProgress from "nprogress"
import nprogressStyles from "nprogress/nprogress.css?url"
import {
  MutationCache,
  QueryCache,
  QueryClient,
  QueryClientProvider,
} from "@tanstack/react-query"

import tailwindStylesheet from "./tailwind.css?url"
import fontStylesheet from "./fonts.css?url"

import { ErrorComponent } from "@components/common"
import { IconCheck, IconExclamation } from "@components/icons"
import { baseUrl } from "@lib/utils"
import { ClientError } from "graphql-request"
import { loadRazorpayScript } from "@/lib/utils/load-razorpay-script"
import ErrorBoundaryLayout from "./components/common/Layout/error-boundary-layout"
import advancedFormat from "dayjs/plugin/advancedFormat"
import dayjs from "dayjs"

export const links: LinksFunction = () => [
  // { rel: "preload", href: fontStylesheet, as: "style" },
  // { rel: "preload", href: tailwindStylesheet, as: "style" },
  // { rel: "preload", href: nprogressStyles, as: "style" },
  { rel: "stylesheet", href: fontStylesheet },
  { rel: "stylesheet", href: tailwindStylesheet },
  { rel: "stylesheet", href: nprogressStyles },
]

dayjs.extend(advancedFormat)

export default function App() {
  const navigation = useNavigation()
  const nprogress = NProgress.configure({
    showSpinner: false,
  })
  const navigate = useNavigate()
  const loadRazorpay = useRef<boolean | null>(null)

  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 60 * 1000,
            retry: (failureCount, error) => {
              if (error instanceof ClientError) {
                if (error?.response?.status === 419) {
                  return false
                }
              }

              return failureCount < 2
            },
          },
        },
        mutationCache: new MutationCache({
          onError: async (error) => {
            if (error instanceof ClientError) {
              if (error.response?.status === 419) {
                console.log("Handling 419 CSRF token error")

                try {
                  await fetch(`${baseUrl}/csrf`, {
                    credentials: "include",
                  })

                  // Important: Instead of navigating away, retry the failed queries
                  const failedQueries = queryClient.getQueryCache().findAll({
                    predicate: (query) => query.state.status === "error",
                  })

                  // Wait a moment for the cookie to be set
                  setTimeout(() => {
                    failedQueries.forEach((query) => {
                      queryClient.refetchQueries({ queryKey: query.queryKey })
                    })
                  }, 100)
                } catch (refreshError) {
                  console.error("Failed to refresh CSRF token:", refreshError)
                  // Only navigate away if token refresh fails
                  navigate({ pathname: "/" })
                }
              } else {
                error?.response?.errors?.map((error) => {
                  // alert("unauthenticated error")
                  if (error.message === "Unauthenticated.") {
                    navigate({
                      pathname: "/logout",
                    })
                  }
                })
              }
            }
          },
        }),
        queryCache: new QueryCache({
          onError: async (error) => {
            if (error instanceof ClientError) {
              if (error.response?.status === 419) {
                try {
                  await fetch(`${baseUrl}/csrf`, {
                    credentials: "include",
                  })

                  // Important: Instead of navigating away, retry the failed queries
                  const failedQueries = queryClient.getQueryCache().findAll({
                    predicate: (query) => query.state.status === "error",
                  })

                  // Wait a moment for the cookie to be set
                  setTimeout(() => {
                    failedQueries.forEach((query) => {
                      queryClient.refetchQueries({ queryKey: query.queryKey })
                    })
                  }, 100)
                } catch (refreshError) {
                  console.error("Failed to refresh CSRF token:", refreshError)
                  // Only navigate away if token refresh fails
                  navigate({ pathname: "/" })
                }
              } else if (error?.response?.status === 429) {
                const location = globalThis.location.pathname
                if (location) {
                  navigate({
                    pathname: `/too-many-requests?ref=${location}`,
                  })
                } else {
                  navigate({
                    pathname: `/too-many-requests`,
                  })
                }
              } else {
                error?.response?.errors?.map((error) => {
                  // alert("unauthenticated error")
                  if (error.message === "Unauthenticated.") {
                    navigate({
                      pathname: "/logout",
                    })
                  }
                })
              }
            }
          },
        }),
      })
  )

  useEffect(() => {
    if (navigation.state !== "idle") {
      nprogress.start()
    } else {
      nprogress.done()
    }
  }, [navigation.state, nprogress])

  useEffect(() => {
    const initRazorpay = async () => {
      await loadRazorpayScript()
    }
    if (!loadRazorpay.current) {
      loadRazorpay.current = true
      initRazorpay()
    }
  }, [])

  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width,initial-scale=1" />
        <meta name="mobile-web-app-capable" content="yes" />
        {/* <script src="https://checkout.razorpay.com/v1/checkout.js"></script> */}
        <Meta />
        <Links />
      </head>
      <body>
        <script
          async
          src="https://www.googletagmanager.com/gtag/js?id=G-WGP9BQGHQB"
        ></script>
        <script
          async
          id="gtag-init"
          dangerouslySetInnerHTML={{
            __html: `
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());

          gtag('config', 'G-WGP9BQGHQB', {
            page_path: window.location.pathname
          });
        `,
          }}
        />
        <QueryClientProvider client={queryClient}>
          <Outlet />
        </QueryClientProvider>
        <Toaster
          toastOptions={{
            success: {
              duration: 4000,
              icon: <IconCheck className="text-green-500" />,
            },
            error: {
              duration: 4000,
              icon: <IconExclamation className="text-destructive" />,
            },
          }}
        />
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  )
}

export function ErrorBoundary() {
  const error = useRouteError()

  console.log("error boundary", error)

  if (isRouteErrorResponse(error)) {
    return (
      <html lang="en">
        <head>
          <title>Oh no! Triket has run into an error</title>
          <Meta />
          <Links />
        </head>
        <body>
          <ErrorBoundaryLayout>
            <div className="flex h-96 flex-col items-center justify-center gap-y-4">
              <h1 className="text-center text-4xl font-bold">
                {error.status === 404
                  ? `${error.status} Page ${error.statusText}`
                  : `${error.status} ${error.statusText}`}
              </h1>
              <NavLink className="underline" to="/">
                Go Home.
              </NavLink>
            </div>
          </ErrorBoundaryLayout>
          <Scripts />
        </body>
      </html>
    )
  } else if (error instanceof Error) {
    // console.log("error", error)
    return (
      <html lang="en">
        <head>
          <title>Oh no! Triket has run into an error</title>
          <Meta />
          <Links />
        </head>
        <body>
          <div className="flex h-full min-h-screen flex-col bg-white">
            <main className="grow">
              <div className="flex h-96 flex-col items-center justify-center gap-y-4">
                {error.message.includes("status code 503") ? (
                  <div className="flex flex-col items-center">
                    <img src="/logo.png" alt="logo" className="w-24" />
                    <div className="mt-16 text-center text-2xl font-bold">
                      Website is currently under maintenance.
                    </div>
                    <div className="text-center text-2xl font-bold">
                      Please come back later.
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-col items-center">
                    <img src="/logo.png" alt="logo" className="w-24" />
                    <h1 className="mt-16 text-center text-4xl font-bold">
                      Application Error
                    </h1>
                  </div>
                )}
              </div>
            </main>
            <footer className="mx-auto h-auto w-full px-2 pb-16 pt-8">
              <div className="mt-16 flex flex-col items-center">
                <span>A product of </span>
                <img src="/arsi_logo.svg" alt="Arsi logo" className="size-12" />
                <a
                  href="https://arsi.in"
                  target="_blank"
                  rel="noreferrer noopener"
                  className="hover:underline"
                >
                  Arsi Consultancy
                </a>
              </div>
            </footer>
          </div>
          <Scripts />
        </body>
      </html>
    )
  } else {
    return (
      <html lang="en">
        <head>
          <title>Oh no! Triket has run into an error</title>
          <Meta />
          <Links />
        </head>
        <body>
          <ErrorBoundaryLayout>
            <div className="flex h-96 flex-col items-center justify-center gap-y-4">
              <ErrorComponent />
            </div>
          </ErrorBoundaryLayout>
          <Scripts />
        </body>
      </html>
    )
  }
}
