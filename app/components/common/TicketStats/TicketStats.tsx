import { Rupee } from ".."

import { Loader } from "@components/ui"
import { graphql } from "@/__generated__"
import { useQuery } from "@tanstack/react-query"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"

const TICKET_STATS = graphql(`
  query TicketStats($eventID: ID!) {
    getTicketStatsForEvent(event_id: $eventID) {
      total_amount
      total_tip_amount
      total_tickets_sold
      ticket_type_stats {
        num_of_tickets
        ticket_type
      }
    }
  }
`)

interface Props {
  eventId: string
}

const TicketStats = ({ eventId }: Props) => {
  const graphql = getGraphqlClient()

  const { isLoading, isError, data } = useQuery({
    queryKey: ["ticket-stats", eventId],
    queryFn: async () => {
      return await graphql.request(TICKET_STATS, {
        eventID: eventId,
      })
    },
  })

  if (isLoading) {
    return <Loader />
  }

  if (isError) {
    return <p className="text-red-500">Error fetching ticket stats</p>
  }

  const ticketsSold =
    data?.getTicketStatsForEvent?.ticket_type_stats?.filter(
      (item) =>
        item?.ticket_type !== "special_invitee" &&
        item?.ticket_type !== "default"
    ) || []

  return (
    <div className="flex flex-col">
      <p>
        <span>Total tickets sold : </span>
        {data?.getTicketStatsForEvent?.total_tickets_sold || 0}
      </p>
      <p>
        <span>Total Amount : </span> <Rupee />{" "}
        {data?.getTicketStatsForEvent?.total_amount?.toFixed(2) || 0}
      </p>
      <p>
        <span>Total tip amount : </span> <Rupee />{" "}
        {data?.getTicketStatsForEvent?.total_tip_amount?.toFixed(2) || 0}
      </p>
      {ticketsSold.length ? (
        <div className="mt-4">
          <p className="font-bold">Number of tickets sold</p>
          {ticketsSold.map((item) => {
            return (
              <p key={item?.ticket_type}>
                <span>{item?.ticket_type} </span> : {item?.num_of_tickets}
              </p>
            )
          })}
        </div>
      ) : null}
    </div>
  )
}

export default TicketStats
