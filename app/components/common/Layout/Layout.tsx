import type { ReactNode } from "react"

import Footer from "../Footer"
import Header from "../Header"
import <PERSON><PERSON><PERSON>yLoader from "@/components/common/razorpay-loader"

interface Props {
  children: ReactNode
}

const Layout = ({ children }: Props) => {
  return (
    <div className="flex h-full min-h-screen flex-col bg-white">
      <Header />
      <main className="grow">{children}</main>
      <Footer />
      <RazorpayLoader />
    </div>
  )
}

export default Layout
