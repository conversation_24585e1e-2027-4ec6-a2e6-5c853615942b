import type { ReactNode } from "react"

import Footer from "../Footer"
import { NavLink } from "react-router"

interface Props {
  children: ReactNode
}

const ErrorBoundaryLayout = ({ children }: Props) => {
  return (
    <div className="flex h-full min-h-screen flex-col bg-white">
      <header className="relative mx-auto flex h-[8vh] w-full items-center justify-between border-black px-2 py-10 md:p-10">
        <NavLink to="/" className="text-4xl font-bold">
          <img src="/logo.png" alt="logo" className="w-24" />
        </NavLink>
      </header>
      <main className="grow">{children}</main>
      <Footer />
    </div>
  )
}

export default ErrorBoundaryLayout
