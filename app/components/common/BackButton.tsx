import { useNavigate } from "react-router";

import { IconLeft } from "@components/icons"

interface Props {
  onClick?: () => void
}

const BackButton = ({ onClick }: Props) => {
  const navigate = useNavigate()
  return (
    <button type="button" onClick={() => {
      if (onClick) {
        onClick()
      } else {
        navigate(-1)
      }
    }}>
      <IconLeft className="text-2xl" />
    </button>
  )
}

export default BackButton
