import { NavLink } from "react-router";
import clsx from "clsx"
import type { Dispatch, SetStateAction } from "react"

import {
  Sheet,
  SheetClose,
  Sheet<PERSON>ontent,
  She<PERSON><PERSON>eader,
  SheetTrigger,
} from "@/components/ui/sheet"
import { IconMenu, IconX } from "@/components/icons"
import { Button } from "@/components/ui/button"

interface Props {
  open: boolean
  setOpen: Dispatch<SetStateAction<boolean>>
  handleLogout: () => void
  loading: boolean
}

const StaffMenu = ({ open, setOpen, handleLogout, loading }: Props) => {
  const closeAdminMenu = () => {
    setOpen(false)
  }

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger>
        <IconMenu className="text-4xl" />
      </SheetTrigger>
      <SheetContent side="right" className="px-0">
        <SheetHeader>
          <NavLink to="/" className="mb-4 w-32 px-2 text-4xl font-bold">
            <img src="/logo.png" alt="logo" className="w-24" />
          </NavLink>
          <SheetClose
            className="absolute right-4 top-4"
            onClick={closeAdminMenu}
          >
            <IconX />
          </SheetClose>
        </SheetHeader>
        <nav>
          <ul>
            <li>
              <NavLink
                to="/"
                className={({ isActive }) =>
                  clsx("flex w-full p-2 hover:bg-slate-100", {
                    "bg-slate-100": isActive,
                  })
                }
                onClick={closeAdminMenu}
              >
                Home
              </NavLink>
            </li>
            <li>
              <NavLink
                to="/staff"
                className={({ isActive }) =>
                  clsx("flex w-full p-2 hover:bg-slate-100", {
                    "bg-slate-100": isActive,
                  })
                }
                onClick={closeAdminMenu}
              >
                Staff
              </NavLink>
            </li>
            <li>
              <NavLink
                to="/scan-ticket"
                className={({ isActive }) =>
                  clsx("flex w-full p-2 hover:bg-gray-100", {
                    "bg-slate-100": isActive,
                  })
                }
                onClick={closeAdminMenu}
              >
                Scan Ticket
              </NavLink>
            </li>
            <li>
              <Button
                variant="ghost"
                className="px-2 text-base font-normal text-black"
                isLoading={loading}
                onClick={handleLogout}
              >
                Logout
              </Button>
            </li>
          </ul>
        </nav>
      </SheetContent>
    </Sheet>
  )
}

export default StaffMenu
