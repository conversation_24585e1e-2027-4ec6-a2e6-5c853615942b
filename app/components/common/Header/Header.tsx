import { NavLink, useFetcher } from "react-router";
import { useState } from "react"

import AdminMenu from "./AdminMenu"
import StaffMenu from "./StaffMenu"

import useLogout from "@/lib/hooks/use-logout"

interface Props {
  role?: string
}

const Header = ({ role }: Props) => {

  const [adminMenu, setAdminMenu] = useState(false)
  const [staffMenu, setStaffMenu] = useState(false)

  const { logoutUser } = useLogout()
  const fetcher = useFetcher()

  const handleLogout = () => {
    logoutUser.mutate(undefined, {
      onSuccess: () => {
        fetcher.submit(null, {
          action: "/logout",
          method: "POST",
        })
      },
      onError: () => {
        fetcher.submit(null, {
          action: "/logout",
          method: "POST",
        })
      },
    })
  }

  return (
    <>
      <header className="relative mx-auto flex h-[8vh] w-full items-center justify-between border-black px-2 py-10 md:p-10">
        <NavLink to="/" className="text-4xl font-bold">
          <img src="/logo.png" alt="logo" className="w-24" />
        </NavLink>
        {role === "admin" ? (
          <AdminMenu
            loading={logoutUser.isPending}
            handleLogout={handleLogout}
            open={adminMenu}
            setOpen={setAdminMenu}
          />
        ) : null}
        {role === "staff" ? (
          <StaffMenu
            loading={logoutUser.isPending}
            handleLogout={handleLogout}
            open={staffMenu}
            setOpen={setStaffMenu}
          />
        ) : null}
        {/* {role !== "admin" && role !== "staff" && displayHeader ? ( */}
        {/*   <NavLink to="/login">Login</NavLink> */}
        {/* ) : null} */}
      </header>
    </>
  )
}

export default Header
