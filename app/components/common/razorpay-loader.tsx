import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogOverlay,
  DialogTitle,
} from "@/components/ui/dialog"
import razorpayLoaderStore from "@/lib/store/razorpay-loader-store"
import { Loader2 } from "lucide-react"

const RazorpayLoader = () => {
  const { loading } = razorpayLoaderStore()
  return (
    <Dialog open={loading}>
      <DialogOverlay className="bg-gray-800" />
      <DialogContent>
        <img alt="triket-logo" className="mx-auto w-1/2" src="/logo.png" />
        <DialogHeader>
          <DialogTitle className="text-center">Please Wait!</DialogTitle>
          <DialogDescription className="text-center">
            Processing payment... Please do not refresh the page.
          </DialogDescription>
        </DialogHeader>
        <Loader2 className="mx-auto size-6 animate-spin" />
      </DialogContent>
    </Dialog>
  )
}

export default Ra<PERSON>payLoader
// export const handleRazorpay = ({
//   orderId,
//   price,
//   contact,
//   name,
//   resetStates,
// }: Props) => {
//   $razorpayLoading.set(true)
//   const rzpAmount = price * 100
//   const options = {
//     key: rzpayKey,
//
//     amount: rzpAmount, // in currency subunits. Here 1000 = 1000 paise, which equals to ╬ô├⌐Γòú10
//     currency: "INR", // Default is INR. We support more than 90 currencies.
//     image: "/logo_black.png",
//     name: "AIJAL CLUB",
//     order_id: orderId, // Replace with Order ID generated in Step 4
//     theme: {
//       color: "#10172a",
//     },
//     prefill: {
//       contact: contact || "",
//       name: name || "",
//     },
//     handler: function (response: RazorpayResponse) {
//       // for loading state
//       $razorpayLoading.set(true)
//       let count = 0
//       const intervalId = setInterval(async () => {
//         try {
//           const paymentDetail = await graphqlClient.request(GetPaymentDetail, {
//             orderId: orderId,
//           })
//
//           if (paymentDetail.getPaymentDetail.status === "captured") {
//             clearInterval(intervalId)
//             // resetStates && resetStates()
//             // $razorpayLoading.set(false)
//             navigate(
//               `/payment-success?orderId=${response.razorpay_order_id}`
//             ).then(() => {
//               resetStates && resetStates()
//               $razorpayLoading.set(false)
//             })
//           } else if (paymentDetail.getPaymentDetail.status === "failed") {
//             clearInterval(intervalId)
//             // resetStates && resetStates()
//             // $razorpayLoading.set(false)
//             navigate("/payment-failed").then(() => {
//               resetStates && resetStates()
//               $razorpayLoading.set(false)
//             })
//           } else {
//             count += 1
//           }
//
//           if (count >= RAZORPAY_POLLING_COUNT) {
//             clearInterval(intervalId)
//             // resetStates && resetStates()
//             // $razorpayLoading.set(false)
//             // navigate("/payment-failed")
//             navigate("/payment-failed").then(() => {
//               resetStates && resetStates()
//               $razorpayLoading.set(false)
//             })
//           }
//         } catch (error) {
//           console.error(error)
//           clearInterval(intervalId)
//           resetStates && resetStates()
//           $razorpayLoading.set(false)
//           throw new Error("There was an error processing your payment.")
//         }
//       }, 2000)
//     },
//
//     method: "upi",
//     apps: ["google_pay", "phonepe", "bhim", "paytm", "sbi"],
//   }
//
//   const razorpay = new (window as any).Razorpay(options)
//
//   razorpay.once("ready", function () {
//     $razorpayLoading.set(false)
//     razorpay.open()
//   })
//
//   razorpay.on("payment.failed", function (response: any) {
//     console.log("rzpay payment failed", response)
//     razorpay.close()
//     // navigate("/payment-failure")
//   })
//   razorpay.on("payment.error", function (response: any) {
//     console.log("rzpay payment error", response)
//     razorpay.close()
//     // navigate("/payment-failure")
//   })
// }
