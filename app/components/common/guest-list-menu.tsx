import { IconVertical, IconDownload } from "../icons"

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button, buttonVariants } from "@/components/ui/button"
import { cn } from "@/lib/utils/shadcnUtil"

interface Props {
  downloadUrl?: string
  tipAmount?: number
  tipMessage?: string
  handleTips?: ({
    tipMessage,
    tipAmount,
  }: {
    tipMessage: string
    tipAmount: number
  }) => void
}

const GuestListMenu = ({
  downloadUrl,
  tipAmount,
  tipMessage,
  handleTips,
}: Props) => {
  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger>
          <IconVertical className="text-slate-500" />
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuItem asChild>
            <a
              href={downloadUrl}
              className={cn(buttonVariants({ variant: "link" }), "w-full")}
              target="_blank"
              rel="noreferrer"
            >
              <IconDownload className="mr-2 size-4 text-green-700" /> Download
              ticket
            </a>
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <Button
              variant="link"
              className="w-full"
              onClick={() => {
                if (tipAmount && handleTips) {
                  handleTips({
                    tipAmount,
                    tipMessage: tipMessage || "",
                  })
                }
              }}
              disabled={!tipAmount}
            >
              Tip
            </Button>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  )
}

export default GuestListMenu
