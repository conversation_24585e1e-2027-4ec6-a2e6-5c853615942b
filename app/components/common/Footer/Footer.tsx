import { NavLink } from "react-router";

import IconInstagram from "@components/icons/IconInstagram"
import IconTwitter from "@components/icons/IconTwitter"

const Footer = () => {
  return (
    <footer className="mx-auto h-auto w-full bg-pallet-green px-2 pb-16 pt-8 text-white">
      <div className="mx-auto flex max-w-6xl flex-col gap-y-8">
        <div className="col-span-12 flex flex-col px-4 text-center md:col-span-4">
          <span className="text-4xl font-bold">triket</span>
          <div className="my-2 flex justify-center gap-x-3">
            {/* <IconFb className="text-2xl" /> */}
            <a
              href="https://twitter.com/triket_in"
              target="_blank"
              rel="noreferrer noopener"
              aria-label="Triket twitter account link"
            >
              <IconTwitter className="text-2xl" />
            </a>
            <a
              href="https://instagram.com/triket.in"
              target="_blank"
              rel="noreferrer noopener"
              aria-label="Triket instagram account link"
            >
              <IconInstagram className="text-2xl" />
            </a>
          </div>
        </div>
        <div className="col-span-12 grid grid-cols-12 gap-y-8 md:col-span-8">
          <div className="col-span-12 flex flex-col items-center md:col-span-4">
            <div className="flex flex-col items-center md:items-start">
              <h3 className="font-bold">ABOUT US</h3>
              <NavLink to="/who-we-are" className="mt-4 hover:underline">
                Who we are
              </NavLink>
              <h3 className="mt-4 font-bold">CONTACT US</h3>
              <div className="flex flex-col items-center text-sm md:items-start">
                <a className="hover:underline" href="tel:+************">
                  +************
                </a>
                <a className="hover:underline" href="mailto: <EMAIL>">
                  <EMAIL>
                </a>
                <h3 className="mt-4 font-bold">ADDRESS</h3>
                <div className="flex flex-col">
                  <p>T-14 Bungkawn,</p>
                  <p>Dam veng,</p>
                  <p>Aizawl, Mizoram</p>
                  <p>Pin - 796001</p>
                </div>
              </div>
            </div>
          </div>
          <div className="col-span-12 flex flex-col items-center md:col-span-4">
            <div className="flex flex-col items-center md:items-start">
              <h3 className="font-bold">OUR PARTNERS</h3>
              <a
                href="https://www.instagram.com/eighteen.eleven_aizawl"
                target="_blank"
                rel="noreferrer noopener"
                className="mt-4 hover:underline"
              >
                18:11
              </a>
              <div>Rûn Design Studio</div>
              <a
                href="https://www.instagram.com/audioverse.in"
                target="_blank"
                rel="noreferrer noopener"
                className="hover:underline"
              >
                Audioverse
              </a>
              <a
                href="https://www.instagram.com/bbyanimalsinc"
                target="_blank"
                rel="noreferrer noopener"
                className="hover:underline"
              >
                Bbyanimals
              </a>
              <a
                href="https://www.instagram.com/drinkallnatural"
                target="_blank"
                rel="noreferrer noopener"
                className="hover:underline"
              >
                LOCAL All Natural Soda
              </a>
            </div>
          </div>
          <div className="col-span-12 flex flex-col items-center md:col-span-4 ">
            <div className="flex flex-col items-center md:items-start">
              <h3 className="font-bold">HELPFUL LINKS</h3>
              <NavLink
                className="mt-4 hover:underline"
                to="/terms-and-conditions"
              >
                Terms of use
              </NavLink>

              <NavLink
                to="/terms-and-conditions#shipping-policy"
                className="hover:underline"
              >
                Shipping policy
              </NavLink>
              <NavLink
                to="/terms-and-conditions#privacy-policy"
                className="hover:underline"
              >
                Privacy policy
              </NavLink>
              <NavLink to="/request-refund" className="hover:underline">
                Request refund
              </NavLink>
              <NavLink to="/get-started" className="hover:underline">
                Create an event
              </NavLink>
              <NavLink to="/login" className="hover:underline">
                Admin login
              </NavLink>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-16 flex flex-col items-center">
        <span>A product of </span>
        <img src="/arsi_logo.png" alt="Arsi logo" className="size-12" />
        <a
          href="https://arsi.in"
          target="_blank"
          rel="noreferrer noopener"
          className="hover:underline"
        >
          Arsi Consultancy
        </a>
      </div>
    </footer>
  )
}

export default Footer
