import { useEffect, useState } from "react"

import { cn } from "@/lib/utils/shadcnUtil"
import { decodeBlurhashSSR } from "@/lib/utils"

interface Props {
  src: string
  alt: string
  hash?: string
  className?: string
  objectFit: "contain" | "cover"
  variant?: "normal" | "carousel"
  rounded?: "none" | "full"
}

const Blurimage = ({
  src,
  alt,
  hash,
  className,
  variant = "normal",
  objectFit = "cover",
  rounded = "none",
}: Props) => {
  const [loaded, setLoaded] = useState(false)

  useEffect(() => {
    const img = new Image()
    img.src = src
    img.onload = () => {
      setLoaded(true)
    }
  }, [src])

  return (
    <div
      className={cn("h-full w-full rounded-none object-cover", {
        "relative overflow-hidden": variant === "normal",
        "rounded-full": rounded === "full",
        className,
      })}
    >
      <div
        suppressHydrationWarning={true}
        className={cn(
          "h-full w-full rounded-none blur-lg transition-opacity duration-500 ease-in-out",
          {
            "opacity-0": loaded,
            "absolute inset-0": variant === "normal",
          }
        )}
        style={{
          backgroundImage: `url(${decodeBlurhashSSR(hash)})`,
          backgroundSize: "cover",
          backgroundRepeat: "no-repeat",
        }}
      />
      <img
        className={cn("h-full w-full rounded-none transition ease-in-out", {
          "opacity-100 blur-none": loaded,
          "opacity-0 blur-lg": !loaded,
          "absolute inset-0": variant === "normal",
          "object-contain": objectFit === "contain",
          "object-cover": objectFit === "cover",
          "rounded-full": rounded === "full",
        })}
        src={src}
        alt={alt}
        style={{
          transitionProperty: "opacity, filter",
          transitionDuration: "500ms, 1500ms",
        }}
        loading="lazy"
      />
    </div>
  )
}

export default Blurimage
