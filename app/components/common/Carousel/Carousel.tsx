import { useState, type FC, useCallback, useEffect } from "react"
import useEmblaCarousel, {
  type UseEmblaCarouselType,
} from "embla-carousel-react"

type CarouselApi = UseEmblaCarouselType[1]

import { CarouselDots } from "."

import { baseUrl } from "@lib/utils"
import type { AppImage } from "@/__generated__/graphql"

interface Props {
  data: AppImage[]
}

const Carousel: FC<Props> = ({ data }) => {
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true })
  const [scrollSnaps, setScrollSnaps] = useState<number[]>([])

  const scrollTo = useCallback(
    (index: number) => emblaApi && emblaApi.scrollTo(index),
    [emblaApi]
  )

  const onSelect = useCallback((emblaApi: CarouselApi) => {
    emblaApi && setSelectedIndex(emblaApi.selectedScrollSnap())
  }, [])

  const onInit = useCallback((emblaApi: CarouselApi) => {
    emblaApi && setScrollSnaps(emblaApi.scrollSnapList())
  }, [])

  useEffect(() => {
    if (!emblaApi) return

    onInit(emblaApi)
    emblaApi.on("reInit", onInit)
    emblaApi.on("reInit", onSelect)
    emblaApi.on("select", onSelect)
  }, [emblaApi, onInit, onSelect])

  return (
    <>
      <div className="overflow-hidden" ref={emblaRef}>
        <div className="relative flex w-full">
          {data.map((item) => {
            return (
              <div
                key={item.id}
                className=" text-primary-black relative flex w-full shrink-0 grow-0 basis-full flex-col items-center"
              >
                <img
                  className="z-10 w-full object-contain md:w-2/3"
                  alt={`${item.id}`}
                  src={`${baseUrl}/image/medium/${item.path}`}
                />
              </div>
            )
          })}
        </div>
      </div>
      {scrollSnaps.length > 1 ? (
        <div className="absolute bottom-2 left-0 right-0 flex w-full items-center justify-center gap-x-1">
          {scrollSnaps.map((_, index) => {
            return (
              <CarouselDots
                key={index}
                onClick={() => scrollTo(index)}
                selected={index === selectedIndex}
              />
            )
          })}
        </div>
      ) : null}
    </>
  )
}

export default Carousel
