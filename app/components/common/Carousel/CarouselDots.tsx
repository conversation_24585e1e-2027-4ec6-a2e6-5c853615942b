import clsx from "clsx"
import type { FC } from "react"

interface Props {
  selected: boolean
  onClick: () => void
}

const CarouselDots: FC<Props> = ({ selected, onClick }) => {
  return (
    <button
      className={clsx(
        "flex size-3 items-center rounded-full border border-black",
        {
          "bg-black": selected,
          "border-black bg-white": !selected,
        }
      )}
      type="button"
      onClick={onClick}
    />
  )
}

export default CarouselDots
