import type { <PERSON> } from "react"

interface Props {
  errorMessage?: string
}

const ErrorComponent: FC<Props> = ({ errorMessage }) => {
  return (
    <div className="flex flex-col">
      <h2 className="text-center text-4xl font-semibold">Error</h2>
      <p className="text-center text-lg">
        {errorMessage ?? "Sorry, an error has occurred"}
      </p>
    </div>
  )
}

export default ErrorComponent
