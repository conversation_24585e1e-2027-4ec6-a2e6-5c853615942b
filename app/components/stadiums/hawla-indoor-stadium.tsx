import { IconArrowLeft, IconArrowRight } from "@/components/icons/arrow-icons"
import { cn } from "@/lib/utils/shadcnUtil"

interface Props {
  onClick: (value: string) => void
  selectedType?: string
}

interface ButtonProps {
  onClick: () => void
  selectedType?: string
  rotate?: boolean
  className?: string
  index?: number
}

// const GeneralButton = ({
//   onClick,
//   selectedType,
//   rotate = false,
//   className,
//   index,
// }: ButtonProps) => {
//   return (
//     <button
//       className={cn(
//         "col-span-1 flex items-center justify-center bg-pallet-green/50",
//         {
//           "bg-pallet-green ring-2 ring-pallet-green/50":
//             selectedType === `general-${index}`,
//         },
//         className
//       )}
//       onClick={onClick}
//       type="button"
//     >
//       <span
//         className={cn("text-center", {
//           "-rotate-90": rotate,
//         })}
//       >
//         General (Gallery)
//       </span>
//     </button>
//   )
// }

const AwayButton = ({
  onClick,
  selectedType,
  rotate = false,
  className,
  index,
}: ButtonProps) => {
  return (
    <button
      className={cn(
        "col-span-1 flex h-12 w-full items-center justify-center bg-blue-500/50",
        {
          "bg-blue-700 ring-2": selectedType === `away-end-${index}`,
          "h-32": rotate,
        },
        className
      )}
      onClick={onClick}
      type="button"
    >
      <span
        className={cn("text-center", {
          "-rotate-90": rotate,
        })}
      >
        Away End {index}
      </span>
    </button>
  )
}

const HomeButton = ({
  onClick,
  selectedType,
  rotate = false,
  className,
  index,
}: ButtonProps) => {
  return (
    <button
      className={cn(
        "col-span-1 flex h-12 w-full items-center justify-center bg-pallet-red/50",
        {
          "bg-pallet-red ring-2 ring-pallet-red/50":
            selectedType === `home-end-${index}`,
          "h-32": rotate,
        },
        className
      )}
      onClick={onClick}
      type="button"
    >
      <span
        className={cn("text-center", {
          "rotate-90": rotate,
        })}
      >
        Home End {index}
      </span>
    </button>
  )
}

const HawlaIndoorStadium = ({ onClick, selectedType }: Props) => {
  return (
    <div className="grid grid-cols-9 gap-1 text-xs font-bold text-white">
      {/* first line general x 2 */}
      <div className="col-span-2" />
      <div className="col-span-2 flex items-center justify-center gap-x-1 text-black">
        89 <IconArrowRight /> 101
      </div>
      <div className="col-span-1" />
      <div className="col-span-2 flex items-center justify-center gap-x-1 text-black">
        102 <IconArrowRight /> 113
      </div>
      <div className="col-span-1" />

      {/* second line general x 2 */}
      <div className="col-span-2 h-12" />
      <AwayButton
        className="col-span-2"
        onClick={() => onClick("away-end-1")}
        selectedType={selectedType}
        index={1}
      />
      <div className="col-span-1 flex items-center justify-center bg-pallet-yellow px-2 py-1 text-center text-black">
        Tech Area
      </div>
      <HomeButton
        className="col-span-2"
        onClick={() => onClick("home-end-1")}
        selectedType={selectedType}
        index={1}
      />
      <div className="col-span-2" />

      {/* third and fourth line */}
      {/* <GeneralButton */}
      {/*   onClick={() => onClick("general-2")} */}
      {/*   selectedType={selectedType} */}
      {/*   className="col-span-1 row-span-2" */}
      {/*   rotate={true} */}
      {/*   index={2} */}
      {/* /> */}
      <div className="col-span-1 row-span-2">
        <div
          className={cn(
            "flex h-32 w-full items-center justify-center text-black"
          )}
        >
          <div className="flex -rotate-90 gap-x-4 pt-4">
            <span className="flex items-center gap-x-1 ">
              71 <IconArrowRight /> 88
            </span>
            <span className="grow basis-full" />
            <span className="flex items-center gap-x-1">
              55 <IconArrowRight /> 70
            </span>
          </div>
        </div>
      </div>

      <div className="col-span-1 row-span-2">
        <AwayButton
          onClick={() => onClick("away-end-2")}
          selectedType={selectedType}
          index={2}
          rotate={true}
        />
      </div>
      <div className="col-span-5 row-span-2 flex h-32 items-center bg-gray-500">
        <img alt="hawla-indoor-stadium" src="/hawla_indoor_stadium.png" />
      </div>
      <HomeButton
        className="col-span-1 row-span-2"
        onClick={() => onClick("home-end-2")}
        selectedType={selectedType}
        index={2}
        rotate={true}
      />
      <div className="col-span-1 row-span-2">
        <div
          className={cn(
            "flex h-32 w-full items-center justify-center text-black"
          )}
        >
          <div className="flex rotate-90 gap-x-4 pt-4">
            <span className="flex items-center gap-x-1">
              114 <IconArrowRight /> 131
            </span>
            <span className="grow basis-full" />
            <span className="flex items-center gap-x-1">
              132 <IconArrowRight /> 147
            </span>
          </div>
        </div>
      </div>
      {/* <GeneralButton */}
      {/*   onClick={() => onClick("general-3")} */}
      {/*   selectedType={selectedType} */}
      {/*   rotate={true} */}
      {/*   className="col-span-1 row-span-2" */}
      {/*   index={3} */}
      {/* /> */}

      {/* fifth line */}
      <div className="col-span-2 h-12" />
      <div className="col-span-5 grid grid-cols-4 gap-1">
        <AwayButton
          className="col-span-2 row-span-2 "
          onClick={() => onClick("away-end-3")}
          selectedType={selectedType}
          index={3}
        />
        <HomeButton
          className="col-span-2 row-span-2"
          onClick={() => onClick("home-end-3")}
          selectedType={selectedType}
          index={3}
        />
      </div>
      <div className="col-span-2" />

      {/* sixth line */}
      <div className="col-span-2 h-12" />
      <div className="col-span-5 grid grid-cols-4 items-start gap-1">
        <div className="col-span-2 flex items-center justify-center gap-x-1 text-black">
          54 <IconArrowLeft /> 28
        </div>
        <div className="col-span-2 flex items-center justify-center gap-x-1 text-black">
          27 <IconArrowLeft /> 1
        </div>
      </div>
      <div className="col-span-2" />
    </div>
  )
}

export default HawlaIndoorStadium
