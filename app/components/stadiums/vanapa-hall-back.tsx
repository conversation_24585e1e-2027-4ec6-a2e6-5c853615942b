import { CheckboxGroup, CheckboxGroupItem } from "../ui/checkbox"

const VanapaHallBack = () => {
  return (
    <>
      {/* B187 - 206 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index < 2 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`B${index + 185}`}
              >
                B{index + 185}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index > 9 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`B${index + 197}`}
              >
                B{index + 197}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>

      {/* B167 - 186 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index < 2 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`B${index + 165}`}
              >
                B{index + 165}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index > 9 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`B${index + 177}`}
              >
                B{index + 177}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>

      {/* B145 - 166 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index < 1 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`B${index + 144}`}
              >
                B{index + 144}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index > 10 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`B${index + 156}`}
              >
                B{index + 156}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>

      {/* B121 - 144 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => (
            <CheckboxGroupItem
              key={index + 1}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
              value={`B${index + 121}`}
            >
              B{index + 121}
            </CheckboxGroupItem>
          ))}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => (
            <CheckboxGroupItem
              key={index + 1}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
              value={`B${index + 133}`}
            >
              B{index + 133}
            </CheckboxGroupItem>
          ))}
        </CheckboxGroup>
      </div>

      {/* B97 - 120 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => (
            <CheckboxGroupItem
              key={index + 1}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
              value={`B${index + 97}`}
            >
              B{index + 97}
            </CheckboxGroupItem>
          ))}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => (
            <CheckboxGroupItem
              key={index + 1}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
              value={`B${index + 109}`}
            >
              B{index + 109}
            </CheckboxGroupItem>
          ))}
        </CheckboxGroup>
      </div>

      {/* B73 - 96 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => (
            <CheckboxGroupItem
              key={index + 1}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
              value={`B${index + 73}`}
            >
              B{index + 73}
            </CheckboxGroupItem>
          ))}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => (
            <CheckboxGroupItem
              key={index + 1}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
              value={`B${index + 85}`}
            >
              B{index + 85}
            </CheckboxGroupItem>
          ))}
        </CheckboxGroup>
      </div>

      {/* B49 - 72 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => (
            <CheckboxGroupItem
              key={index + 1}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
              value={`B${index + 49}`}
            >
              B{index + 49}
            </CheckboxGroupItem>
          ))}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => (
            <CheckboxGroupItem
              key={index + 1}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
              value={`B${index + 61}`}
            >
              B{index + 61}
            </CheckboxGroupItem>
          ))}
        </CheckboxGroup>
      </div>

      {/* B25 - 48 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => (
            <CheckboxGroupItem
              key={index + 1}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
              value={`B${index + 25}`}
            >
              B{index + 25}
            </CheckboxGroupItem>
          ))}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => (
            <CheckboxGroupItem
              key={index + 1}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
              value={`B${index + 37}`}
            >
              B{index + 37}
            </CheckboxGroupItem>
          ))}
        </CheckboxGroup>
      </div>

      {/* B1 - 24 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => (
            <CheckboxGroupItem
              key={index + 1}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
              value={`B${index + 1}`}
            >
              B{index + 1}
            </CheckboxGroupItem>
          ))}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => (
            <CheckboxGroupItem
              key={index + 1}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
              value={`B${index + 13}`}
            >
              B{index + 13}
            </CheckboxGroupItem>
          ))}
        </CheckboxGroup>
      </div>
    </>
  )
}

export default VanapaHallBack
