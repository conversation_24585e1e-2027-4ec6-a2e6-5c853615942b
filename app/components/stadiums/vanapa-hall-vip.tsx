import { CheckboxGroup, CheckboxGroupItem } from "../ui/checkbox"

const VanapaHallVip = () => {
  return (
    <>
      {/* V15 - 30 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index < 3 || index > 10 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`V${index + 12}`}
              >
                V{index + 12}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index < 1 || index > 8 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`V${index + 22}`}
              >
                V{index + 22}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>

      {/* V1 - 14 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index < 4 || index > 10 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`V${index - 3}`}
              >
                V{index - 3}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index < 1 || index > 7 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`V${index + 7}`}
              >
                V{index + 7}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>
    </>
  )
}

export default VanapaHallVip
