import { CheckboxGroup, CheckboxGroupItem } from "../ui/checkbox"

const VanapaHallFront = () => {
  return (
    <>
      {/* F152-175 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => (
            <CheckboxGroupItem
              key={index + 1}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
              value={`F${index + 152}`}
            >
              F{index + 152}
            </CheckboxGroupItem>
          ))}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => (
            <CheckboxGroupItem
              key={index + 1}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
              value={`F${index + 164}`}
            >
              F{index + 164}
            </CheckboxGroupItem>
          ))}
        </CheckboxGroup>
      </div>

      {/* F128 - 151 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => (
            <CheckboxGroupItem
              key={index + 1}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
              value={`F${index + 128}`}
            >
              F{index + 128}
            </CheckboxGroupItem>
          ))}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => (
            <CheckboxGroupItem
              key={index + 1}
              variant="seat"
              className="col-span-1 rounded-lg text-[0.5rem]"
              value={`F${index + 140}`}
            >
              F{index + 140}
            </CheckboxGroupItem>
          ))}
        </CheckboxGroup>
      </div>

      {/* F106 - 127 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index < 1 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`F${index + 105}`}
              >
                F{index + 105}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index > 10 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`F${index + 117}`}
              >
                F{index + 117}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>

      {/* F84 - 105 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index < 1 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`F${index + 83}`}
              >
                F{index + 83}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index > 10 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`F${index + 95}`}
              >
                F{index + 95}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>

      {/* F62 - 83 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index < 1 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`F${index + 61}`}
              >
                F{index + 61}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index > 10 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`F${index + 73}`}
              >
                F{index + 73}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>

      {/* F41 - 82 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index < 2 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`F${index + 39}`}
              >
                F{index + 39}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index > 10 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`F${index + 51}`}
              >
                F{index + 51}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>

      {/* F21 - 40 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index < 2 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`F${index + 19}`}
              >
                F{index + 19}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index > 9 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`F${index + 31}`}
              >
                F{index + 31}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>

      {/* F1 - 20 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index < 2 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`F${index - 1}`}
              >
                F{index - 1}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) =>
            index > 9 ? (
              <div key={index} className="col-span-1" />
            ) : (
              <CheckboxGroupItem
                key={index + 1}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={`F${index + 11}`}
              >
                F{index + 11}
              </CheckboxGroupItem>
            )
          )}
        </CheckboxGroup>
      </div>
    </>
  )
}

export default VanapaHallFront
