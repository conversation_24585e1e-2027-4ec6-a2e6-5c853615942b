import type { ButtonHTMLAttributes, FC, ReactNode } from "react"
import Loader from "../Loader"

interface Props extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "primary" | "no-border"
  loaderVariant?: "small" | "large" | "smallWhite"
  size?: "sm" | "md" | "lg"
  children: ReactNode
  isLoading?: boolean
  imageSrc?: string
}

const ImageButton: FC<Props> = ({
  children,
  variant = "primary",
  loaderVariant,
  size = "md",
  isLoading,
  imageSrc,
  ...rest
}) => {
  return (
    <button {...rest}>
      {isLoading ? (
        <Loader variant={loaderVariant ?? "small"} />
      ) : (
        <div className="relative h-full w-full">
          <span className="absolute font-bold text-xl w-full h-full flex justify-center items-center">
            {children}
          </span>
          <img className="w-32" src={imageSrc} alt="button" />
        </div>
      )}
    </button>
  )
}

export default ImageButton
