import clsx from "clsx"
import type { FC } from "react"

interface LoaderProps {
  variant?: "small" | "large" | "smallWhite"
  className?: string | undefined
}

/**
 * Css loader, variant small uses a circular indicator
 * while large is three pulsating blocks
 */
const Loader: FC<LoaderProps> = ({ variant = "small", className }) => {
  const mainDivStyle = clsx(
    {
      "lds-ring-small": variant === "small",
      "lds-ring": variant === "large",
      "lds-ring-small-white": variant === "smallWhite",
    },
    className
  )

  return (
    <div className={mainDivStyle}>
      <div></div>
      <div></div>
      <div></div>
    </div>
  )
}

export default Loader
