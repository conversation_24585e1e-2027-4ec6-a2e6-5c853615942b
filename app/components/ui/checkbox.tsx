import * as React from "react"
import * as CheckboxPrimitive from "@radix-ui/react-checkbox"
import { Check } from "lucide-react"

import { cn } from "@/lib/utils/shadcnUtil"
import IconSeat from "@/components/icons/icon-seat"

const Checkbox = React.forwardRef<
  React.ElementRef<typeof CheckboxPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>
>(({ className, ...props }, ref) => (
  <CheckboxPrimitive.Root
    ref={ref}
    className={cn(
      "peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
      className
    )}
    {...props}
  >
    <CheckboxPrimitive.Indicator
      className={cn("flex items-center justify-center text-current")}
    >
      <Check className="size-4" />
    </CheckboxPrimitive.Indicator>
  </CheckboxPrimitive.Root>
))
Checkbox.displayName = CheckboxPrimitive.Root.displayName

interface CheckboxGroupProps extends React.HTMLAttributes<HTMLDivElement> {}

const CheckboxGroup = React.forwardRef<HTMLDivElement, CheckboxGroupProps>(
  ({ className, ...props }, ref) => {
    return <div className={cn("grid gap-2", className)} ref={ref} {...props} />
  }
)
CheckboxGroup.displayName = "CheckboxGroup"

type CheckboxVariant = "default" | "seat"

interface CheckboxGroupItemProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  children?: React.ReactNode
  variant?: CheckboxVariant
}

const CheckboxGroupItem = React.forwardRef<
  HTMLInputElement,
  CheckboxGroupItemProps
>(({ className, children, variant = "default", ...props }, ref) => {
  const variantClasses = {
    seat: "relative flex aspect-square h-full w-full items-center justify-center ring-offset-background peer-checked:text-white peer-focus:outline-none peer-focus-visible:ring-2 peer-focus-visible:ring-ring peer-focus-visible:ring-offset-0 peer-disabled:cursor-not-allowed",
    default:
      "relative flex aspect-square h-full w-full items-center justify-center rounded-full border border-primary ring-offset-background peer-checked:border-primary-blue peer-checked:bg-primary-blue peer-checked:text-white peer-focus:outline-none peer-focus-visible:ring-2 peer-focus-visible:ring-ring peer-focus-visible:ring-offset-0 peer-disabled:cursor-not-allowed peer-disabled:opacity-50 peer-data-[state=unchecked]:text-black",
  }

  return (
    <div className="relative">
      <input type="checkbox" ref={ref} className="peer sr-only" {...props} />
      <label
        htmlFor={props.id}
        className={cn(variantClasses[variant], className)}
      >
        {variant === "seat" && (
          <div className="absolute inset-0 size-full p-0">
            <IconSeat
              fill={
                props.checked ? "#2b70ab" : props.disabled ? "#99a1af" : "none"
              }
              stroke={props.disabled ? "#99a1af" : "#000"}
              className="size-full"
            />
          </div>
        )}
        <span className="absolute inset-0 flex size-full items-center justify-center">
          {children}
        </span>
      </label>
    </div>
  )
})
CheckboxGroupItem.displayName = "CheckboxGroupItem"

export { CheckboxGroup, CheckboxGroupItem, Checkbox }
