import clsx from "clsx"
import type { <PERSON> } from "react"

interface Props {
  noMargin?: boolean
  className?: string
}

const TicketLine: FC<Props> = ({ noMargin = true, className }) => {
  return (
    <div
      className={clsx(
        "bg-[url('/ticket-loop.png')] bg-repeat-x",
        className,
        {
          "h-8 bg-center my-4": noMargin,
          "h-4": !noMargin,
        }
      )}
    />
  )
}

export default TicketLine
