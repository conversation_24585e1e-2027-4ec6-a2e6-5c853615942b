import * as React from "react"

import { cn } from "@/lib/utils/shadcnUtil"

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  rightSection?: React.ReactNode
  leftSection?: React.ReactNode
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, rightSection, leftSection, ...props }, ref) => {
    return (
      <div className="relative">
        {leftSection && (
          <div className="absolute left-2 top-1/2 -translate-y-1/2 text-xl text-gray-400">
            {leftSection}
          </div>
        )}
        <input
          type={type}
          className={cn(
            "flex h-10 w-full rounded-md border border-black bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring focus-visible:ring-offset-0 disabled:cursor-not-allowed disabled:opacity-50",
            leftSection && "pl-10",
            className
          )}
          ref={ref}
          {...props}
        />
        {rightSection && (
          <div className="absolute right-2 top-1/2 -translate-y-1/2 text-xl text-gray-400">
            {rightSection}
          </div>
        )}
      </div>
    )
  }
)
Input.displayName = "Input"

export { Input }
