import {
  FaArrowDown,
  FaArrowLeft,
  FaArrowRight,
  FaArrowUp,
} from "react-icons/fa"

interface Props {
  className?: string
}

export const IconArrowUp = ({ className }: Props) => {
  return <FaArrowUp className={className} />
}

export const IconArrowDown = ({ className }: Props) => {
  return <FaArrowDown className={className} />
}

export const IconArrowLeft = ({ className }: Props) => {
  return <FaArrowLeft className={className} />
}

export const IconArrowRight = ({ className }: Props) => {
  return <FaArrowRight className={className} />
}
