interface IconSeatProps {
  fill?: string
  stroke?: string
  strokeWidth?: number
  className?: string
}
const IconSeat = ({
  fill = "none",
  stroke = "#000",
  strokeWidth = 2,
  className = "",
}: IconSeatProps) => {
  return (
    <svg
      className={className}
      viewBox="0 0 207.98 206.44"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs></defs>
      <g id="Layer_1-2" data-name="Layer 1">
        <g>
          <path
            fill={fill}
            stroke={stroke}
            strokeWidth={strokeWidth}
            strokeMiterlimit="10"
            d="M24.22,64.88v-17.09c0-17.43,14.13-31.55,31.55-31.55h96.55c17.43,0,31.55,14.13,31.55,31.55v17.09"
          />
          <path
            fill={fill}
            stroke={stroke}
            strokeWidth={strokeWidth}
            strokeMiterlimit="10"
            d="M24.22,52.64v-17.09c0-17.43,14.13-31.55,31.55-31.55h96.55c17.43,0,31.55,14.13,31.55,31.55v17.09"
          />
          <path
            fill={fill}
            stroke={stroke}
            strokeWidth={strokeWidth}
            strokeMiterlimit="10"
            d="M183.86,66.87v96.74c0,21.45-17.38,38.83-38.83,38.83H63.05c-21.45,0-38.83-17.38-38.83-38.83v-96.74"
          />
          <path
            fill={fill}
            stroke={stroke}
            strokeWidth={strokeWidth}
            strokeMiterlimit="10"
            d="M14,57.14h10.1v97h-10.1c-5.52,0-10-4.48-10-10v-77c0-5.52,4.48-10,10-10Z"
          />
          <path
            fill={fill}
            stroke={stroke}
            strokeWidth={strokeWidth}
            strokeMiterlimit="10"
            d="M183.88,57.14h10.1c5.52,0,10,4.48,10,10v77c0,5.52-4.48,10-10,10h-10.1V57.14h0Z"
          />
        </g>
      </g>
    </svg>
  )
}

export default IconSeat
