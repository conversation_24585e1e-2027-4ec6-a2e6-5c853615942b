import dayjs from "dayjs"
import { z } from "zod"

export const couponSchema = z.object({
  event_id: z.string(),
  name: z.string().min(1, "Name is required"),
  is_published: z.boolean(),
  expiry_date: z
    .string()
    .refine((v) => v !== "", "Expiry date is required")
    .transform((v) => dayjs(v).format("YYYY-MM-DD HH:mm:ss")),
  max_usage: z.coerce
    .number({
      required_error: "Max usage is required",
      invalid_type_error: "Max usage must be a number",
    })
    .int()
    .positive()
    .min(1, { message: "Max usage should be at least 1" }),
  discount_percent: z.coerce
    .number({
      required_error: "Discount value is required",
      invalid_type_error: "Discount value must be a number",
    })
    .int()
    .positive()
    .min(1, { message: "Discount value should be at least 1" }),
})

export const createCouponSchema = z.object({
  data: z.array(couponSchema),
})

export type CouponType = z.infer<typeof couponSchema>
export type CreateCouponType = z.infer<typeof createCouponSchema>
