import type { PastEventsQuery } from "@/__generated__/graphql"
import { PAST_EVENTS } from "@/lib/graphql/queries"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import type { EventTypes } from "@/lib/types/event-types"
import { useInfiniteQuery } from "@tanstack/react-query"
import dayjs from "dayjs"

interface Props {
  type: EventTypes
}

const FIRST = 10

const useGetPastEvents = ({ type }: Props) => {
  const graphql = getGraphqlClient()

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isError,
    isFetching,
    isFetchingNextPage,
    isLoading,
  } = useInfiniteQuery({
    getNextPageParam: (lastPage: PastEventsQuery) => {
      if (!lastPage.getPastEvents.paginatorInfo.hasMorePages) {
        return
      }
      return lastPage.getPastEvents.paginatorInfo.currentPage + 1
    },
    initialPageParam: 1,
    queryFn: async ({ pageParam = 1 }) =>
      await graphql.request(PAST_EVENTS, {
        page: pageParam,
        currentDatetime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
        first: FIRST,
        type,
      }),
    queryKey: ["past-events", type],
  })

  return {
    data,
    isLoading,
    isError,
    fetchNextPage,
    hasNextPage,
    isFetching,
    isFetchingNextPage,
  }
}

export default useGetPastEvents
