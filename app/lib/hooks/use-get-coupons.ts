import { GET_COUPONS } from "@/lib/graphql/queries"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import { useQuery } from "@tanstack/react-query"

const useGetCoupons = (eventId: string) => {
  const graphql = getGraphqlClient()

  const { data, isLoading, isError } = useQuery({
    queryKey: ["coupons", eventId],
    queryFn: async () => {
      return graphql.request(GET_COUPONS, {
        eventId: eventId,
      })
    },
  })

  return { data, isLoading, isError }
}

export default useGetCoupons
