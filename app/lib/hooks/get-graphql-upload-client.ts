import { baseUrl, extractXsrf<PERSON>romCookie } from "@/lib/utils"
import type { DocumentNode } from "graphql"
import { print } from "graphql"
import { ClientError, GraphQLClient } from "graphql-request"

interface Props {
  fieldName: string
  graphqlDocument: DocumentNode
  isMultiple?: boolean
  token?: string
}

const refreshCsrfToken = async () => {
  try {
    await fetch(`${baseUrl}/csrf`, {
      credentials: "include",
      method: "GET",
    })
    // After fetching, the browser should have the new cookie
    if (typeof document !== "undefined") {
      const cookie = document.cookie
      return extractXsrfFromCookie(cookie) || ""
    }
  } catch (error) {
    console.error("Failed to refresh CSRF token:", error)
  }
  return ""
}

function getGraphqlUploadClient({
  fieldName,
  graphqlDocument,
  isMultiple,
  token,
}: Props) {
  let xsrfToken = ""

  if (typeof document !== "undefined") {
    const cookie = document.cookie
    xsrfToken = extractXsrfFromCookie(cookie) || ""
  }

  const graphqlClientInstance = new GraphQLClient(`${baseUrl}/graphql`, {
    credentials: "include",

    requestMiddleware: (request) => {
      if (!request.variables?.[fieldName]) {
        return request
      }

      const mutationAsString = print(graphqlDocument)
      const formData = new FormData()
      const variables = { ...request.variables }
      const files = variables[fieldName]

      // Set field to null in variables as it will be replaced by file references
      variables[fieldName] = null

      console.log("variables", variables)
      formData.append(
        "operations",
        JSON.stringify({
          query: mutationAsString,
          variables,
        })
      )

      // Create the map object based on whether we have multiple files or not
      const map: Record<string, string[]> = {}

      if (isMultiple && Array.isArray(files)) {
        // For multiple files, create a map entry for each file
        for (const [index] of files.entries()) {
          map[index] = [`variables.${fieldName}.${index}`]
        }

        // Append each file to the FormData
        for (const [index, file] of files.entries()) {
          formData.append(index.toString(), file)
        }
      } else {
        // Single file upload (original behavior)
        map[0] = [`variables.${fieldName}`]
        formData.append("0", files)
      }

      formData.append("map", JSON.stringify(map))

      if (request.headers instanceof Headers) {
        request.headers.delete("Content-Type")
      }

      return {
        ...request,
        body: formData,
        // headers: {},
      }
    },
  })

  graphqlClientInstance.setHeaders({
    Authorization: `Bearer ${token}`,
    "X-XSRF-TOKEN": decodeURIComponent(xsrfToken),
  })

  // graphqlClientInstance.request

  const originalRequest = graphqlClientInstance.request.bind(
    graphqlClientInstance
  )
  graphqlClientInstance.request = async (...args) => {
    try {
      return await originalRequest(...args)
    } catch (error) {
      console.log("caught error")
      console.log("error", error)
      if (error instanceof ClientError && error?.response?.status === 419) {
        console.log("caught error is 419")
        const newToken = await refreshCsrfToken()
        if (newToken) {
          graphqlClientInstance.setHeader(
            "X-XSRF-TOKEN",
            decodeURIComponent(newToken)
          )
          return originalRequest(...args)
        }
      }
      throw error
    }
  }

  return graphqlClientInstance
}

export default getGraphqlUploadClient
