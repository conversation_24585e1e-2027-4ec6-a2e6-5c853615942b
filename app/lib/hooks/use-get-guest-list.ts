import { GET_GUEST_LIST } from "@/lib/graphql/queries"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import { NUMBER_OF_ITEMS_TO_FETCH, SEARCH_DEBOUNCE_VALUE } from "@/lib/utils"
import { useInfiniteQuery } from "@tanstack/react-query"
import { useState } from "react"
import { useDebounce } from "use-debounce"

const useGetGuestList = (eventId: string) => {
  const graphql = getGraphqlClient()

  const [searchGuestList, setSearchGuestList] = useState("")
  const [ticketType, setTicketType] = useState<string | null>("")
  const [searchString] = useDebounce(searchGuestList, SEARCH_DEBOUNCE_VALUE)

  const handleTicketTypeChange = (e: string) => {
    if (e === "All") {
      setTicketType(() => {
        return null
      })
    } else {
      setTicketType(() => {
        return e
      })
    }
  }

  const handleSearchChange = (e: string) => {
    setSearchGuestList(e)
  }

  const {
    data,
    isLoading,
    isError,
    isFetchingNextPage,
    fetchNextPage,
    hasNextPage,
  } = useInfiniteQuery({
    queryKey: ["get-guest-list", searchString, eventId, ticketType],
    queryFn: async ({ pageParam = 1 }) => {
      const isMobileNumber = /^\d+$/.test(searchString)
      const containsLetter = /[a-zA-Z]/.test(searchString)

      const modifiedSearch = `%${searchString}%`

      return graphql.request(GET_GUEST_LIST, {
        eventId: eventId,
        first: NUMBER_OF_ITEMS_TO_FETCH,
        page: pageParam,
        ticketTypeId: ticketType,
        mobileNumber:
          isMobileNumber && !containsLetter ? modifiedSearch : undefined,
        userName:
          containsLetter && !isMobileNumber ? modifiedSearch : undefined,
      })
    },
    enabled: !!eventId,
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      if (!lastPage.getGuestList?.paginatorInfo.hasMorePages) {
        return
      }
      return lastPage.getGuestList.paginatorInfo.currentPage + 1
    },
  })

  const total = data?.pages[0]?.getGuestList?.paginatorInfo?.total || 0

  return {
    data,
    isLoading,
    isError,
    isFetchingNextPage,
    fetchNextPage,
    ticketType,
    handleTicketTypeChange,
    handleSearchChange,
    searchGuestList,
    hasNextPage,
    total,
  }
}

export default useGetGuestList
