import { useImmer } from "use-immer"

import { GST_AMOUNT } from "../utils/constants"

interface CalculatePricingProps {
  convenienceAmount: number
  selectedNumberOfTickets: number
  ticketPrice: number
  discountPercent?: number
  calculateGst?: boolean
  tipAmount?: number
}

const usePaymentCalculation = () => {
  const [pricing, updatePricing] = useImmer({
    ticketPrice: 0, // single ticket price
    totalAmount: 0, // ticket price * number of tickets bought
    totalAmountPayable: 0, // total amount to pay after calculating discount, gst and convenience fees
    convenienceFee: 0, // convenience fee for each ticket
    gstAmount: 0, // amount of gst fees needed to be paid
    discountAmount: 0, // amount of discount when applying a discount code
    tipAmount: 0, // amount of tip given
  })

  const calculatePricing = ({
    convenienceAmount,
    ticketPrice,
    selectedNumberOfTickets,
    calculateGst,
    discountPercent,
    tipAmount,
  }: CalculatePricingProps) => {
    // calculate totalAmount without additional fees
    const totalAmount = ticketPrice * selectedNumberOfTickets

    // total convenience fee depending on number of tickets bought
    const convenienceFee = convenienceAmount * selectedNumberOfTickets

    let amountPayable = 0
    let discount = 0
    let gstAmount = 0

    // if apply discount code
    if (discountPercent) {
      discount = totalAmount * (discountPercent / 100)
      amountPayable = totalAmount - discount
    } else {
      amountPayable = totalAmount
    }

    // if gst needs to be calculated
    if (calculateGst) {
      gstAmount = amountPayable * GST_AMOUNT
      amountPayable = amountPayable + gstAmount
    }

    // if tip amount is given
    if (tipAmount) {
      amountPayable = amountPayable + tipAmount
    }

    amountPayable = amountPayable + convenienceFee

    updatePricing((draft) => {
      draft.totalAmount = totalAmount
      draft.convenienceFee = convenienceFee
      draft.totalAmountPayable = amountPayable
      draft.gstAmount = gstAmount
      draft.ticketPrice = ticketPrice
      draft.discountAmount = discount
      draft.tipAmount = tipAmount || 0
    })
  }

  return { pricing, calculatePricing }
}

export default usePaymentCalculation
