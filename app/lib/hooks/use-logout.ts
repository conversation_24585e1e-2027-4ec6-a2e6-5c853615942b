import { LOGOUT_MUTATION } from "@/lib/graphql/mutations"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import { useMutation } from "@tanstack/react-query"

export default function useLogout() {
  const graphql = getGraphqlClient()

  const logoutUser = useMutation({
    mutationFn: async () => {
      return await graphql.request(LOGOUT_MUTATION)
    },
  })

  return { logoutUser }
}
