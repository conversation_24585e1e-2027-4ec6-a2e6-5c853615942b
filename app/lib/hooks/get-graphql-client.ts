import { ClientError, GraphQL<PERSON>lient } from "graphql-request"

import { baseUrl, extractXsrfFromCookie } from "../utils"
import { redirect } from "react-router"

const refreshCsrfToken = async () => {
  try {
    await fetch(`${baseUrl}/csrf`, {
      credentials: "include",
      method: "GET",
    })
    // After fetching, the browser should have the new cookie
    if (typeof document !== "undefined") {
      const cookie = document.cookie
      return extractXsrfFromCookie(cookie) || ""
    }
  } catch (error) {
    console.error("Failed to refresh CSRF token:", error)
  }
  return ""
}

const getGraphqlClient = (token?: string) => {
  let xsrfToken = ""

  if (typeof document !== "undefined") {
    const cookie = document.cookie
    xsrfToken = extractXsrfFromCookie(cookie) || ""
  }

  const graphqlClientInstance = new GraphQLClient(`${baseUrl}/graphql`, {
    credentials: "include",
  })
  graphqlClientInstance.setHeaders({
    Authorization: `Bearer ${token}`,
    "X-XSRF-TOKEN": decodeURIComponent(xsrfToken),
  })

  const originalRequest = graphqlClientInstance.request.bind(
    graphqlClientInstance
  )
  graphqlClientInstance.request = async (...args) => {
    try {
      return await originalRequest(...args)
    } catch (error) {
      console.log("caught error by graphql-client just error", error)
      if (error instanceof ClientError && error?.response?.status === 419) {
        console.log("caught error is 419")
        const newToken = await refreshCsrfToken()
        if (newToken) {
          graphqlClientInstance.setHeader(
            "X-XSRF-TOKEN",
            decodeURIComponent(newToken)
          )
          return originalRequest(...args)
        }
      } 
      // else if (
      //   error instanceof ClientError &&
      //   error?.response?.status === 429
      // ) {
      //   console.log("caught error is 429")
      // }
      // return redirect(
      //   `/too-many-requests?ref=${globalThis.location.pathname}`
      // )
      throw error
    }
  }

  return graphqlClientInstance
}

export default getGraphqlClient
