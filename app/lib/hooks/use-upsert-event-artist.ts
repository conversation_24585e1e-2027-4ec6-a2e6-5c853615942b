import { UPSERT_EVENT_ARTIST } from "@/lib/graphql/mutations"
import getGraphqlUploadClient from "@/lib/hooks/get-graphql-upload-client"
import type { UpsertArtistType } from "@/lib/types/artist-types"
import getGraphqlMultipleClient from "@/lib/utils/graphql-upload-multiple-client"
import { useMutation } from "@tanstack/react-query"

const useUpsertEventArtist = (eventId: string, artistId?: string) => {
  const graphql = getGraphqlUploadClient({
    fieldName: "avatar",
    graphqlDocument: UPSERT_EVENT_ARTIST,
  })

  // const graphql = getGraphqlMultipleClient({
  //   fieldConfig: [{ fieldName: "avatar", isMultiple: false }],
  //   graphqlDocument: UPSERT_EVENT_ARTIST,
  // })

  const upsertArtist = useMutation({
    mutationFn: async (data: UpsertArtistType) => {
      return await graphql.request(UPSERT_EVENT_ARTIST, {
        ...data,
        eventId: eventId,
        id: artistId,
      })
    },
  })

  return { upsertArtist }
}

export default useUpsertEventArtist
