import { GET_ME_QUERY } from "@/lib/graphql/queries"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import { useQuery } from "@tanstack/react-query"

const useGetMe = () => {
  const graphql = getGraphqlClient()
  const { data, isLoading, isError } = useQuery({
    queryKey: ["get-me"],
    queryFn: async () => {
      return await graphql.request(GET_ME_QUERY)
    },
  })

  return { data, isLoading, isError }
}

export default useGetMe
