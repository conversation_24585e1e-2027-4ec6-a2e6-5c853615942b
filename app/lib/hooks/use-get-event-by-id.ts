import { GET_EVENT_BY_ID } from "@/lib/graphql/queries"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import { useQuery } from "@tanstack/react-query"

const useGetEventById = (eventId: string) => {
  const graphql = getGraphqlClient()

  const { data, isLoading, isError } = useQuery({
    queryKey: ["event-by-id", eventId],
    queryFn: async () => {
      return graphql.request(GET_EVENT_BY_ID, {
        id: eventId,
      })
    },
  })

  return { data, isLoading, isError }
}

export default useGetEventById
