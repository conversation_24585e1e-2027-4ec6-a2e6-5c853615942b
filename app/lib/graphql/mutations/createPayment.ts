import { graphql } from "@/__generated__"

const CREATE_PAYMENT_MUTATION = graphql(`
  mutation CreatePayment(
    $eventTicketTypeId: ID!
    $mobileNumber: String!
    $username: String!
    $numberOfTickets: Int!
    $couponHash: String
    $isGift: Boolean
    $recipientName: String
    $recipientPhone: String
    $recipientMessage: String
    $tipAmount: Float
    $tipMessage: String
  ) {
    createPayment(
      event_ticket_type_id: $eventTicketTypeId
      mobile_number: $mobileNumber
      username: $username
      num_of_tickets: $numberOfTickets
      coupon_hash: $couponHash
      is_gift: $isGift
      recipient_name: $recipientName
      recipient_phone: $recipientPhone
      recipient_message: $recipientMessage
      tip_amount: $tipAmount
      tip_message: $tipMessage
    ) {
      order_id
      payment_id
      provider_type
      goto_url
      mobile_number
      username
    }
  }
`)

export default CREATE_PAYMENT_MUTATION
