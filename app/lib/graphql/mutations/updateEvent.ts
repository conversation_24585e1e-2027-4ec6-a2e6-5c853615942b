import { graphql } from "@/__generated__"

const UPDATE_EVENT_MUTATION = graphql(`
  mutation UpdateEvent(
    $id: ID!
    $name: String
    $description: String
    $location: String
    $eventImages: [Upload]
    $address: String
    $orgName: String
    $orgContactNumber: String
    $isPublished: Boolean!
    $isPrivateEvent: Boolean
    $startDate: DateTime
    $endDate: DateTime
    $startBookingDate: DateTime
    $openMicLink: String
    $seatingPlan: Upload
    $city: String
    $faq: String
  ) {
    updateEvent(
      id: $id
      name: $name
      description: $description
      location: $location
      event_images: $eventImages
      organizer_name: $orgName
      org_contact_number: $orgContactNumber
      address: $address
      is_published: $isPublished
      is_private_event: $isPrivateEvent
      start_date: $startDate
      end_date: $endDate
      start_booking_date: $startBookingDate
      open_mic_link: $openMicLink
      seating_plan: $seatingPlan
      city: $city
      faq: $faq
    ) {
      id
    }
  }
`)

export default UPDATE_EVENT_MUTATION
