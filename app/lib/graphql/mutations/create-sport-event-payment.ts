import { graphql } from "@/__generated__"

export const CREATE_SPORT_EVENT_PAYMENT = graphql(`
  mutation CreateSportEventPayment(
    $ticketTypeId: Int!
    $mobileNumber: String!
    $numberOfTickets: Int
    $username: String!
    $seatNumbers: [Int!]!
    $tipAmount: Float
    $tipMEssage: String
  ) {
    createSportEventPayment(
      ticket_type_id: $ticketTypeId
      mobile_number: $mobileNumber
      number_of_tickets: $numberOfTickets
      username: $username
      seat_numbers: $seatNumbers
      tip_amount: $tipAmount
      tip_message: $tipMEssage
    ) {
      order_id
      payment_id
      provider_type
      goto_url
      username
      mobile_number
    }
  }
`)
