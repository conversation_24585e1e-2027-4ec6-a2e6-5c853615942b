import { graphql } from "@/__generated__"

const UPSERT_EVENT_ARTIST = graphql(`
  mutation UpsertEventArtist(
    $id: ID
    $name: String
    $avatar: Upload
    $order: Int
    $eventId: ID!
    $social_link: String
  ) {
    upsertEventArtist(
      id: $id
      name: $name
      avatar: $avatar
      order: $order
      event_id: $eventId
      social_link: $social_link
    ) {
      id
    }
  }
`)

export default UPSERT_EVENT_ARTIST
