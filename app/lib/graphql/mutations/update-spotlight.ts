import { graphql } from "@/__generated__"

const UPDATE_SPOTLIGHT_MUTATION = graphql(`
  mutation UpdateSpotlight(
    $id: Int!
    $image: Upload
    $url: String
    $order: Int
    $isExternal: Boolean
  ) {
    updateSpotlight(
      id: $id
      image: $image
      url: $url
      is_external: $isExternal
      order: $order
    ) {
      message
    }
  }
`)

export default UPDATE_SPOTLIGHT_MUTATION
