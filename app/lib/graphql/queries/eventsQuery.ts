import { graphql } from "@/__generated__"

const EVENTS_QUERY = graphql(`
  query Events(
    $page: Int!
    $currentDatetime: DateTime!
    $name: String
    $event_types: [String!]
    $first: Int!
  ) {
    getEvents(
      page: $page
      current_datetime: $currentDatetime
      name: $name
      event_types: $event_types
      first: $first
    ) {
      data {
        id
        name
        start_date
        end_date
        type
        images {
          id
          path
          hash
          width
          height
        }
        slug
        eventTicketTypes {
          price
          ticket_type
        }
        start_booking_date
        open_mic_link
        city
      }
      paginatorInfo {
        lastPage
        hasMorePages
        currentPage
      }
    }
  }
`)

export default EVENTS_QUERY
