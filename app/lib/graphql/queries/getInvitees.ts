import { graphql } from "@/__generated__"

const GET_INVITEES = graphql(`
  query GetInvitees(
    $username: String
    $mobileNumber: String
    $eventId: ID!
    $first: Int!
    $page: Int
  ) {
    getInvitees(
      username: $username
      mobile_number: $mobileNumber
      event_id: $eventId
      first: $first
      page: $page
    ) {
      data {
        id
        username
        mobile_number
        whatsapp_sent_status
        number_of_tickets
        scanned_at
        paymentOrder {
          paymentable {
            __typename
            ... on PhonePePaymentOrder {
              order_id
              payment_id
            }
            ... on RzpayPaymentOrder {
              order_id
              payment_id
            }
          }
        }
      }
      paginatorInfo {
        lastPage
        total
        hasMorePages
        currentPage
      }
    }
  }
`)

export default GET_INVITEES
