import { graphql } from "@/__generated__"

const EVENTS_FOR_SUPER_ADMIN = graphql(`
  query EventsForSuperAdmin(
    $page: Int!
    $currentDatetime: DateTime!
    $name: String
    $first: Int!
    $type: String! # you can't run codegen without this coz it's required
  ) {
    events(
      page: $page
      current_datetime: $currentDatetime
      name: $name
      first: $first
      type: $type
    ) {
      data {
        id
        name
      }
    }
  }
`)

export default EVENTS_FOR_SUPER_ADMIN
