import { graphql } from "@/__generated__"

const SPORTS_QUERY = graphql(`
  query Sports(
    $page: Int!
    $currentDatetime: DateTime!
    $name: String
    $type: String!
  ) {
    events(
      page: $page
      current_datetime: $currentDatetime
      name: $name
      type: $type
    ) {
      data {
        id
        name
        start_date
        images {
          id
          path
          hash
          width
          height
        }
        slug
        sportTicketTypes {
          price
          ticket_type
          start_seat_number
          end_seat_number
        }
        start_booking_date
        open_mic_link
        city
      }
      paginatorInfo {
        lastPage
      }
    }
  }
`)

export default SPORTS_QUERY
