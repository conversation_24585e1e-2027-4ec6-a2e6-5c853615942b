import { graphql } from "@/__generated__"

const PAST_EVENTS = graphql(`
  query PastEvents(
    $first: Int!
    $currentDatetime: DateTime!
    $page: Int
    $type: String
  ) {
    getPastEvents(
      first: $first
      current_datetime: $currentDatetime
      page: $page
      type: $type
    ) {
      data {
        id
        name
        type
        end_date
        images {
          id
          path
          hash
          width
          height
        }
        slug
      }
      paginatorInfo {
        hasMorePages
        currentPage
        lastPage
      }
    }
  }
`)

export default PAST_EVENTS
