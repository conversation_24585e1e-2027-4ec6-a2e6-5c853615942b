import { graphql } from "@/__generated__"

const MY_EVENTS_QUERY = graphql(`
  query MyEvents($page: Int!, $first: Int!) {
    myEvents(page: $page, first: $first) {
      data {
        id
        name
        description
        location
        eventTicketTypes {
          id
          ticket_type
          price
          maximum_capacity
        }
        images {
          id
          path
          hash
          width
          height
        }
        slug
        start_date
        end_date
        address
        org_contact_number
        is_published
        is_private_event
      }
      paginatorInfo {
        lastPage
        hasMorePages
        currentPage
      }
    }
  }
`)

export default MY_EVENTS_QUERY
