import { graphql } from "@/__generated__"

const SPORTS_BY_SLUG = graphql(`
  query SportsBySlug($slug: String!) {
    eventBySlug(slug: $slug) {
      id
      name
      description
      location
      type
      sportTicketTypes {
        id
        price
        ticket_type
        start_seat_number
        end_seat_number
        slug
      }
      seatingPlan {
        id
        path
      }
      images {
        id
        path
        hash
        width
        height
      }
      slug
      start_date
      end_date
      start_booking_date
      address
      org_contact_number
      organizer_name
      faq
      artists {
        id
        name
        avatar
        social_link
      }
      open_mic_link
      coupons {
        id
      }
    }
  }
`)

export default SPORTS_BY_SLUG
