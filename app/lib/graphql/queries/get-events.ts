import { graphql } from "@/__generated__"

const GET_EVENTS = graphql(`
  query getEvents(
    $first: Int!
    $page: Int
    $name: String
    $eventTypes: [String!]
    $sortOrder: String
  ) {
    getEvents(
      first: $first
      page: $page
      name: $name
      event_types: $eventTypes
      sort_order: $sortOrder
    ) {
      data {
        id
        name
        type
        start_date
        end_date
        images {
          id
          path
          hash
          width
          height
        }
        slug
        eventTicketTypes {
          price
          ticket_type
        }
        sportTicketTypes {
          price
          ticket_type
        }
        start_booking_date
        open_mic_link
        city
      }
      paginatorInfo {
        hasMorePages
        currentPage
        lastPage
      }
    }
  }
`)

export default GET_EVENTS
