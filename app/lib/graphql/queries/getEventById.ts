import { graphql } from "@/__generated__"

const EVENT_BY_ID = graphql(`
  query EventById($id: ID!) {
    eventById(id: $id) {
      id
      name
      description
      location
      images {
        id
        path
        hash
        width
        height
      }
      seatingPlan {
        id
        path
        hash
      }
      address
      org_contact_number
      is_published
      is_private_event
      organizer_name
      eventTicketTypes {
        id
        ticket_type
        price
        maximum_capacity
      }
      slug
      start_date
      end_date
      start_booking_date
      artists {
        id
        name
        avatar
        social_link
        order
      }
      open_mic_link
      city
      faq
    }
  }
`)

export default EVENT_BY_ID
