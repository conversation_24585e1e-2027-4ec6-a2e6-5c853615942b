import { graphql } from "@/__generated__"

const GET_GUEST_LIST = graphql(`
  query GetGuestList(
    $eventId: ID!
    $first: Int!
    $page: Int
    $userName: String
    $mobileNumber: String
    $ticketTypeId: ID
  ) {
    getGuestList(
      event_id: $eventId
      first: $first
      page: $page
      username: $userName
      mobile_number: $mobileNumber
      ticket_type_id: $ticketTypeId
    ) {
      __typename
      ... on SportGuestList {
        data {
          id
          username
          mobile_number
          number_of_tickets
          seat_numbers
          scanned_at
          paymentOrder {
            paymentable {
              __typename
              ... on PhonePePaymentOrder {
                order_id
                payment_id
              }
              ... on RzpayPaymentOrder {
                order_id
                payment_id
              }
            }
          }
          eventTicketType {
            id
            ticket_type
            slug
          }
          tip_amount
          tip_message
        }
        paginatorInfo {
          lastPage
          total
          currentPage
          hasMorePages
        }
      }
      ... on GeneralGuestList {
        data {
          id
          username
          mobile_number
          number_of_tickets
          scanned_at
          paymentOrder {
            paymentable {
              __typename
              ... on PhonePePaymentOrder {
                order_id
                payment_id
              }
              ... on RzpayPaymentOrder {
                order_id
                payment_id
              }
            }
          }
          eventTicketType {
            id
            ticket_type
          }
          tip_amount
          tip_message
        }
        paginatorInfo {
          lastPage
          total
          currentPage
          hasMorePages
        }
      }
    }
  }
`)

export default GET_GUEST_LIST
