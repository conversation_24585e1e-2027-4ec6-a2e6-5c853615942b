import { graphql } from "@/__generated__"

const EVENT_BY_SLUG = graphql(`
  query EventBySlug($slug: String!) {
    eventBySlug(slug: $slug) {
      id
      name
      description
      type
      location
      eventTicketTypes {
        id
        ticket_type
        price
        maximum_capacity
        status
        is_convenience_fee_incl
        is_gst_incl
      }
      seatingPlan {
        id
        path
      }
      images {
        id
        path
        hash
        width
        height
      }
      slug
      start_date
      end_date
      start_booking_date
      address
      org_contact_number
      organizer_name
      faq
      artists {
        id
        name
        avatar
        social_link
      }
      open_mic_link
      coupons {
        id
      }
    }
  }
`)

export default EVENT_BY_SLUG
