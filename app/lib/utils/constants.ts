export const eventType = [
  { id: 1, name: "Public Event", isPrivateEvent: false },
  { id: 2, name: "Private Event", isPrivateEvent: true },
]

export const selectTicketClass = [
  { id: 1, name: "Single Tier" },
  { id: 2, name: "Multiple Tier" },
]

export const TICKET_CLASSES = [
  { id: "1", name: "Single Tier" },
  { id: "2", name: "Multiple Tier" },
]

export const REFUND_EVENTS =
  process.env.NODE_ENV === "production"
    ? [
        // { id: "26", name: "Music Travel Love - Live in Aizawl" },
        { id: "28", name: "MUSIC TRAVEL LOVE Live in Shillong" },
      ]
    : process.env.NODE_ENV === "test"
      ? [
          // { id: "24", name: "Music Travel Love - Live in Aizawl" },
          { id: "25", name: "MUSIC TRAVEL LOVE Live in Shillong" },
        ]
      : [
          // { id: "24", name: "Music Travel Love - Live in Aizawl" },
          { id: "25", name: "MUSIC TRAVEL LOVE Live in Shillong" },
        ]

export const CITIES = [
  { id: 1, label: "Aizawl", value: "aizawl" },
  { id: 2, label: "Shillong", value: "shillong" },
  { id: 3, label: "Nagaland", value: "nagaland" },
]

export const NUMBER_OF_ITEMS_TO_FETCH = 20

export const INPUT_DEBOUNCED_VALUE = 500

export const PUBLISH_STATUS = [
  { name: "No", value: false },
  { name: "Yes", value: true },
]

export const SEARCH_DEBOUNCE_VALUE = 500

export const GST_AMOUNT = 0.18

export const GST_EVENTS = ["26", "28"]
