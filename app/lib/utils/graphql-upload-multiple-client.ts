import { baseUrl, extractXsrfFromCookie } from "@/lib/utils"
import type { DocumentNode } from "graphql"
import { print } from "graphql"
import { ClientError, GraphQLClient } from "graphql-request"

interface FieldConfig {
  fieldName: string
  isMultiple?: boolean
}

interface Props {
  fieldConfig: FieldConfig | FieldConfig[]
  graphqlDocument: DocumentNode
  token?: string
}

const refreshCsrfToken = async () => {
  try {
    await fetch(`${baseUrl}/csrf`, {
      credentials: "include",
      method: "GET",
    })
    // After fetching, the browser should have the new cookie
    if (typeof document !== "undefined") {
      const cookie = document.cookie
      return extractXsrfFromCookie(cookie) || ""
    }
  } catch (error) {
    console.error("Failed to refresh CSRF token:", error)
  }
  return ""
}

function getGraphqlMultipleClient({
  fieldConfig,
  graphqlDocument,
  token,
}: Props) {
  let xsrfToken = ""

  if (typeof document !== "undefined") {
    const cookie = document.cookie
    xsrfToken = extractXsrfFromCookie(cookie) || ""
  }

  const graphqlClientInstance = new GraphQLClient(`${baseUrl}/graphql`, {
    credentials: "include",
    requestMiddleware: (request) => {
      const configs = Array.isArray(fieldConfig) ? fieldConfig : [fieldConfig]
      const hasFiles = configs.some(
        (config) => request.variables?.[config.fieldName]
      )
      if (!hasFiles) {
        return request
      }

      const mutationAsString = print(graphqlDocument)
      const formData = new FormData()
      const variables = { ...request.variables }

      configs.forEach((config) => {
        if (variables[config.fieldName]) {
          variables[config.fieldName] = null
        }
      })

      formData.append(
        "operations",
        JSON.stringify({
          query: mutationAsString,
          variables,
        })
      )

      const map: Record<string, string[]> = {}

      let fileIndex = 0

      for (const config of configs) {
        const { fieldName, isMultiple } = config
        const files = request.variables?.[fieldName]

        if (!files) continue

        // Set field to null in variables
        variables[fieldName] = null

        if (isMultiple && Array.isArray(files)) {
          // Multiple files for this field
          // for (const [i] of files.entries()) {
          //   map[fileIndex] = [`variables.${fieldName}.${i}`]
          //   formData.append(fileIndex.toString(), files[i])
          //   fileIndex++
          // }
          files.forEach((file, i) => {
            map[fileIndex] = [`variables.${fieldName}.${i}`]
            formData.append(fileIndex.toString(), file)
            fileIndex++
          })
        } else {
          // Single file for this field
          map[fileIndex] = [`variables.${fieldName}`]
          formData.append(fileIndex.toString(), files)
        }
      }

      formData.append("map", JSON.stringify(map))

      if (request.headers instanceof Headers) {
        request.headers.delete("Content-Type")
      }

      return {
        ...request,
        body: formData,
      }
    },
  })

  graphqlClientInstance.setHeaders({
    Authorization: `Bearer ${token}`,
    "X-XSRF-TOKEN": decodeURIComponent(xsrfToken),
  })

  const originalRequest = graphqlClientInstance.request.bind(
    graphqlClientInstance
  )

  graphqlClientInstance.request = async (...args) => {
    try {
      return await originalRequest(...args)
    } catch (error) {
      console.log("caught error")
      if (error instanceof ClientError && error?.response?.status === 419) {
        console.log("caught error is 419")
        const newToken = await refreshCsrfToken()
        console.log("newToken", newToken)
        if (newToken) {
          graphqlClientInstance.setHeader(
            "X-XSRF-TOKEN",
            decodeURIComponent(newToken)
          )
          return originalRequest(...args)
        }
      }
      throw error
    }
  }

  return graphqlClientInstance
}

export default getGraphqlMultipleClient
