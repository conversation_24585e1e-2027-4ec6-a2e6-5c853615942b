import { decode } from "blurhash"
import { createCanvas } from "canvas"

export const decodeBlurhashSSR = (blurhash?: string) => {
  if (!blurhash) {
    return undefined
  }

  let pixels: Uint8ClampedArray | undefined

  try {
    pixels = decode(blurhash, 32, 32)
  } catch {
    return undefined
  }

  const canvas = createCanvas(32, 32)
  const ctx = canvas.getContext("2d")

  const imageData = ctx.createImageData(32, 32)
  imageData.data.set(pixels)
  ctx.putImageData(imageData, 0, 0)

  return canvas.toDataURL()
}
