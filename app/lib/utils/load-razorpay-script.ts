"use client"

let isRazorpayLoaded = false

export const loadRazorpayScript = () => {
  if (isRazorpayLoaded) {
    return Promise.resolve(true)
  }

  if (!document) {
    return
  }

  return new Promise((resolve) => {
    const script = document.createElement("script")
    script.src = "https://checkout.razorpay.com/v1/checkout.js"
    script.addEventListener("load", () => {
      isRazorpayLoaded = true
      resolve(true)
    })
    script.addEventListener("error", () => {
      isRazorpayLoaded = false
      alert("Error Unable to load razorpay")
      resolve(false)
    })
    // script.onerror = () => {
    //   isRazorpayLoaded = false;
    //   resolve(false);
    // };
    document.body.append(script)
  })
}
