import toast from "react-hot-toast"

export const handleCopyToClipboard = async () => {
  const userAgent = navigator.userAgent
  const currentUrl = window.location.href
  if (
    userAgent.match(/Android/i) ||
    userAgent.match(/webOS/i) ||
    userAgent.match(/iPhone/i) ||
    userAgent.match(/iPad/i) ||
    userAgent.match(/iPod/i) ||
    userAgent.match(/BlackBerry/i) ||
    userAgent.match(/Windows Phone/i)
  ) {
    await navigator.share({
      title: document.title,
      url: currentUrl,
    })
  } else {
    await navigator.clipboard.writeText(currentUrl)
    toast.success("URL copied to clipboard")
  }
}
