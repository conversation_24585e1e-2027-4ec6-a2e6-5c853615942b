const parseRefundStatusForTable = (status: string) => {
  switch (status) {
    case "refund.created":
      return "Refund created"
    case "refund.processed":
      return "Refund processed"
    case "refund.failed":
      return "Refund failed"
    case "PAYMENT_PENDING":
      return "Payment pending"
    case "PAYMENT_SUCCESS":
      return "Payment success"
    case "INTERNAL_SERVER_ERROR":
      return "Internal server error"
    case "BAD_REQUEST":
      return "Bad request"
    case "AUTHORIZATION_FAILED":
      return "Authorization failed"
    case "TRANSACTION_NOT_FOUND":
      return "Transaction not found"
    case "PAYMENT_ERROR":
      return "Payment error"
    case "PAYMENT_DECLINED":
      return "Payment declined"
    case "REVERSAL_WINDOW_EXCEEDED":
      return "Reversal window exceeded"
    default:
      return ""
  }
}

export default parseRefundStatusForTable
