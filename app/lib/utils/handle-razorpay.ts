import { rz<PERSON><PERSON><PERSON><PERSON> } from "@/lib/utils"

interface Props {
  username?: string
  mobileNumber?: string
  amount: number
  orderId: string
  handlerCallback?: () => void // for example resetting the form and handling loading
  setLoading: (open: boolean) => void
}

export const handleRazorpay = ({
  username,
  mobileNumber,
  amount,
  orderId,
  handlerCallback,
  setLoading,
}: Props) => {
  // const { setLoading } = razorpayLoaderStore()
  setLoading(true)
  const rzpAmount = amount * 100
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const options = {
    key: rzpayKey,
    amount: rzpAmount, // in currency subunits. Here 1000 = 1000 paise, which equals to ₹10
    currency: "INR", // Default is INR. We support more than 90 currencies.
    image: "/logo.png",
    name: "Triket test",
    order_id: orderId, // Replace with Order ID generated in Step 4
    theme: {
      color: "#f0392a",
    },
    prefill: {
      contact: mobileNumber ?? "",
      name: username ?? "",
    },
    config: {
      display: {
        blocks: {
          // netbanking: false,
          upi: {
            name: "Pay with UPI",
            instruments: [
              {
                method: "upi",
                flows: ["qr", "collect", "intent"],
                apps: [
                  "google_pay",
                  "paytm",
                  "phonepe",
                  "bhim",
                  "amazon",
                  "airtel",
                  "sbi",
                  "imobile",
                ],
              },
            ],
          },
          card: {
            name: "Pay with card",
            instruments: [
              {
                method: "card",
              },
            ],
          },
          wallet: {
            name: "Pay with wallet",
            instruments: [
              {
                method: "wallet",
              },
            ],
          },
        },
        sequence: ["block.upi", "card", "wallet"],
        preferences: {
          show_default_blocks: true,
        },
      },
    },
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    handler: (response: any) => {
      setLoading(true)
      // reset data first
      if (handlerCallback) handlerCallback()

      const queryParams = new URLSearchParams()
      queryParams.append("order_id", response.razorpay_order_id)
      queryParams.append("payment_id", response.razorpay_payment_id)

      window.location.href = `/payment-success?${queryParams.toString()}`
      // setLoading(false)
      // navigate({
      //   pathname: "/payment-success",
      //   search: "?" + queryParams.toString(),
      // })
    },
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const razorpay = new (window as any).Razorpay(options)

  razorpay.once("ready", function () {
    // setOpen(false)
    setLoading(false)
    razorpay.open()
  })

  razorpay.on("payment.failed", function () {
    razorpay.close()
    if (handlerCallback) handlerCallback()
    // form.reset()
  })

  razorpay.on("payment.error", function () {
    razorpay.close()
    if (handlerCallback) handlerCallback()
    // form.reset()
  })
}
