import { create } from "zustand"

import type { EventTicketType } from "@/__generated__/graphql"

interface StaffState {
  selectedEvent: {
    id: string
    name: string
    eventTicketTypes: (Partial<EventTicketType> | null)[]
  }
  setSelectedEvent: (newState: {
    id: string
    name: string
    eventTicketTypes: (Partial<EventTicketType> | null)[]
  }) => void
}

const useStaffStore = create<StaffState>((set) => ({
  selectedEvent: {
    id: "",
    name: "Select event",
    eventTicketTypes: [], // Initialize as an empty array
  },
  setSelectedEvent: (newState) => {
    set(() => ({
      selectedEvent: {
        id: newState.id,
        name: newState.name,
        eventTicketTypes: newState.eventTicketTypes, // Update the eventTicketTypes array
      },
    }))
  },
}))

export default useStaffStore
