import { createCookieSessionStorage } from "react-router";

type SessionData = {
  role: string
  token: string
}

type SessionFlashData = {
  error: boolean
  orderId: string
}

const { getSession, commitSession, destroySession } =
  createCookieSessionStorage<SessionData, SessionFlashData>({
    cookie: {
      name: "__triket_session",
      secrets: ["tr1kets3cre1"],
      secure:
        process.env.NODE_ENV === "production" ||
        process.env.NODE_ENV === "test",
      path: "/",
      httpOnly: true,
      maxAge: 31536000000, // 1 year
    },
  })

export { getSession, commitSession, destroySession }
