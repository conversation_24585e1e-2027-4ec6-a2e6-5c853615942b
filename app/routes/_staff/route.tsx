import { redirect, Outlet, useLoaderData } from "react-router"

import { <PERSON><PERSON>, <PERSON><PERSON> } from "@/components/common"
import { destroySession, getSession } from "@/sessions"
import type { Route } from "./+types/route"

export async function loader({ request }: Route.LoaderArgs) {
  const session = await getSession(request.headers.get("Cookie"))

  const role = session.get("role")

  if (role === "staff" || role === "admin") {
    return { role }
  } else {
    return redirect("/login", {
      headers: {
        "Set-Cookie": await destroySession(session),
      },
    })
  }
}

const AdminStaffLayout = () => {
  const loaderData = useLoaderData<typeof loader>()
  return (
    <div className="flex h-full min-h-screen flex-col bg-white">
      <Header role={loaderData.role} />
      <main className="grow px-2">
        <Outlet />
      </main>
      <Footer />
    </div>
  )
}

export default AdminStaffLayout
