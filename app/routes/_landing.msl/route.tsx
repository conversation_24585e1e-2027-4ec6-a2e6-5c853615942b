import { PastEventsList } from "@/features/Home"
import MslUpcomingMatchList from "@/routes/_landing.msl/msl-upcoming-match-list"
import type { MetaFunction } from "react-router"

export const meta: MetaFunction = () => {
  const description = "Mizoram Super League | Season X"
  const title = "Triket - MSL"
  const url = "https://triket.in/msl"
  return [
    { title: title },
    {
      name: "description",
      content: description,
    },
    {
      property: "og:title",
      content: "Triket",
    },
    {
      property: "og:url",
      content: url,
    },
    {
      property: "og:type",
      content: "website",
    },
    {
      property: "og:image",
      itemProp: "image",
      content: "/msl_teams.png",
    },

    {
      property: "og:description",
      content: description,
    },
    {
      property: "robots",
      content: "index,follow",
    },
    {
      property: "twitter:card",
      content: "summary_large_image",
    },
    {
      property: "twitter:url",
      content: url,
    },
    {
      property: "twitter:title",
      content: title,
    },
    {
      property: "twitter:description",
      content: description,
    },
    {
      property: "og:site_name",
      content: title,
    },
  ]
}
const MSL = () => {
  return (
    <div className="grid grid-cols-12">
      <div className="col-span-12 mx-auto my-8 flex w-full max-w-5xl flex-col gap-4 px-4 md:order-2 md:gap-16 md:px-10">
        <h2 className="px-8 text-center text-3xl md:text-5xl">
          MIZORAM SUPER LEAGUE | SEASON X
        </h2>
        <div className="flex aspect-16/9 size-full items-center">
          <img alt="MSL" src="/msl_teams.png" />
        </div>
      </div>
      <div className="order-1 col-span-12 md:order-3">
        <MslUpcomingMatchList />
        <PastEventsList type="msl" />
      </div>
    </div>
  )
}

export default MSL
