import type { GetEventsQuery } from "@/__generated__/graphql"
import GET_EVENTS from "@/lib/graphql/queries/get-events"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import { useInfiniteQuery } from "@tanstack/react-query"

const useGetMslEvents = () => {
  const graphql = getGraphqlClient()

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isError,
    isFetching,
    isFetchingNextPage,
    isLoading,
  } = useInfiniteQuery({
    getNextPageParam: (lastPage: GetEventsQuery) => {
      if (!lastPage.getEvents.paginatorInfo.hasMorePages) {
        return
      }
      return lastPage.getEvents.paginatorInfo.currentPage + 1
    },
    initialPageParam: 1,
    queryFn: async ({ pageParam = 1 }) =>
      await graphql.request(GET_EVENTS, {
        page: pageParam,
        eventTypes: ["MSL"],
        first: 10,
        sortOrder: "asc",
      }),
    queryKey: ["events-msl-list"],
  })

  return {
    data,
    isLoading,
    isError,
    fetchNextPage,
    hasNextPage,
    isFetching,
    isFetchingNextPage,
  }
}

export default useGetMslEvents
