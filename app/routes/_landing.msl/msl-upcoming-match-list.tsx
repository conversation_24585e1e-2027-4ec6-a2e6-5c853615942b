import { NavLink } from "react-router";
import dayjs from "dayjs"

import { <PERSON><PERSON>ge, <PERSON><PERSON><PERSON><PERSON>omponent, Rupee } from "@components/common"
import { baseUrl, parseTicketPrices } from "@lib/utils"
import type { SportTicketType } from "@/__generated__/graphql"
import { But<PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import useGetMslEvents from "@/routes/_landing.msl/use-get-msl-events"

const MslUpcomingMatchList = () => {
  const {
    data: mslData,
    fetchNextPage,
    hasNextPage,
    isError,
    isFetchingNextPage,
    isLoading,
  } = useGetMslEvents()

  const data = mslData?.pages.flatMap((page) => page?.getEvents.data) ?? []

  return (
    <>
      <section className="mx-auto my-8 flex max-w-5xl flex-col gap-4 px-4 md:my-8 md:gap-16 md:px-10">
        {data.length > 0 && (
          <h2 className="px-8 text-center text-3xl md:text-5xl">
            Upcoming Match
          </h2>
        )}
        <div className="relative grid grid-cols-2 gap-4 md:gap-16">
          {isLoading && (
            <>
              <div className="col-span-1 aspect-3/4">
                <Skeleton className="h-full" />
              </div>
              <div className="col-span-1 aspect-3/4">
                <Skeleton className="h-full" />
              </div>
            </>
          )}
          {isError && (
            <div className="col-span-2">
              <ErrorComponent errorMessage="Unable to get upcoming matches" />
            </div>
          )}
          {!isError &&
            !isLoading &&
            data.map((item) => {
              let prices = [0]
              if (item?.sportTicketTypes != null) {
                prices = item?.sportTicketTypes
                  ? parseTicketPrices(
                      item.sportTicketTypes as SportTicketType[]
                    )
                  : [0]
              }

              return (
                item && (
                  <div key={item.id} className="col-span-1">
                    <NavLink to={`/msl/${item.slug}`}>
                      <div className="mx-auto flex w-full flex-col">
                        <div className="flex cursor-pointer justify-center">
                          {item.images && item.images[0]?.path ? (
                            <div className="relative aspect-[4/5] w-full text-clip">
                              <Blurimage
                                objectFit="cover"
                                src={`${baseUrl}/image/medium/${item.images[0].path}`}
                                hash={item.images[0].hash || ""}
                                alt={item.name}
                              />
                            </div>
                          ) : null}
                        </div>
                        <h2 className="mt-1 text-left text-base font-semibold tracking-tight md:text-3xl">
                          {item.name}
                        </h2>

                        {item?.start_booking_date &&
                        dayjs().isBefore(item.start_booking_date) ? (
                          <>
                            <p className="text-xs text-gray-500">Coming soon</p>
                          </>
                        ) : (
                          <>
                            <p className="text-xs text-gray-500">
                              {dayjs(item?.start_date).format(
                                "Do MMM, YYYY | hh:mm a"
                              )}{" "}
                              <span className="hidden md:inline-block">
                                |{" "}
                                {prices.length > 1 ? (
                                  <>
                                    <Rupee /> {Math.min(...prices)} onwards
                                  </>
                                ) : Math.min(...prices) > 0 ? (
                                  <>
                                    <Rupee /> {Math.min(...prices)}
                                  </>
                                ) : (
                                  " Free booking"
                                )}
                              </span>
                            </p>

                            <p className="text-xs text-gray-500 md:hidden">
                              {prices.length > 1 ? (
                                <>
                                  <Rupee /> {Math.min(...prices)} onwards
                                </>
                              ) : Math.min(...prices) > 0 ? (
                                <>
                                  <Rupee /> {Math.min(...prices)}
                                </>
                              ) : (
                                " Free booking"
                              )}
                            </p>
                          </>
                        )}
                      </div>
                    </NavLink>
                  </div>
                )
              )
            })}
          {hasNextPage && (
            <div className="col-span-2 mt-8 flex justify-center">
              <Button
                isLoading={isFetchingNextPage}
                onClick={() => fetchNextPage()}
                variant="link"
              >
                Load More
              </Button>
            </div>
          )}
        </div>
      </section>
    </>
  )
}

export default MslUpcomingMatchList
