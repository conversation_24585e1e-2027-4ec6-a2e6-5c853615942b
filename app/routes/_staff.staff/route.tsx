import { NavLink } from "react-router"

import SelectEvent from "./select-event"

import { <PERSON><PERSON><PERSON>on, ErrorComponent } from "@components/common"
import { IconCheck, IconLoader, IconSearch, IconX } from "@components/icons"
import { useStaffStore } from "@lib/store"
import { Button, buttonVariants } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import useGetGuestList from "@/lib/hooks/use-get-guest-list"

// TODO: use searchParams to maintain state like selectedevent id
const Staff = () => {
  const selectedEvent = useStaffStore((state) => state.selectedEvent)

  const {
    data,
    isLoading,
    isError,
    handleTicketTypeChange,
    handleSearchChange,
    isFetchingNextPage,
    fetchNextPage,
    hasNextPage,
    total,
    searchGuestList,
  } = useGetGuestList(selectedEvent.id)

  const guestList =
    data?.pages.flatMap((page) =>
      page?.getGuestList.__typename === "GeneralGuestList"
        ? page?.getGuestList.data
        : null
    ) ?? []

  const sportsGuestList =
    data?.pages.flatMap((page) =>
      page.getGuestList?.__typename === "SportGuestList"
        ? page?.getGuestList.data
        : null
    ) ?? []

  return (
    <>
      {isError && (
        <div className="my-16">
          <ErrorComponent />
        </div>
      )}

      {!isError && (
        <div className="mx-auto flex max-w-md flex-col">
          <div className="flex">
            <BackButton />
            <h1 className="w-full text-center font-bold">Staffs</h1>
          </div>
          <div className="mt-8 flex w-full flex-col gap-y-4">
            <NavLink
              to={`/scan-ticket`}
              className={buttonVariants({ variant: "primaryBlue" })}
            >
              Scan ticket
            </NavLink>
          </div>

          <SelectEvent />

          <div className="relative my-4 w-full">
            <Input
              value={searchGuestList}
              onChange={(e) => handleSearchChange(e.target.value)}
              placeholder="Search by name or phone number"
              leftSection={
                isLoading ? (
                  <IconLoader className="animate-spin" />
                ) : (
                  <IconSearch />
                )
              }
              rightSection={
                searchGuestList && (
                  <button
                    onClick={() => handleSearchChange("")}
                    className="flex items-center justify-center"
                  >
                    <IconX />
                  </button>
                )
              }
            />

            {(sportsGuestList || guestList) && (
              <div className="mt-4 flex flex-col gap-y-2 bg-slate-100 p-4">
                <div className="flex items-center justify-between">
                  <h2 className="w-full font-bold">Guest List : {total}</h2>
                  {selectedEvent.eventTicketTypes &&
                    selectedEvent.eventTicketTypes.length > 1 && (
                      <Select onValueChange={handleTicketTypeChange}>
                        <SelectTrigger className="mt-4">
                          <SelectValue placeholder="Filter ticket type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="All">All</SelectItem>
                          {selectedEvent?.eventTicketTypes.map((item) => {
                            return (
                              item?.id && (
                                <SelectItem key={item.id} value={item.id}>
                                  {item?.ticket_type === "Default"
                                    ? "Guest"
                                    : item.ticket_type === "special_invitee"
                                      ? "Special Guest"
                                      : item?.ticket_type}
                                </SelectItem>
                              )
                            )
                          })}
                        </SelectContent>
                      </Select>
                    )}
                </div>
                {guestList?.map((item) => {
                  return (
                    item && (
                      <div
                        className="grid grid-cols-12 items-center"
                        key={item.id}
                      >
                        <span className="col-span-7">
                          {item.username || ""}
                        </span>
                        <span className="col-span-4">
                          {item.number_of_tickets} ticket(s)
                        </span>
                        <span className="col-span-1">
                          {item.scanned_at ? (
                            <IconCheck className="text-blue-400" />
                          ) : (
                            <span />
                          )}
                        </span>
                      </div>
                    )
                  )
                })}
                {sportsGuestList?.map((item) => {
                  return (
                    item && (
                      <div
                        className="grid grid-cols-12 items-center"
                        key={item.id}
                      >
                        <span className="col-span-7">
                          {item.username || ""}
                        </span>
                        <span className="col-span-4">
                          {item.number_of_tickets} ticket(s)
                        </span>
                        <span className="col-span-1">
                          {item.scanned_at ? (
                            <IconCheck className="text-blue-400" />
                          ) : (
                            <span />
                          )}
                        </span>
                      </div>
                    )
                  )
                })}
                {hasNextPage && (
                  <div className="col-span-12 mt-8 flex justify-center">
                    <Button
                      isLoading={isFetchingNextPage}
                      variant="link"
                      type="button"
                      onClick={() => fetchNextPage()}
                    >
                      Fetch More
                    </Button>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}
    </>
  )
}

export default Staff
