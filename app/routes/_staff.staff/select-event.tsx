import { graphql } from "@/__generated__"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { IconLoader } from "@/components/icons"
import { useStaffStore } from "@lib/store"
import { useQuery } from "@tanstack/react-query"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"

const STAFF_EVENTS_QUERY = graphql(`
  query StaffEvents {
    adminEvents {
      id
      name
      eventTicketTypes {
        id
        ticket_type
        price
        maximum_capacity
      }
    }
  }
`)

const SelectEvent = () => {
  const graphql = getGraphqlClient()

  const { isLoading, data } = useQuery({
    queryKey: ["staff-events"],
    queryFn: async () => {
      return await graphql.request(STAFF_EVENTS_QUERY)
    },
  })
  const setSelectedEvent = useStaffStore((state) => state.setSelectedEvent)

  const handleChange = (id: string) => {
    // handleSelectEvent(id)
    const selectedEvent = data?.adminEvents?.find((item) => item?.id === id)
    if (
      selectedEvent &&
      selectedEvent.eventTicketTypes &&
      selectedEvent.eventTicketTypes.length > 0
    ) {
      setSelectedEvent({
        id: selectedEvent.id,
        name: selectedEvent.name,
        eventTicketTypes: selectedEvent?.eventTicketTypes,
      })
    }
  }

  return (
    <>
      {isLoading && (
        <div className="mt-8 flex justify-center">
          <IconLoader className="size-4 animate-spin" />
        </div>
      )}
      {!isLoading && (
        <Select onValueChange={handleChange}>
          <SelectTrigger className="mt-8 w-full">
            <SelectValue placeholder="Select event" />
          </SelectTrigger>
          <SelectContent>
            {data?.adminEvents &&
              data.adminEvents.map((item) => {
                return (
                  item && (
                    <SelectItem key={item.id} value={item.id}>
                      {item.name}
                    </SelectItem>
                  )
                )
              })}
          </SelectContent>
        </Select>
      )}
    </>
  )
}

export default SelectEvent
