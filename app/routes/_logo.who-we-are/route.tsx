import { TicketLine } from "@components/ui"

const WhoWeAre = () => {
  return (
    <>
      <div className="mx-auto flex max-w-4xl flex-col gap-y-8 px-4 md:px-10">
        <h1 className="mt-10 text-3xl font-bold">Who we are</h1>
        <section className="flex flex-col gap-y-4 pb-10">
          <p>
            At Triket, we are passionate about bringing the world of
            entertainment and adventure closer to you. Our mission is to make
            your experience of discovering and booking tickets for your favorite
            events, concerts, shows, and attractions seamless and enjoyable. We
            are your one-stop destination for accessing a wide range of tickets,
            ensuring you never miss out on any event that piques your interest.
          </p>
          <h2 className="text-xl font-bold">Our Platform:</h2>
          <p>
            Triket is a user-friendly and secure online platform that connects
            event organizers, venues, and performers with enthusiasts like you.
            Our website is designed with simplicity and convenience in mind,
            enabling you to browse and book tickets effortlessly. Whether you're
            a music lover, a sports enthusiast, a theater buff, or an
            art connoisseur.
          </p>
          <h3 className="font-bold">Key Features:</h3>

          <p>
            <span className="font-bold">Dynamic QR Code Integration:</span> Say
            goodbye to the hassle of manual payment reconciliation. Triket
            integrates a dynamic unique QR code system for every ticket booked
            that gets embedded into an automatically generated E-ticket.
          </p>
          <p>
            <span className="font-bold">Automated Payment Records: </span>No
            more manual recording of payments! Triket automates the payment
            recording process, eliminating the need for organizers to
            painstakingly document transactions. Once a payment is received, the
            system logs it digitally, saving both time and effort.
          </p>
          <p>
            <span className="font-bold">Effortless Check-In: </span>
            At the check-in stage, we've streamlined the process for organizers.
            Forget about manually searching through records; Triket simplifies
            the check-in by seamlessly retrieving payment and attendee
            information, reducing tedious and time-consuming efforts.
          </p>
          <p>
            <span className="font-bold">Special Invitees: </span>
            You have a special guests you want to invite for your event? Enter
            their information and send an automatically generated E-ticket with
            a click of a button.
          </p>
          <p>
            <span className="font-bold">Real-time Updates: </span>
            Stay in control with real-time updates. Triket provides organizers
            with instant access to attendee data and payment statuses, allowing
            for quick decision-making and on-the-spot issue resolution.We at
            Triket are committed to revolutionizing event management. We aim to
            empower organizers by providing a comprehensive solution that not
            only addresses pain points but also enhances the overall event
            experience. Join us in embracing efficiency and innovation in the
            world of online ticketing.
          </p>
        </section>
      </div>
      <TicketLine noMargin={false} />
    </>
  )
}

export default WhoWeAre
