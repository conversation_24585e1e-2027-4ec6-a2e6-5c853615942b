import { graphql } from "@/__generated__"

export const GET_BOOKED_SEATS = graphql(`
  query GetBookedSeatsForSeatedEvent($eventId: ID!) {
    getBookedSeatsForSeatedEvent(event_id: $eventId)
  }
`)

export const CREATE_PAYMENT_FOR_SEAT_TICKETS = graphql(`
  mutation CreatePaymentForSeatTickets(
    $eventId: Int!
    $mobileNumber: String!
    $seatNumbers: [String!]!
    $tipAmount: Float
    $tipMessage: String
    $username: String!
  ) {
    createPaymentForSeatedTicket(
      event_id: $eventId
      mobile_number: $mobileNumber
      seat_numbers: $seatNumbers
      tip_amount: $tipAmount
      tip_message: $tipMessage
      username: $username
    ) {
      order_id
      payment_id
      provider_type
      goto_url
      username
      mobile_number
    }
  }
`)
