import { z } from "zod"

export const schema = z.object({
  ticketTypeId: z.number().min(1, "Required"),
  username: z.string().min(1, "Required").max(30, "Name is too long"),
  mobileNumber: z
    .string()
    .min(10, "Mobile number must be 10 digits")
    .max(10, "Mobile number must be 10 digits"),
  seatNumbers: z.array(z.string()).max(5, "Cannot select more than 5 seats"),
  tipAmount: z.coerce.number().optional(),
  tipMessage: z.string().optional(),
  numberOfTickets: z.number().optional(),
  checkTerms: z.literal<boolean>(true, {
    errorMap: () => ({
      message: "Please accept the terms and conditions to proceed",
    }),
  }),
})

export type FormType = z.infer<typeof schema>
