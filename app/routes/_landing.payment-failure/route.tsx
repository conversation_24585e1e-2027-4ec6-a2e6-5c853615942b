import { redirect, Link, useLoaderData } from "react-router"

import { commitSession, getSession } from "@/sessions"
import { deleteCookie } from "@/lib/utils"

import type { Route } from "./+types/route"

export async function loader({ request }: Route.LoaderArgs) {
  const session = await getSession(request.headers.get("Cookie"))

  const cookies = request.headers.get("Cookie")

  const splitCookies = cookies?.split("; ")

  let event: string | null = null

  if (splitCookies) {
    for (const cookie of splitCookies) {
      if (cookie.startsWith("payment-event=")) {
        event = cookie.substring("payment-event=".length)
        break
      }
    }
  }

  const orderId = session.get("orderId") || null
  const error = session.get("error") || false

  if (!error || !orderId) {
    return redirect("/", {
      headers: {
        "Set-Cookie": await commitSession(session),
      },
    })
  }

  return json(
    { orderId, event },
    {
      headers: {
        "Set-Cookie": await commitSession(session),
      },
    }
  )
}

const PaymentFailure = () => {
  const data = useLoaderData<typeof loader>()

  const handleBack = () => {
    deleteCookie("payment-event")
  }

  return (
    <>
      <div className="mx-auto flex max-w-5xl items-center justify-center px-4 py-16 md:px-0">
        <p className="text-3xl font-bold">
          Oops! there was a problem while generating your ticket(s).
          <span className="block pt-4 text-lg font-normal">
            If you have completed the payment but haven't received the ticket
            within 10 minutes, please contact us at{" "}
            <a
              href="tel:+918131884358"
              className=" inline-block font-bold text-primary-blue hover:underline"
            >
              8131884358
            </a>
          </span>
        </p>
      </div>
      <div className="my-4 flex flex-col items-center">
        <div>
          <div>
            Order number: <span className="font-bold">{data.orderId}</span>
          </div>
        </div>
        <div className="my-4">
          {data.event ? (
            <Link onClick={handleBack} to={data.event}>
              Go back
            </Link>
          ) : (
            <Link onClick={handleBack} to="/">
              Go back
            </Link>
          )}
        </div>
      </div>
    </>
  )
}

export default PaymentFailure
