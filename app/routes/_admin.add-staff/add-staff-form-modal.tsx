import { z } from "zod"
import { useForm } from "react-hook-form"
import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import toast from "react-hot-toast"
import { useState } from "react"

import { graphql } from "@/__generated__"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Button, buttonVariants } from "@/components/ui/button"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import { parseGraphqlError } from "@/lib/utils/parse-graphql-error"

const ADD_STAFF_MUTATION = graphql(`
  mutation AddStaff($username: String!, $password: String!) {
    addStaff(username: $username, password: $password) {
      id
    }
  }
`)

const schema = z
  .object({
    // eventID: z.string().optional(),
    username: z.string().min(1, "Username is required"),
    password: z.string().min(6, "Password must be at least 6 characters"),
    confirmPassword: z
      .string()
      .min(6, "Password must be at least 6 characters"),
  })
  .refine((data) => data.password === data.confirmPassword, {
    path: ["confirmPassword"],
    message: "Passwords do not match",
  })

type FormSchemaType = z.infer<typeof schema>

const AddStaffFormModal = () => {
  const [open, setOpen] = useState(false)
  const form = useForm<FormSchemaType>({
    resolver: zodResolver(schema),
  })
  const graphql = getGraphqlClient()
  const queryClient = useQueryClient()

  const closeModal = () => {
    setOpen(false)
  }

  const addStaff = useMutation({
    mutationFn: async (data: FormSchemaType) => {
      return await graphql.request(ADD_STAFF_MUTATION, {
        username: data.username,
        password: data.password,
      })
    },
  })

  const handleAddStaff = (data: FormSchemaType) => {
    addStaff.mutate(data, {
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: ["get-staffs"],
        })
        toast.success("Staff added successfully")
        closeModal()
        form.reset()
      },
      onError: (error) => {
        toast.error(parseGraphqlError(error))
      },
    })
  }

  return (
    <>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger className={buttonVariants({ variant: "outline" })}>
          Add Staff
        </DialogTrigger>
        <DialogContent>
          <DialogHeader className="text-left font-bold">Add Staff</DialogHeader>
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(handleAddStaff)}
              className="flex flex-col gap-y-6"
            >
              {/* <FormField */}
              {/*   control={form.control} */}
              {/*   name="eventID" */}
              {/*   render={() => ( */}
              {/*     <FormItem> */}
              {/*       <FormLabel>Event</FormLabel> */}
              {/*       <Select onValueChange={handleSelectEvent}> */}
              {/*         <FormControl> */}
              {/*           <SelectTrigger type="button"> */}
              {/*             <SelectValue placeholder="Select event" /> */}
              {/*           </SelectTrigger> */}
              {/*         </FormControl> */}
              {/*         <SelectContent> */}
              {/*           {data?.myEvents.data.map((item) => ( */}
              {/*             <SelectItem key={item.id} value={item.id}> */}
              {/*               {item.name} */}
              {/*             </SelectItem> */}
              {/*           ))} */}
              {/*         </SelectContent> */}
              {/*       </Select> */}
              {/*     </FormItem> */}
              {/*   )} */}
              {/* /> */}
              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Staff name</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Username" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Password</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="password"
                        placeholder="Password"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Confirm Password</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="password"
                        placeholder="Confirm password"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="flex justify-end">
                <Button
                  isLoading={addStaff.isPending}
                  type="submit"
                  variant="primaryBlue"
                >
                  Add Staff
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  )
}

export default AddStaffFormModal
