import { useState } from "react"
import toast from "react-hot-toast"

import AddStaffFormModal from "./add-staff-form-modal"

import { BackButton, ErrorComponent } from "@/components/common"
import { Loader } from "@/components/ui"
import { Button } from "@/components/ui/button"
import { graphql } from "@/__generated__"
import { IconTrash } from "@/components/icons"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
} from "@/components/ui/dialog"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import { parseGraphqlError } from "@/lib/utils/parse-graphql-error"

const GET_STAFFS = graphql(`
  query GetStaffs {
    getStaffs {
      id
      identifier
      role
    }
  }
`)

const DELETE_STAFF = graphql(`
  mutation DeleteStaff($id: ID!) {
    deleteStaff(id: $id) {
      message
    }
  }
`)

const AddStaff = () => {
  const [openDelete, setOpenDelete] = useState({
    id: "",
    open: false,
  })
  const graphql = getGraphqlClient()
  const queryClient = useQueryClient()

  const closeDeleteModal = () => {
    setOpenDelete((prev) => {
      return {
        ...prev,
        open: false,
      }
    })
  }

  const { data, isLoading, isError } = useQuery({
    queryKey: ["get-staffs"],
    queryFn: async () => {
      return await graphql.request(GET_STAFFS)
    },
  })

  const deleteStaff = useMutation({
    mutationFn: async (id: string) => {
      return await graphql.request(DELETE_STAFF, {
        id: id,
      })
    },
  })

  const handleDeleteStaff = () => {
    if (openDelete.id) {
      deleteStaff.mutate(openDelete.id, {
        onSuccess: () => {
          toast.success("Staff deleted successfully")
          queryClient.invalidateQueries({
            queryKey: ["get-staffs"],
          })
          closeDeleteModal()
        },
        onError: (error) => {
          toast.error(parseGraphqlError(error))
          closeDeleteModal()
        },
      })
    }
  }

  const handleOnOpenChange = (open: boolean) => {
    setOpenDelete((prev) => {
      return {
        ...prev,
        id: "",
        open: open,
      }
    })
  }

  const staffList =
    data?.getStaffs &&
    data?.getStaffs.map((item, index) => {
      return (
        <li key={item?.id} className="border-b py-2">
          <p className="flex items-center justify-between">
            <span>
              {index + 1}) {item?.identifier}
            </span>
            {item?.id && (
              <button
                onClick={() =>
                  setOpenDelete((prev) => {
                    return {
                      ...prev,
                      id: item.id,
                      open: true,
                    }
                  })
                }
              >
                <IconTrash className="text-lg text-red-700" />
              </button>
            )}
          </p>
        </li>
      )
    })

  return (
    <>
      {isLoading && (
        <div className="my-16 flex justify-center">
          <Loader variant="large" />
        </div>
      )}

      {isError && (
        <div className="my-16">
          <ErrorComponent />
        </div>
      )}

      {!isLoading && !isError && (
        <>
          <div className="flex max-w-lg flex-col md:mx-auto">
            <div className="flex">
              <BackButton />
              <h1 className="w-full text-center font-bold">My Staff List</h1>
            </div>
            <div className="mt-4">
              <AddStaffFormModal />
            </div>
            <ul className="mt-4 flex flex-col">{staffList}</ul>
          </div>
        </>
      )}
      <Dialog open={openDelete.open} onOpenChange={handleOnOpenChange}>
        <DialogContent>
          <DialogHeader className="text-left font-bold">
            Delete staff?
          </DialogHeader>
          <DialogFooter>
            <Button
              onClick={handleDeleteStaff}
              isLoading={deleteStaff.isPending}
              variant="destructive"
            >
              Yes
            </Button>
            <Button onClick={closeDeleteModal} variant="secondary">
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}

export default AddStaff
