import { useForm } from "react-hook-form"
import toast from "react-hot-toast"
import type { ActionFunctionArgs, LoaderFunctionArgs } from "react-router"
import { z } from "zod"
import { data, NavLink, redirect, useFetcher } from "react-router"
import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod"

import { graphql } from "@/__generated__"
import { Input } from "@/components/ui/input"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Button } from "@/components/ui/button"
import { commitSession, destroySession, getSession } from "@/sessions"
import { useMutation } from "@tanstack/react-query"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import { parseGraphqlError } from "@/lib/utils/parse-graphql-error"

const schema = z.object({
  identifier: z.string().min(1, "Username is required"),
  password: z.string().min(1, "Password is required"),
})

type FormSchemaType = z.infer<typeof schema>

const LOGIN_MUTATION = graphql(`
  mutation Login($identifier: String!, $password: String!) {
    adminLogin(username: $identifier, password: $password) {
      user {
        id
        identifier
        role
      }
      token
    }
  }
`)

export async function action({ request }: ActionFunctionArgs) {
  const session = await getSession(request.headers.get("Cookie"))
  const formData = await request.formData()

  const role = formData.get("role")?.toString()
  const token = formData.get("token")?.toString()

  if (!role || !token) {
    return data(
      { error: "Invalid session data" },
      {
        headers: {
          "Set-Cookie": await destroySession(session),
        },
      }
    )
  }

  session.set("role", role)
  session.set("token", token)

  if (role === "admin") {
    return redirect("/my-events", {
      headers: {
        "Set-Cookie": await commitSession(session),
      },
    })
  }
  if (role === "staff") {
    return redirect("/staff", {
      headers: {
        "Set-Cookie": await commitSession(session),
      },
    })
  }
  if (role === "super-admin") {
    return redirect("/super-admin", {
      headers: {
        "Set-Cookie": await commitSession(session),
      },
    })
  }
  if (role === "triket-support") {
    return redirect("/support", {
      headers: {
        "Set-Cookie": await commitSession(session),
      },
    })
  }
}

export async function loader({ request }: LoaderFunctionArgs) {
  const session = await getSession(request.headers.get("Cookie"))

  const role = session.get("role")

  if (role === "admin") {
    return redirect("/my-events")
  }
  if (role === "staff") {
    return redirect("/staff")
  }
  if (role === "super-admin") {
    return redirect("/super-admin")
  }
  if (role === "triket-support") {
    return redirect("/support")
  }

  return null
}

const Login = () => {
  const fetcher = useFetcher()
  const graphql = getGraphqlClient()

  const form = useForm<FormSchemaType>({
    resolver: zodResolver(schema),
    defaultValues: {
      identifier: "",
      password: "",
    },
  })

  const userLogin = useMutation({
    mutationFn: async (data: FormSchemaType) => {
      return await graphql.request(LOGIN_MUTATION, {
        identifier: data.identifier,
        password: data.password,
      })
    },
  })

  const handleLogin = (data: FormSchemaType) => {
    userLogin.mutate(data, {
      onSuccess: (data) => {
        const formData = new FormData()
        // const token = data.userLogin.
        const role = data?.adminLogin?.user?.role ?? ""
        const token = data?.adminLogin?.token ?? ""

        formData.append("role", role)
        formData.append("token", token)

        void fetcher.submit(formData, {
          method: "POST",
        })
      },
      onError: (error) => {
        toast.error(parseGraphqlError(error))
      },
    })
  }

  return (
    <div className="mx-auto flex h-full min-h-screen max-w-lg flex-col items-center justify-center text-gray-700">
      <NavLink to="/">
        <img
          src="/logo.png"
          alt="logo"
          className="mx-auto mb-8 flex justify-center"
        />
      </NavLink>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(handleLogin)}
          className="flex w-2/3 flex-col gap-y-6"
        >
          <FormField
            control={form.control}
            name="identifier"
            render={({ field }) => {
              return (
                <FormItem>
                  <FormLabel>Username</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Enter username" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )
            }}
          />
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => {
              return (
                <FormItem>
                  <FormLabel>Password</FormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      {...field}
                      placeholder="Enter password"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )
            }}
          />
          <div className="flex justify-center">
            <Button
              type="submit"
              isLoading={userLogin.isPending}
              className="w-full"
            >
              Login
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}

export default Login
