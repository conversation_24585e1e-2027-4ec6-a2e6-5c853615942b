import { zod<PERSON><PERSON><PERSON><PERSON> } from "@hookform/resolvers/zod"
import { NavLink, useNavigate } from "react-router"
import { useForm } from "react-hook-form"
import { z } from "zod"
import toast from "react-hot-toast"

import { REFUND_EVENTS } from "@/lib/utils/constants"
import { graphql } from "@/__generated__"
import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useMutation } from "@tanstack/react-query"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import { parseGraphqlError } from "@/lib/utils/parse-graphql-error"
import type { Route } from "./+types/route"

const USER_REQUEST_REFUND = graphql(`
  mutation UserRequestRefund($eventId: ID!, $phoneNumber: String!) {
    userRequestRefund(event_id: $eventId, phone_number: $phoneNumber) {
      message
    }
  }
`)

const schema = z.object({
  eventId: z.string().min(1, "Please select an event"),
  phoneNumber: z.string().min(1, "Phone number is required"),
})

type FormSchemaType = z.infer<typeof schema>

export const meta = ({ location }: Route.MetaArgs) => {
  return [
    { title: `Triket - Request refund` },
    {
      name: "description",
      content:
        "Triket is a user-friendly and secure online platform that connects event organizers, venues, and performers with enthusiasts alike",
    },
    {
      property: "og:url",
      content: `https://triket.arsi.in${location.pathname}`,
    },
    {
      property: "og:image",
      itemProp: "image",
      content: "/logo.png",
    },
  ]
}

const RequestRefund = () => {
  const navigate = useNavigate()
  const form = useForm<FormSchemaType>({
    resolver: zodResolver(schema),
    defaultValues: {
      eventId: "",
      phoneNumber: "",
    },
  })
  const graphql = getGraphqlClient()

  const requestRefund = useMutation({
    mutationFn: async (data: FormSchemaType) => {
      return await graphql.request(USER_REQUEST_REFUND, {
        ...data,
      })
    },
  })

  const handleRequestRefund = async (data: FormSchemaType) => {
    requestRefund.mutate(data, {
      onSuccess: () => {
        navigate("submitted")
      },
      onError: (error) => {
        toast.error(parseGraphqlError(error))
      },
    })
  }

  return (
    <>
      <div className="mx-auto flex max-w-4xl flex-col gap-y-4 px-4 md:px-10">
        <h1 className="mt-10 text-3xl font-bold">Request refund</h1>
        <p>
          Please fill out the form below to process your refund. The details
          should match the information entered at the time of ticket purchase.
        </p>
        <p>
          For more information, please refer to our{" "}
          <NavLink
            className="text-primary-blue"
            to="/terms-and-conditions#refunds"
          >
            refund policy.
          </NavLink>
        </p>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleRequestRefund)}
            className="my-8 space-y-4 md:w-1/2"
          >
            <FormField
              control={form.control}
              name="eventId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Select event</FormLabel>
                  <Select onValueChange={field.onChange}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select event" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {REFUND_EVENTS.map((item) => (
                        <SelectItem key={item.id} value={item.id}>
                          {item.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="phoneNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Phone number</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Phone number" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button
              variant="primaryBlue"
              isLoading={requestRefund.isPending}
              type="submit"
            >
              Request Refund
            </Button>
          </form>
        </Form>
      </div>
    </>
  )
}

export default RequestRefund
