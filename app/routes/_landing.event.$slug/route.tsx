import dayjs from "dayjs"
import { useN<PERSON>gate, useLoaderData, redirect } from "react-router"

import { Rupee } from "@components/common"
import { Carousel } from "@components/common/Carousel"
import { IconLeft, IconLocation, IconMic, IconShare } from "@components/icons"
import { DottedLine, TicketLine } from "@components/ui"
import PaymentMenu from "@features/PaymentMenu"
import { GET_EVENT_BY_SLUG } from "@lib/graphql/queries"
import { baseUrl, parseTicketPrices } from "@lib/utils"
import type { AppImage, EventTicketType } from "@/__generated__/graphql"
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import { handleCopyToClipboard } from "@/lib/utils/handle-copy-to-clipboard"

import type { Route } from "./+types/route"
import { ClientError } from "graphql-request"

export const meta = ({ data, location }: Route.MetaArgs) => {
  const eventData = data?.eventData
  const imagePath =
    eventData?.images && eventData?.images.length
      ? eventData?.images[0]?.path
      : null
  //!TODO placeholder for images
  return [
    {
      title: `${eventData?.name} - ${eventData?.organizer_name}`,
    },
    {
      name: "description",
      content: eventData?.description,
    },
    {
      property: "og:title",
      content: `${eventData?.name} - ${eventData?.organizer_name}`,
    },
    {
      property: "og:url",
      content: `https://triket.arsi.in${location.pathname}`,
    },
    {
      property: "og:type",
      content: "website",
    },
    {
      property: "og:image",
      itemProp: "image",
      content: imagePath ? `${baseUrl}/image/thumbnail/${imagePath}` : null,
    },
    {
      property: "og:image:height",
      content: "600",
    },
    {
      property: "og:image:width",
      content: "600",
    },
    {
      property: "robots",
      content: "index,follow",
    },
    {
      property: "og:description",
      content: eventData?.description,
    },
    {
      property: "twitter:card",
      content: "summary_large_image",
    },
    {
      property: "twitter:url",
      content: `https://triket.arsi.in${location.pathname}`,
    },
    {
      property: "twitter:title",
      content: `${eventData?.organizer_name} - ${eventData?.name}`,
    },
    {
      property: "twitter:description",
      content: eventData?.description,
    },
    {
      property: "twitter:image",
      content: imagePath ? `${baseUrl}/image/medium/${imagePath}` : null,
    },
    {
      property: "og:site_name",
      content: "Triket",
    },
  ]
}

export async function loader({ params }: Route.LoaderArgs) {
  const graphqlClient = getGraphqlClient()

  try {
    const eventSlug = await graphqlClient.request(GET_EVENT_BY_SLUG, {
      slug: params.slug!,
    })

    if (eventSlug.eventBySlug === null) {
      throw redirect("/")
    } else if (eventSlug.eventBySlug?.type !== "general") {
      return redirect(`/msl/${params.slug}`)
    } else {
      return { eventData: eventSlug.eventBySlug }
    }
  } catch (error) {
    if (error instanceof ClientError && error?.response?.status === 429) {
      return redirect(`/too-many-requests?ref=/event/${params.slug}`)
    }
    throw error
  }
}

const EventBySlug = () => {
  const navigate = useNavigate()
  const loaderData = useLoaderData<typeof loader>()
  const data = loaderData.eventData

  // convert the string faq into an array of object
  const parsedFaq = data?.faq ? JSON.parse(data.faq) : []

  // get event prices in an array
  const prices = data?.eventTicketTypes
    ? parseTicketPrices(data.eventTicketTypes as EventTicketType[])
    : [0]

  return (
    <>
      <div className="mx-auto flex h-full min-h-[92vh] max-w-3xl flex-col px-4 pt-4">
        <div className="flex grow flex-col">
          <div className="flex">
            <button
              type="button"
              onClick={() => navigate(-1)}
              aria-label="go back"
            >
              <IconLeft className="text-2xl" />
            </button>
          </div>

          <div className="mt-4 pb-6 text-gray-700">
            <div className="relative pb-6">
              {data?.images && <Carousel data={data?.images as AppImage[]} />}
            </div>
          </div>

          {/* <div className="flex flex-row justify-between"> */}
          <div className="grid grid-cols-12">
            <div className="col-span-12 flex flex-col md:col-span-10">
              <h1 className="text-2xl font-bold leading-6 text-gray-700 md:text-4xl">
                {data?.name?.toUpperCase()}
              </h1>
              <p className="text-gray-500">{data?.address}</p>
              <p className="text-gray-500">
                {dayjs(data?.start_date).format("Do MMM, YYYY | hh:mm a")}
              </p>
              <p className="font-bold text-gray-500 md:hidden">
                {prices.length > 1 ? (
                  <>
                    <Rupee /> {Math.min(...prices)} onwards
                  </>
                ) : Math.min(...prices) > 0 ? (
                  <>
                    <Rupee /> {Math.min(...prices)}
                  </>
                ) : (
                  <>Free booking</>
                )}
              </p>
              <div className="mt-4">
                <button
                  onClick={handleCopyToClipboard}
                  className="flex items-center gap-x-2 text-primary-blue"
                >
                  <IconShare /> Share this event
                </button>
              </div>
              {data?.location ? (
                <div className="mt-4">
                  <a
                    className="flex cursor-pointer items-center gap-x-2 text-red-400"
                    rel="noreferrer"
                    href={data.location}
                    target="_blank"
                  >
                    <IconLocation /> View location on map
                  </a>
                </div>
              ) : null}
            </div>

            {/* Show coming soon if start_booking_date is setup */}
            {data?.start_booking_date &&
            dayjs().isBefore(data.start_booking_date) ? (
              <div className="col-span-2 hidden md:block">
                <button disabled>
                  <div className="relative size-full">
                    <span className="absolute flex size-full items-center justify-center font-bold leading-3">
                      Coming Soon
                    </span>
                    <img
                      className="w-32"
                      src="/buy-btn.png"
                      alt="Coming soon"
                    />
                  </div>
                </button>
              </div>
            ) : // Show event ended if current date is after end date
            data?.end_date && dayjs().isAfter(data.end_date) ? (
              <div className="col-span-2 hidden md:block">
                <button disabled>
                  <div className="relative size-full">
                    {/* Show event ended if current date is after end date */}
                    <span className="absolute flex size-full items-center justify-center font-bold leading-3">
                      Event Ended
                    </span>
                    <img
                      className="w-32"
                      src="/buy-btn.png"
                      alt="Event ended"
                    />
                  </div>
                </button>
              </div>
            ) : (
              <div className="col-span-2 hidden md:block">
                <PaymentMenu
                  eventId={data?.id || ""}
                  data={data?.eventTicketTypes as EventTicketType[]}
                  seatingPlanImagePath={data?.seatingPlan?.path}
                  showDiscount={data?.coupons?.length > 0 ? true : false}
                  isMsl={data?.slug.startsWith("msl")}
                />
                <p className="text-center font-bold text-gray-500">
                  {prices.length > 1 ? (
                    <>
                      <Rupee /> {Math.min(...prices)} onwards
                    </>
                  ) : Math.min(...prices) > 0 ? (
                    <>
                      <Rupee /> {Math.min(...prices)} onwards
                    </>
                  ) : (
                    <>Free Booking</>
                  )}
                </p>
              </div>
            )}
          </div>

          <DottedLine />

          <div className="flex flex-col gap-y-4">
            <h2 className="text-2xl">ABOUT</h2>
            <div className="whitespace-pre-line text-gray-500">
              {data?.description}
            </div>
          </div>

          {data?.artists && data.artists.length > 0 && (
            <div className="mt-8 flex flex-col gap-y-4">
              <h2 className="text-2xl">ARTISTS</h2>
              <div className="grid grid-cols-4 gap-4">
                {data.artists.map((item) => {
                  return item?.social_link ? (
                    <a
                      key={item.id}
                      href={item.social_link}
                      target="_blank"
                      rel="noreferrer noopener"
                      aria-label="Artist social account link"
                      className="col-span-1"
                    >
                      <div className="relative flex h-auto w-full justify-center">
                        <img
                          // className="h-20 w-20 rounded-full object-cover md:h-40 md:w-40"
                          className="size-16 rounded-full object-cover xs:size-20 md:size-40"
                          src={`${baseUrl}/image/medium/${item?.avatar}`}
                          alt={`${item?.name} - profile`}
                        />
                      </div>
                      <p className="mt-2 text-center text-xs text-gray-500 md:text-sm">
                        {item?.name}
                      </p>
                    </a>
                  ) : (
                    <div className="col-span-1 flex flex-col" key={item?.id}>
                      <div className="relative flex h-auto w-full justify-center">
                        <img
                          className="size-16 rounded-full object-cover xs:size-20 md:size-40"
                          src={`${baseUrl}/image/medium/${item?.avatar}`}
                          alt={`${item?.name} - profile`}
                        />
                      </div>
                      <p className="mt-2 text-center text-xs text-gray-500 md:text-sm">
                        {item?.name}
                      </p>
                    </div>
                  )
                })}
              </div>
            </div>
          )}

          <DottedLine />

          {data?.open_mic_link ? (
            <a
              target="_blank"
              rel="noopener noreferrer"
              href={data.open_mic_link}
              className="flex items-center gap-x-2 text-primary-blue"
              aria-label="Sign up for open mic document link"
            >
              <IconMic /> Sign up for open mic
            </a>
          ) : null}

          {data?.open_mic_link && <DottedLine />}

          {parsedFaq.length > 0 && (
            <Accordion collapsible type="single" defaultValue="faq">
              <AccordionItem value="faq" className="border-b-0">
                <AccordionTrigger>FAQs</AccordionTrigger>
                <AccordionContent>
                  {parsedFaq.map((item: any, index: number) => {
                    return (
                      <div className="pb-2 pt-4" key={index}>
                        <div>
                          {index + 1}) {item.question}
                        </div>
                        <div className="w-full pl-4 text-gray-500">
                          {item.answer}
                        </div>
                      </div>
                    )
                  })}
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          )}
          <TicketLine />
        </div>

        {/* if start booking date is not available */}
        {/* mobile sticky floater */}
        {data?.start_booking_date &&
        dayjs().isBefore(data.start_booking_date) ? (
          <div className="sticky bottom-0 flex justify-center bg-white py-4 md:hidden">
            <button disabled>
              <div className="relative size-full">
                <span className="absolute flex size-full items-center justify-center font-bold leading-3">
                  Coming Soon
                </span>
                <img className="w-32" src="/buy-btn.png" alt="Coming soon" />
              </div>
            </button>
          </div>
        ) : data?.end_date && dayjs().isAfter(data.end_date) ? (
          <div className="sticky bottom-0 flex justify-center bg-white py-4 md:hidden">
            <button disabled>
              <div className="relative size-full">
                <span className="absolute flex size-full items-center justify-center font-bold leading-3">
                  Event Ended
                </span>
                <img className="w-32" src="/buy-btn.png" alt="Coming soon" />
              </div>
            </button>
          </div>
        ) : (
          <div className="sticky bottom-0 bg-white py-4 md:hidden">
            <div className="flex justify-between">
              <PaymentMenu
                data={data?.eventTicketTypes as EventTicketType[]}
                eventId={data?.id || ""}
                seatingPlanImagePath={data?.seatingPlan?.path}
                showDiscount={data?.coupons?.length > 0 ? true : false}
                isMsl={data?.slug.startsWith("msl")}
              />
            </div>
          </div>
        )}
      </div>
    </>
  )
}

export default EventBySlug
