import { useParams } from "react-router";
import { useState } from "react"

import { baseUrl } from "@/lib/utils"
import type { EventByIdQuery } from "@/__generated__/graphql"
import { IconCheck, IconLoader, IconSearch, IconX } from "@/components/icons"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { GuestListMenu, Rupee } from "@/components/common"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import useGetGuestList from "@/lib/hooks/use-get-guest-list"

interface Props {
  eventData: EventByIdQuery
}

const GuestList = ({ eventData }: Props) => {
  const { id } = useParams()

  const [tips, setTips] = useState({
    open: false,
    tipAmount: 0,
    tipMessage: "",
  })

  const {
    data,
    isLoading,
    isError,
    isFetchingNextPage,
    fetchNextPage,
    handleTicketTypeChange,
    searchGuestList,
    handleSearchChange,
    hasNextPage,
    total,
  } = useGetGuestList(id!)

  const handleTipDialog = ({
    tipMessage,
    tipAmount,
  }: {
    tipMessage: string
    tipAmount: number
  }) => {
    setTips({
      open: true,
      tipAmount,
      tipMessage: tipMessage,
    })
  }

  const handleTipClose = (open: boolean) => {
    if (open === false) {
      setTips({
        open: false,
        tipAmount: 0,
        tipMessage: "",
      })
    }
  }

  if (isError) {
    return (
      <div className="mt-4 flex w-full flex-col items-center">
        <h2 className="text-lg font-bold">Error</h2>
        <p>Cannot get guest list</p>
      </div>
    )
  }

  const guestList =
    data?.pages.flatMap((page) =>
      page?.getGuestList.__typename === "GeneralGuestList"
        ? page?.getGuestList.data
        : null
    ) ?? []
  const sportsGuestList =
    data?.pages.flatMap((page) =>
      page.getGuestList?.__typename === "SportGuestList"
        ? page?.getGuestList.data
        : null
    ) ?? []

  return (
    <>
      <div className="relative mt-4 w-full">
        <Input
          value={searchGuestList}
          onChange={(e) => handleSearchChange(e.target.value)}
          placeholder="Search by name or phone number"
          leftSection={
            isLoading ? <IconLoader className="animate-spin" /> : <IconSearch />
          }
          rightSection={
            searchGuestList && (
              <button
                onClick={() => handleSearchChange("")}
                className="flex items-center justify-center"
              >
                <IconX />
              </button>
            )
          }
        />
      </div>
      <div className="mt-4 flex flex-col gap-y-2 bg-slate-100 p-4 text-black">
        <div className="mb-4 flex flex-col items-center justify-between">
          <h2 className="w-full text-left font-bold">Guest List : {total}</h2>
          {eventData?.eventById?.eventTicketTypes &&
            eventData?.eventById?.eventTicketTypes.length > 1 && (
              <Select onValueChange={handleTicketTypeChange}>
                <SelectTrigger className="mt-4">
                  <SelectValue placeholder="Filter ticket type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="All">All</SelectItem>
                  {eventData?.eventById.eventTicketTypes.map((item) => {
                    return (
                      item && (
                        <SelectItem key={item?.id} value={item.id}>
                          {item?.ticket_type === "Default"
                            ? "Guest"
                            : item.ticket_type === "special_invitee"
                              ? "Special Guest"
                              : item?.ticket_type}
                        </SelectItem>
                      )
                    )
                  })}
                </SelectContent>
              </Select>
            )}
        </div>
        {sportsGuestList?.map((item) => {
          let downloadUrl = "#"
          if (
            item &&
            (item.paymentOrder?.paymentable?.__typename ===
              "RzpayPaymentOrder" ||
              item.paymentOrder?.paymentable?.__typename ===
                "PhonePePaymentOrder") &&
            item.paymentOrder?.paymentable?.order_id &&
            item.paymentOrder?.paymentable?.payment_id
          ) {
            downloadUrl = `${baseUrl}/download-ticket?order_id=${item.paymentOrder.paymentable.order_id}&payment_id=${item.paymentOrder.paymentable.payment_id}`
          }

          return (
            item && (
              <div
                className="grid grid-cols-12 items-center border-b pb-2"
                key={item.id}
              >
                <span className="col-span-7 whitespace-pre-wrap">
                  {item.username || ""}{" "}
                  {item.mobile_number ? `(${item.mobile_number})` : ""}
                </span>
                <span className="col-span-3">
                  {item.number_of_tickets} ticket(s)
                </span>
                <div className="col-span-2 flex justify-between">
                  {item.scanned_at !== null ? (
                    <div title="Checked-in" className="relative">
                      <IconCheck className="text-blue-400" />
                    </div>
                  ) : (
                    <div />
                  )}
                  {downloadUrl !== "#" ? (
                    <GuestListMenu
                      downloadUrl={downloadUrl}
                      tipAmount={item.tip_amount ?? 0}
                      tipMessage={item.tip_message ?? ""}
                      handleTips={handleTipDialog}
                    />
                  ) : (
                    <span />
                  )}
                </div>
              </div>
            )
          )
        })}
        {guestList?.map((item) => {
          let downloadUrl = "#"
          if (
            item &&
            (item.paymentOrder?.paymentable?.__typename ===
              "RzpayPaymentOrder" ||
              item.paymentOrder?.paymentable?.__typename ===
                "PhonePePaymentOrder") &&
            item.paymentOrder?.paymentable?.order_id &&
            item.paymentOrder?.paymentable?.payment_id
          ) {
            downloadUrl = `${baseUrl}/download-ticket?order_id=${item.paymentOrder.paymentable.order_id}&payment_id=${item.paymentOrder.paymentable.payment_id}`
          }

          return (
            item && (
              <div
                className="grid grid-cols-12 items-center border-b pb-2"
                key={item.id}
              >
                <span className="col-span-7 whitespace-pre-wrap">
                  {item.username || ""}{" "}
                  {item.mobile_number ? `(${item.mobile_number})` : ""}
                </span>
                <span className="col-span-3">
                  {item.number_of_tickets} ticket(s)
                </span>
                <div className="col-span-2 flex justify-between">
                  {item.scanned_at !== null ? (
                    <div title="Checked-in" className="relative">
                      <IconCheck className="text-blue-400" />
                    </div>
                  ) : (
                    <div />
                  )}
                  {downloadUrl !== "#" ? (
                    <GuestListMenu
                      downloadUrl={downloadUrl}
                      tipAmount={item.tip_amount ?? 0}
                      tipMessage={item.tip_message ?? ""}
                      handleTips={handleTipDialog}
                    />
                  ) : (
                    <span />
                  )}
                </div>
              </div>
            )
          )
        })}

        {hasNextPage && (
          <div className="col-span-12 mt-8 flex justify-center">
            <Button
              isLoading={isFetchingNextPage}
              variant="link"
              type="button"
              onClick={() => fetchNextPage()}
            >
              Fetch More
            </Button>
          </div>
        )}
      </div>
      <Dialog open={tips.open} onOpenChange={handleTipClose}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              Tip amount: <Rupee /> {tips.tipAmount}
            </DialogTitle>
            <DialogDescription>{tips.tipMessage}</DialogDescription>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    </>
  )
}

export default GuestList
