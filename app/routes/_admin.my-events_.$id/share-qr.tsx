import QRCodeCanvas from "qrcode.react"

import { baseWebsiteUrl } from "@lib/utils"
import { But<PERSON> } from "@/components/ui/button"

interface Props {
  slug: string
}

const ShareQR = ({ slug }: Props) => {
  const downloadQr = () => {
    const canvas = document.getElementById("qr-code") as HTMLCanvasElement
    const pngUrl = canvas
      .toDataURL("image/png")
      .replace("image/png", "image/octet-stream")
    const downloadLink = document.createElement("a")
    downloadLink.href = pngUrl
    downloadLink.download = "qrCode.png"
    document.body.appendChild(downloadLink)
    downloadLink.click()
    document.body.removeChild(downloadLink)
  }

  const qrCodeValue = `${baseWebsiteUrl}/event/${slug}`

  return (
    <div className="w-full">
      <QRCodeCanvas
        className="hidden"
        style={{ height: "auto", maxWidth: "100%", width: "100%" }}
        id="qr-code"
        value={qrCodeValue}
        size={1080}
      />
      <Button onClick={downloadQr} variant="outline" className="w-full">
        Download QR Code Share Link
      </Button>
    </div>
  )
}

export default ShareQR
