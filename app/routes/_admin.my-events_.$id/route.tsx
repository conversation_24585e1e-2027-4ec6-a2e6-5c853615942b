import { useParams, NavLink } from "react-router"

import Guest<PERSON>ist from "./guest-list"
import ShowReportDialog from "./show-report-dialog"
import ShareQR from "./share-qr"

import { BackButton, ErrorComponent } from "@components/common"
import { Loader } from "@components/ui"
import { buttonVariants } from "@/components/ui/button"
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import useGetEventById from "@/lib/hooks/use-get-event-by-id"

const EventById = () => {
  const { id } = useParams()

  const { data, isLoading, isError } = useGetEventById(id!)

  return (
    <>
      {isLoading && (
        <div className="my-16 flex justify-center">
          <Loader variant="large" />
        </div>
      )}

      {isError && (
        <div className="my-16">
          <ErrorComponent />
        </div>
      )}

      {!isLoading && !isError && (
        <div className="mx-auto mb-4 flex max-w-md flex-col">
          <div className="flex">
            <BackButton />
            <h1 className="w-full text-center font-bold">
              {data?.eventById?.name}
              <span>
                {data?.eventById?.is_published ? " (Published)" : " (Draft)"}
              </span>
              <span>
                {data?.eventById?.is_private_event ? " (Private Event)" : ""}
              </span>
            </h1>
          </div>

          <Accordion
            collapsible
            type="single"
            className="w-full"
            defaultValue="settings"
          >
            <AccordionItem value="settings">
              <AccordionTrigger>Settings</AccordionTrigger>
              <AccordionContent>
                <div className="flex w-full flex-col gap-y-4">
                  <NavLink
                    to="update"
                    className={buttonVariants({ variant: "outline" })}
                  >
                    Edit event detail
                  </NavLink>

                  <NavLink
                    to="invite-guests"
                    className={buttonVariants({ variant: "outline" })}
                  >
                    Invite Guest
                  </NavLink>

                  <NavLink
                    to="discount-settings"
                    className={buttonVariants({ variant: "outline" })}
                  >
                    Discount Settings
                  </NavLink>

                  <ShowReportDialog eventId={id!} />

                  {data?.eventById && <ShareQR slug={data.eventById.slug} />}

                  <NavLink
                    to="/scan-ticket"
                    className={buttonVariants({
                      variant: "primaryBlue",
                    })}
                  >
                    Scan ticket
                  </NavLink>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
          {data && <GuestList eventData={data} />}
        </div>
      )}
    </>
  )
}

export default EventById
