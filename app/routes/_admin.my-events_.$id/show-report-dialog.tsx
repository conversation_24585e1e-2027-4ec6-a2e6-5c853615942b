import { useState } from "react"

import { TicketStats } from "@components/common"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { buttonVariants } from "@/components/ui/button"

interface Props {
  eventId: string
}

const ShowReportDialog = ({ eventId }: Props) => {
  const [open, setOpen] = useState(false)

  return (
    <>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger className={buttonVariants({ variant: "outline" })}>
          Show Event Report
        </DialogTrigger>
        <DialogContent className="w-full max-w-md rounded-lg">
          <DialogHeader>
            <DialogTitle className="text-left">Event Report</DialogTitle>
          </DialogHeader>
          <TicketStats eventId={eventId} />
        </DialogContent>
      </Dialog>
    </>
  )
}

export default ShowReportDialog
