import { NavLink, useFetcher } from "react-router";
import clsx from "clsx"
import type { Dispatch, SetStateAction } from "react"

import {
  Sheet,
  SheetClose,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  Sheet<PERSON>rigger,
} from "@/components/ui/sheet"
import { Button } from "@/components/ui/button"
import { IconMenu, IconX } from "@/components/icons"
import useLogout from "@/lib/hooks/use-logout"

// NOTE: code smell pass a function instead of Dispatch as type
interface Props {
  open: boolean
  setOpen: Dispatch<SetStateAction<boolean>>
}

const Sidebar = ({ open, setOpen }: Props) => {
  const { logoutUser } = useLogout()
  const fetcher = useFetcher()

  const handleLogout = () => {
    logoutUser.mutate(undefined, {
      onSuccess: () => {
        fetcher.submit(null, {
          action: "/logout",
          method: "POST",
        })
      },
      onError: () => {
        fetcher.submit(null, {
          action: "/logout",
          method: "POST",
        })
      },
    })
  }

  const closeSheet = () => {
    setOpen(false)
  }

  return (
    <Sheet modal={false} defaultOpen={true} open={open}>
      <SheetTrigger asChild className="absolute left-2 top-2">
        <Button variant="ghost" onClick={() => setOpen(true)}>
          <IconMenu className="text-4xl" />
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="w-64 px-0">
        <SheetHeader>
          <NavLink to="/" className="w-32 px-2 text-4xl font-bold">
            <img src="/logo.png" alt="logo" className="w-24" />
          </NavLink>
          <SheetClose className="absolute right-4 top-4" onClick={closeSheet}>
            <IconX />
          </SheetClose>
        </SheetHeader>
        <ul className="mt-4">
          <li>
            <NavLink
              to="/super-admin/spotlight"
              className={({ isActive }) =>
                clsx("flex w-full p-2 hover:bg-slate-100", {
                  "bg-slate-50": isActive,
                })
              }
              onClick={closeSheet}
            >
              Spotlight
            </NavLink>
          </li>
          <li>
            <NavLink
              to="/super-admin/convenience-fee"
              className={({ isActive }) =>
                clsx("flex w-full p-2 hover:bg-slate-100", {
                  "bg-slate-50": isActive,
                })
              }
              onClick={closeSheet}
            >
              Convenience Fee
            </NavLink>
          </li>
          <li>
            <NavLink
              to="/super-admin/promote-event"
              className={({ isActive }) =>
                clsx("flex w-full p-2 hover:bg-slate-100", {
                  "bg-slate-50": isActive,
                })
              }
              onClick={closeSheet}
            >
              Promote Event
            </NavLink>
          </li>
          <li>
            <NavLink
              to="/super-admin/event-attendees"
              className={({ isActive }) =>
                clsx("flex w-full p-2 hover:bg-slate-100", {
                  "bg-slate-50": isActive,
                })
              }
              onClick={closeSheet}
            >
              Event Attendees
            </NavLink>
          </li>
          <li>
            <NavLink
              to="/super-admin/payment-provider-health"
              className={({ isActive }) =>
                clsx("flex w-full p-2 hover:bg-slate-100", {
                  "bg-slate-50": isActive,
                })
              }
              onClick={closeSheet}
            >
              Payment Provider Health
            </NavLink>
          </li>
          <li>
            <NavLink
              to="/super-admin/organizers"
              className={({ isActive }) =>
                clsx("flex w-full p-2 hover:bg-slate-100", {
                  "bg-slate-50": isActive,
                })
              }
              onClick={closeSheet}
            >
              Organizers
            </NavLink>
          </li>
          <li>
            <Button
              variant="ghost"
              onClick={handleLogout}
              isLoading={logoutUser.isPending}
              className="px-2 text-base font-normal text-black"
            >
              Logout
            </Button>
          </li>
        </ul>
      </SheetContent>
    </Sheet>
  )
}

export default Sidebar
