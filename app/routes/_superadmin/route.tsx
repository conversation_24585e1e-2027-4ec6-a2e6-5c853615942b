import { redirect, NavLink, Outlet } from "react-router"
import { useState } from "react"

import Sidebar from "./sidebar"

import { cn } from "@/lib/utils/shadcnUtil"
import { destroySession, getSession } from "@/sessions"
import type { Route } from "./+types/route"

export async function loader({ request }: Route.LoaderArgs) {
  const session = await getSession(request.headers.get("Cookie"))
  const role = session.get("role")

  if (role === "super-admin") {
    return null
  } else {
    return redirect("/login", {
      headers: {
        "Set-Cookie": await destroySession(session),
      },
    })
  }
}

const SuperAdmin = () => {
  const [open, setOpen] = useState(false)
  return (
    <div className="relative flex h-full min-h-screen flex-col text-gray-700">
      <Sidebar open={open} setOpen={setOpen} />
      <main
        className={cn(
          "grow px-4 py-8 transition-all duration-300 ease-in-out",
          {
            "md:ml-64": open,
            "md:ml-0": !open,
          }
        )}
      >
        <Outlet />
      </main>
      <footer className="h-auto w-full px-2 pt-12 text-primary-blue">
        <div className="flex flex-col items-center">
          <NavLink className="hover:underline" to="/terms-and-conditions">
            Terms & Conditions
          </NavLink>
          <NavLink className="hover:underline" to="/login">
            Login
          </NavLink>
          <div className="mt-4 flex flex-col items-center">
            <span>A product of </span>
            <a
              href="https://arsi.in"
              target="_blank"
              rel="noreferrer noopener"
              className="hover:underline"
            >
              Arsi Consultancy
            </a>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default SuperAdmin
