import {
  useNavigate,
  useSearchPara<PERSON>,
  useLoaderData,
  redirect,
} from "react-router"

import { IconLeft } from "@components/icons"
import { TicketLine } from "@components/ui"
import { baseUrl } from "@lib/utils"
import { graphql } from "@/__generated__"
import { cn } from "@/lib/utils/shadcnUtil"
import { buttonVariants } from "@/components/ui/button"
import { Rupee } from "@/components/common"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import type { TicketAvailableQuery } from "@/__generated__/graphql"
import { commitSession, getSession } from "@/sessions"
import type { Route } from "./+types/route"

const TICKET_AVAILABLE_QUERY = graphql(`
  query TicketAvailable($orderId: String!, $paymentId: String!) {
    isTicketDownloadable(order_id: $orderId, payment_id: $paymentId) {
      ticket_path
      amount
    }
  }
`)

const TIMEOUT_DELAY = 2000
const MAX_RETRIES = 5

export const meta = () => {
  return [
    {
      title: "Payment success",
    },
  ]
}

export async function loader({ request }: Route.LoaderArgs) {
  const session = await getSession(request.headers.get("Cookie"))
  const url = new URL(request.url)
  const orderId = url.searchParams.get("order_id")
  const paymentId = url.searchParams.get("payment_id")

  if (!orderId || !paymentId) {
    return redirect("/")
  }

  const graphqlClient = getGraphqlClient()

  let retries = 0
  let data: TicketAvailableQuery | undefined | null

  while (retries < MAX_RETRIES) {
    try {
      const response = await graphqlClient.request(TICKET_AVAILABLE_QUERY, {
        orderId: orderId,
        paymentId: paymentId,
      })

      data = response

      if (data.isTicketDownloadable !== null) {
        break
      }
    } catch (error) {
      session.flash("orderId", orderId)
      session.flash("error", true)
      return redirect("/payment-failure", {
        headers: {
          "Set-Cookie": await commitSession(session),
        },
      })
    }

    retries++
    await new Promise((resolve) => setTimeout(resolve, TIMEOUT_DELAY))
  }

  if (
    data?.isTicketDownloadable === null ||
    data?.isTicketDownloadable?.ticket_path === null
  ) {
    session.flash("orderId", orderId)
    session.flash("error", true)
    return redirect("/payment-failure", {
      headers: {
        "Set-Cookie": await commitSession(session),
      },
    })
  }

  return { data, orderId }
}

const PaymentSuccess = () => {
  const [searchParams] = useSearchParams()
  const data = useLoaderData<typeof loader>()
  const navigate = useNavigate()

  return (
    <>
      <div className="mx-auto flex max-w-5xl flex-col p-4 md:px-0">
        <div className="flex">
          <button type="button" onClick={() => navigate("/")}>
            <IconLeft className="text-2xl" />
          </button>
        </div>
        <div className="mt-8 flex flex-col gap-y-4">
          <h1 className="text-left text-4xl font-bold text-primary-blue">
            Thank you
          </h1>
          <p>
            We are thrilled to have you join us and hope you'll have an
            incredible experience. We will send you a confirmation and ticket
            details as soon as possible.
          </p>
          <p>Please display your ticket at the entrance for verification.</p>
          <p>Your support means the world to us</p>
          <p>See you at the event!</p>
          <div className="mt-4 flex flex-col items-center">
            <div>
              <div>
                Order number: <span className="font-bold">{data.orderId}</span>
              </div>
              <div>
                Amount:{" "}
                <span className="font-bold">
                  <Rupee /> {data?.data?.isTicketDownloadable?.amount}
                </span>
              </div>
            </div>
          </div>
          <div className="mt-4 flex justify-center">
            <a
              href={`${baseUrl}/download-ticket?order_id=${searchParams.get(
                "order_id"
              )}&payment_id=${searchParams.get("payment_id")}`}
              target="_blank"
              rel="noreferrer"
              className={cn(
                buttonVariants({ variant: "default" }),
                "bg-pallet-yellow text-black hover:bg-pallet-yellow/90"
              )}
            >
              Download Ticket
            </a>
          </div>
          <TicketLine />
        </div>
      </div>
    </>
  )
}

export default PaymentSuccess
