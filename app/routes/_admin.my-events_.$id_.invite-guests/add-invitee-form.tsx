import { zod<PERSON><PERSON><PERSON><PERSON> } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import toast from "react-hot-toast"

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { graphql } from "@/__generated__"
import { Button } from "@/components/ui/button"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { parseGraphqlError } from "@/lib/utils/parse-graphql-error"

const ADD_INVITEE = graphql(`
  mutation AddInvitee(
    $eventId: ID!
    $username: String!
    $mobileNumber: String!
    $numberOfTickets: Int!
  ) {
    addInvitee(
      event_id: $eventId
      username: $username
      mobile_number: $mobileNumber
      number_of_tickets: $numberOfTickets
    ) {
      id
    }
  }
`)

const schema = z.object({
  username: z.string().min(1, "Guest name is required"),
  mobileNumber: z.string().min(1, "Mobile number is required"),
  numberOfTickets: z.coerce
    .number({
      required_error: "Number of tickets is required",
      invalid_type_error: "Number of tickets must be a number",
    })
    .int()
    .positive()
    .min(1, { message: "Number of tickets should be at least 1" }),
})

type FormSchemaType = z.infer<typeof schema>

interface Props {
  eventId: string
}

const AddInviteeForm = ({ eventId }: Props) => {
  const graphql = getGraphqlClient()
  const queryClient = useQueryClient()

  const form = useForm<FormSchemaType>({
    resolver: zodResolver(schema),
    defaultValues: {
      username: "",
      mobileNumber: "",
      numberOfTickets: "" as unknown as number,
    },
  })

  const addInvitee = useMutation({
    mutationFn: async (data: FormSchemaType) => {
      return await graphql.request(ADD_INVITEE, {
        ...data,
        eventId: eventId,
      })
    },
  })

  const handleAddInvitee = (data: FormSchemaType) => {
    addInvitee.mutate(data, {
      onSuccess: () => {
        toast.success("Invitee added successfully")
        queryClient.invalidateQueries({
          queryKey: ["get-invitees"],
        })
        form.reset()
        form.setFocus("username")
      },
      onError: (error) => {
        toast.error(parseGraphqlError(error))
        form.reset()
      },
    })
  }

  return (
    <>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(handleAddInvitee)}
          className="flex flex-col gap-y-6"
        >
          <FormField
            control={form.control}
            name="username"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Invitee name</FormLabel>
                <FormControl>
                  <Input placeholder="Invitee name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="mobileNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Mobile number</FormLabel>
                <FormControl>
                  <Input
                    inputMode="numeric"
                    placeholder="Mobile number"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            name="numberOfTickets"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Number of tickets</FormLabel>
                <Input
                  placeholder="Number of tickets"
                  inputMode="numeric"
                  {...field}
                />
                <FormMessage />
              </FormItem>
            )}
          />
          <Button
            type="submit"
            isLoading={addInvitee.isPending}
            variant="primaryBlue"
          >
            Add Invitee
          </Button>
        </form>
      </Form>
    </>
  )
}

export default AddInviteeForm
