import { useParams } from "react-router";

import AddInviteeForm from "./add-invitee-form"
import InviteeList from "./invitee-list"

import { BackButton } from "@components/common"
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"

const InviteGuests = () => {
  const { id } = useParams()

  return (
    <>
      <div className="flex max-w-md flex-col md:mx-auto">
        <div className="flex">
          <BackButton />
          <h1 className="w-full text-center font-bold">Invite guests</h1>
        </div>

        <Accordion
          defaultValue="add-invitee-form"
          collapsible
          type="single"
          className="w-full"
        >
          <AccordionItem value="add-invitee-form" className="border-b-0">
            <AccordionTrigger>Add invitee</AccordionTrigger>
            <AccordionContent className="px-1">
              <AddInviteeForm eventId={id!} />
            </AccordionContent>
          </AccordionItem>
        </Accordion>

        <InviteeList eventId={id!} />
      </div>
    </>
  )
}

export default InviteGuests
