import { useState } from "react"
import { useDebounce } from "use-debounce"

import { GET_INVITEES } from "@/lib/graphql/queries"
import {
  INPUT_DEBOUNCED_VALUE,
  NUMBER_OF_ITEMS_TO_FETCH,
  baseUrl,
} from "@/lib/utils"
import { Input } from "@/components/ui/input"
import { IconCheck, IconLoader, IconSearch, IconX } from "@/components/icons"
import { Button } from "@/components/ui/button"
import { GuestListMenu } from "@/components/common"
import { useInfiniteQuery } from "@tanstack/react-query"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"

interface Props {
  eventId: string
}

const InviteeList = ({ eventId }: Props) => {
  const graphql = getGraphqlClient()
  const [searchInviteeList, setSearchInviteeList] = useState("")

  const [searchString] = useDebounce(searchInviteeList, INPUT_DEBOUNCED_VALUE)

  const {
    data,
    isLoading,
    isError,
    isFetchingNextPage,
    fetchNextPage,
    hasNextPage,
  } = useInfiniteQuery({
    queryKey: ["get-invitees", searchString],
    queryFn: async ({ pageParam = 1 }) => {
      const isMobileNumber = /^\d+$/.test(searchString)
      const containsLetter = /[a-zA-Z]/.test(searchString)

      const modifiedSearch = `%${searchString}%`

      return await graphql.request(GET_INVITEES, {
        eventId: eventId!,
        page: pageParam,
        first: NUMBER_OF_ITEMS_TO_FETCH,
        mobileNumber:
          isMobileNumber && !containsLetter ? modifiedSearch : undefined,
        username:
          containsLetter && !isMobileNumber ? modifiedSearch : undefined,
      })
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      if (!lastPage.getInvitees.paginatorInfo.hasMorePages) {
        return
      }
      return lastPage.getInvitees.paginatorInfo.currentPage + 1
    },
  })

  if (isError) {
    return (
      <div className="mt-4 flex w-full flex-col items-center">
        <h2 className="text-lg font-bold">Error</h2>
        <p>Cannot get guest list</p>
      </div>
    )
  }

  const inviteeList =
    data?.pages.flatMap((page) => page?.getInvitees.data) ?? []
  const total = data?.pages[0]?.getInvitees?.paginatorInfo?.total || 0

  return (
    <div className="my-4">
      <Input
        value={searchInviteeList}
        onChange={(e) => setSearchInviteeList(e.target.value)}
        placeholder="Search by name or phone number"
        leftSection={
          isLoading ? <IconLoader className="animate-spin" /> : <IconSearch />
        }
        rightSection={
          searchInviteeList && (
            <button
              onClick={() => setSearchInviteeList("")}
              className="flex items-center justify-center"
            >
              <IconX />
            </button>
          )
        }
      />
      <div className="mt-4 flex flex-col gap-y-2 bg-slate-100 p-4 text-black">
        <div className="mb-4 flex flex-col items-center justify-between">
          <h2 className="w-full text-left font-bold">Invite List : {total}</h2>
        </div>
        {inviteeList?.map((item) => {
          let downloadUrl = "#"
          if (
            item &&
            item.paymentOrder?.paymentable?.__typename === "PhonePePaymentOrder"
          ) {
            downloadUrl = `${baseUrl}/download-ticket?order_id=${item.paymentOrder.paymentable.order_id}&payment_id=${item.paymentOrder.paymentable.payment_id}`
          }
          return (
            item && (
              <div
                className="grid grid-cols-12 items-center border-b pb-2"
                key={item.id}
              >
                <span className="col-span-7">{item.username || ""}</span>
                <span className="col-span-3">
                  {item.number_of_tickets} ticket(s)
                </span>
                <span className="col-span-2 flex justify-between">
                  {item.scanned_at !== null ? (
                    <div title="Checked-in" className="relative">
                      <IconCheck className="text-blue-400" />
                    </div>
                  ) : (
                    <div />
                  )}
                  {downloadUrl !== "#" ? (
                    <GuestListMenu downloadUrl={downloadUrl} />
                  ) : (
                    <span />
                  )}
                </span>
              </div>
            )
          )
        })}
        {data && hasNextPage && (
          <div className="col-span-12 mt-8 flex justify-center">
            <Button
              isLoading={isFetchingNextPage}
              variant="ghost"
              type="button"
              onClick={() => fetchNextPage()}
            >
              Fetch More
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}

export default InviteeList
