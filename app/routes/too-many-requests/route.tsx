import { baseUrl } from "@/lib/utils"
import { useQuery } from "@tanstack/react-query"
import { Link, useNavigate, useSearchParams } from "react-router"

const TooManyRequests = () => {
  const [searchParams] = useSearchParams()
  const ref = searchParams.get("ref")
  const navigate = useNavigate()

  const { data } = useQuery({
    queryKey: ["too-many-requests"],
    queryFn: async () => {
      let retries = 0
      const maxRetries = 100
      const retryDelay = 2000 // 2 seconds

      while (retries < maxRetries) {
        const response = await fetch(`${baseUrl}/graphql`, {
          credentials: "include",
        })
        if (response.ok) {
          const data = await response.json()
          navigate(ref || "/", { replace: true })
          return data
        }

        retries++
        if (retries < maxRetries) {
          await new Promise((resolve) => setTimeout(resolve, retryDelay))
        }
      }
    },
    retry: false,
  })

  return (
    <div className="mx-auto flex max-w-4xl flex-col gap-y-4 px-4 md:px-10">
      <div className="flex h-full min-h-screen flex-col bg-white">
        <main className="grow">
          <div className="flex h-96 flex-col items-center justify-center gap-y-4">
            <div className="flex flex-col items-center">
              <img src="/logo.png" alt="logo" className="w-24" />
              <h1 className="mt-16 text-center text-4xl font-bold">
                Application Error
              </h1>
              <p>Too many requests, please try again later.</p>
              <Link to={ref || "/"} className="mt-4">
                Return
              </Link>
            </div>
          </div>
        </main>
        <footer className="mx-auto h-auto w-full px-2 pb-16 pt-8">
          <div className="mt-16 flex flex-col items-center">
            <span>A product of </span>
            <img src="/arsi_logo.svg" alt="Arsi logo" className="size-12" />
            <a
              href="https://arsi.in"
              target="_blank"
              rel="noreferrer noopener"
              className="hover:underline"
            >
              Arsi Consultancy
            </a>
          </div>
        </footer>
      </div>
    </div>
  )
}

export default TooManyRequests
