import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import toast from "react-hot-toast"

import { type FormSchemaType, schema } from "./schema"

import { Button, buttonVariants } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { graphql } from "@/__generated__"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import { parseGraphqlError } from "@/lib/utils/parse-graphql-error"

const ADD_ORGANIZER = graphql(`
  mutation AddOrganizer($name: String!, $password: String!) {
    addOrganizer(name: $name, password: $password) {
      message
    }
  }
`)

const AddOrganizerFormModal = () => {
  const [open, setOpen] = useState(false)
  const graphql = getGraphqlClient()
  const queryClient = useQueryClient()

  const form = useForm<FormSchemaType>({
    resolver: zodResolver(schema),
  })

  const closeModal = () => {
    setOpen(false)
  }

  const addOrganizer = useMutation({
    mutationFn: async (data: FormSchemaType) => {
      return await graphql.request(ADD_ORGANIZER, {
        name: data.identifier,
        password: data.password,
      })
    },
  })
  // const [addOrganizer, { loading, client }] = useMutation(ADD_ORGANIZER)

  const handleAddOrganizer = (data: FormSchemaType) => {
    addOrganizer.mutate(data, {
      onSuccess: () => {
        toast.success("Organizer added successfully")
        queryClient.invalidateQueries({
          queryKey: ["get-orgranizers"],
        })
        closeModal()
        form.reset()
      },
      onError: (error) => {
        toast.error(parseGraphqlError(error))
      },
    })
  }

  return (
    <div className="flex justify-between">
      <h1 className="text-2xl font-bold">Organizer List</h1>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger className={buttonVariants({ variant: "primaryBlue" })}>
          Add
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>Add Organizer</DialogHeader>
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(handleAddOrganizer)}
              className="flex flex-col gap-y-6"
            >
              <FormField
                control={form.control}
                name="identifier"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <FormLabel>Identifier</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter organizer identifier"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )
                }}
              />
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <Input
                          type="password"
                          placeholder="Enter organizer password"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )
                }}
              />
              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => {
                  return (
                    <FormItem>
                      <FormLabel>Confirm Password</FormLabel>
                      <FormControl>
                        <Input
                          type="password"
                          placeholder="Enter confirm password"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )
                }}
              />

              <div className="flex gap-x-4">
                <Button
                  isLoading={addOrganizer.isPending}
                  type="submit"
                  variant="primaryBlue"
                >
                  Add Organizer
                </Button>
                <Button type="button" variant="secondary">
                  Cancel
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default AddOrganizerFormModal
