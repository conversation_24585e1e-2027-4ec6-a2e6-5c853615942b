import { z } from "zod"

export const schema = z
  .object({
    identifier: z.string().min(1, "Identifier is required"),
    password: z.string().min(6, "Password must be at least 6 characters"),
    confirmPassword: z
      .string()
      .min(6, "Password must be at least 6 characters"),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords must match",
    path: ["confirmPassword"], // point to 'confirmPassword' field if validation fails
  })

export type FormSchemaType = z.infer<typeof schema>
