import { useState } from "react"
import { useDebounce } from "use-debounce"

import UpdateOrganizerFormModal from "./update-organizer-form-modal"

import { GET_ORGANIZERS } from "@/lib/graphql/queries"
import { Button } from "@/components/ui/button"
import { IconEdit, IconLoader, IconSearch, IconX } from "@/components/icons"
import type { GetOrganizersQuery } from "@/__generated__/graphql"
import { ErrorComponent } from "@/components/common"
import { Input } from "@/components/ui/input"
import { SEARCH_DEBOUNCE_VALUE } from "@/lib/utils"
import { useInfiniteQuery } from "@tanstack/react-query"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"

const OrganizersList = () => {
  const [openEdit, setOpenEdit] = useState(false)
  const [searchOrganizer, setSearchOrganizer] = useState("")
  const [organizer, setOrganizer] = useState({
    id: "",
    name: "",
  })
  const graphql = getGraphqlClient()

  const [keyword] = useDebounce(searchOrganizer, SEARCH_DEBOUNCE_VALUE)

  const {
    data,
    isLoading,
    isError,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
  } = useInfiniteQuery({
    queryKey: ["get-organizers", keyword],
    queryFn: async ({ pageParam = 1 }) =>
      await graphql.request(GET_ORGANIZERS, {
        page: pageParam,
        name: keyword,
      }),
    initialPageParam: 1,
    getNextPageParam: (lastPage: GetOrganizersQuery) => {
      if (!lastPage.organizerList?.hasMorePages) {
        return
      }
      return lastPage.organizerList.currentPage + 1
    },
  })

  const handleEdit = (id: string, name: string) => {
    setOrganizer({
      id: id,
      name: name,
    })

    setOpenEdit(true)
  }

  if (isError) {
    return (
      <div className="mt-4">
        <ErrorComponent errorMessage="Unable to get organizers" />
      </div>
    )
  }

  const allOrganizers =
    data?.pages.flatMap((page) => page.organizerList?.organizers) ?? []

  return (
    <>
      <div className="mt-4 flex flex-col gap-y-2 bg-slate-100 p-4">
        <Input
          value={searchOrganizer}
          onChange={(e) => setSearchOrganizer(e.target.value)}
          placeholder="Search by name"
          leftSection={
            isLoading ? <IconLoader className="animate-spin" /> : <IconSearch />
          }
          rightSection={
            searchOrganizer && (
              <button
                onClick={() => setSearchOrganizer("")}
                className="flex items-center justify-center"
              >
                <IconX />
              </button>
            )
          }
        />
        {allOrganizers?.map((item) => {
          return (
            item && (
              <div key={item.id} className="flex items-center justify-between">
                <div>{item?.identifier}</div>
                <div>
                  <Button
                    onClick={() => handleEdit(item.id, item.identifier)}
                    variant="ghost"
                    size="icon"
                    className="text-green-700"
                  >
                    <IconEdit />
                  </Button>
                </div>
              </div>
            )
          )
        })}
        {hasNextPage && (
          <div className="col-span-12 mt-8 flex justify-center">
            <Button
              isLoading={isFetchingNextPage}
              variant="ghost"
              type="button"
              onClick={() => fetchNextPage()}
            >
              Fetch More
            </Button>
          </div>
        )}
      </div>
      {organizer.id && (
        <UpdateOrganizerFormModal
          open={openEdit}
          setOpen={setOpenEdit}
          id={organizer.id}
          name={organizer.name}
        />
      )}
    </>
  )
}

export default OrganizersList
