import { type Dispatch, type SetStateAction } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import toast from "react-hot-toast"
import { z } from "zod"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DialogHeader } from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { graphql } from "@/__generated__"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { parseGraphqlError } from "@/lib/utils/parse-graphql-error"

const UPDATE_ORGANIZER = graphql(`
  mutation UpdateOrganizer($id: ID!, $name: String, $password: String) {
    updateOrganizer(id: $id, name: $name, password: $password) {
      id
    }
  }
`)

interface Props {
  id: string
  name: string
  open: boolean
  setOpen: Dispatch<SetStateAction<boolean>>
}

const schema = z
  .object({
    identifier: z.string().optional(),
    password: z.string().optional(),
    confirmPassword: z.string().optional(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords must match",
    path: ["confirmPassword"], // point to 'confirmPassword' field if validation fails
  })

type FormSchemaType = z.infer<typeof schema>

const UpdateOrganizerFormModal = ({ id, name, open, setOpen }: Props) => {
  // const [open, setOpen] = useState(false)
  const graphql = getGraphqlClient()
  const queryClient = useQueryClient()

  const form = useForm<FormSchemaType>({
    resolver: zodResolver(schema),
    defaultValues: {
      identifier: name,
    },
  })

  const closeModal = () => {
    setOpen(false)
  }

  const updateOrganizer = useMutation({
    mutationFn: async (data: FormSchemaType) => {
      return await graphql.request(UPDATE_ORGANIZER, {
        ...data,
        id: id,
      })
    },
  })

  // const [updateOrganizer, { loading, client }] = useMutation(UPDATE_ORGANIZER)

  const handleUpdateOrganizer = (data: FormSchemaType) => {
    updateOrganizer.mutate(data, {
      onSuccess: () => {
        toast.success("Organizer updated successfully")
        queryClient.invalidateQueries({
          queryKey: ["get-organizers"],
        })
        closeModal()
      },
      onError: (error) => {
        toast.error(parseGraphqlError(error))
      },
    })
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent>
        <DialogHeader>Update Organizer</DialogHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleUpdateOrganizer)}
            className="flex flex-col gap-y-6"
          >
            <FormField
              control={form.control}
              name="identifier"
              render={({ field }) => {
                return (
                  <FormItem>
                    <FormLabel>Identifier</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter organizer identifier"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )
              }}
            />
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => {
                return (
                  <FormItem>
                    <FormLabel>Password</FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder="Enter organizer password"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )
              }}
            />
            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field }) => {
                return (
                  <FormItem>
                    <FormLabel>Confirm Password</FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder="Enter confirm password"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )
              }}
            />

            <div className="flex gap-x-4">
              <Button
                isLoading={updateOrganizer.isPending}
                type="submit"
                variant="primaryBlue"
              >
                Update Organizer
              </Button>
              <Button type="button" variant="secondary">
                Cancel
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}

export default UpdateOrganizerFormModal
