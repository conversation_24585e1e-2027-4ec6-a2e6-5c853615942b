import { NavLink } from "react-router";

import { ErrorComponent } from "@components/common"
import { Carousel } from "@components/common/Carousel"
import { Loader } from "@components/ui"
import { MY_EVENTS_QUERY } from "@lib/graphql/queries"
import type { AppImage, MyEventsQuery } from "@/__generated__/graphql"
import { useInfiniteQuery } from "@tanstack/react-query"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import { Button } from "@/components/ui/button"

const MyEvents = () => {
  const graphql = getGraphqlClient()

  const {
    data,
    isLoading,
    isError,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
  } = useInfiniteQuery({
    queryKey: ["get-events"],
    queryFn: async ({ pageParam = 1 }) =>
      await graphql.request(MY_EVENTS_QUERY, {
        page: pageParam,
        first: 10,
      }),
    initialPageParam: 1,
    getNextPageParam: (lastPage: MyEventsQuery) => {
      if (!lastPage.myEvents.paginatorInfo.hasMorePages) {
        return
      }
      return lastPage.myEvents.paginatorInfo.currentPage + 1
    },
  })

  const eventsList = data?.pages.flatMap((page) => page?.myEvents.data) ?? []

  // will display create event link if user haven't created any event
  const createEvent =
    eventsList.length === 0 ? (
      <div className="flex h-full min-h-[60vh] flex-col justify-center">
        <h2 className="py-4 text-center text-xl font-semibold">
          You haven't created any event
        </h2>
        <div className="flex justify-center">
          <NavLink to="/create-event" className="underline">
            Create Event
          </NavLink>
        </div>
      </div>
    ) : null

  // event list for user
  const eventsDataList = eventsList.map((item) => {
    return (
      item && (
        <NavLink
          to={`${item.id}`}
          className="mx-auto flex max-w-xl flex-col"
          key={item.id}
        >
          <h2 className="py-4 text-center font-semibold">
            {item.name}
            <span>{item.is_published ? " (Published)" : " (Draft)"}</span>
            <span>{item.is_private_event ? " (Private Event)" : ""}</span>
          </h2>
          <div className="relative w-full pb-6">
            <Carousel data={item.images as AppImage[]} />
          </div>
        </NavLink>
      )
    )
  })

  return (
    <>
      {isLoading && (
        <div className="my-16 flex justify-center">
          <Loader variant="large" />
        </div>
      )}

      {isError && (
        <div className="my-16">
          <ErrorComponent />
        </div>
      )}

      {!isLoading && !isError && (
        <div className="flex flex-col gap-y-4">
          {createEvent}
          {eventsDataList}
          {hasNextPage && (
            <div className="flex justify-center">
              <Button
                isLoading={isFetchingNextPage}
                onClick={() => fetchNextPage()}
                variant="link"
              >
                Load More
              </Button>
            </div>
          )}
        </div>
      )}
    </>
  )
}

export default MyEvents
