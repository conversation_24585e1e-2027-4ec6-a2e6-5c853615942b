import toast from "react-hot-toast"
import { useNavigate } from "react-router";
import { useState } from "react"

import { graphql } from "@/__generated__"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog"
import { Button, buttonVariants } from "@/components/ui/button"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import { parseGraphqlError } from "@/lib/utils/parse-graphql-error"

const DELETE_EVENT = graphql(`
  mutation DeleteEvent($id: ID!) {
    deleteEvent(id: $id) {
      id
    }
  }
`)

interface Props {
  id: string
}

const DeleteEvent = ({ id }: Props) => {
  const navigate = useNavigate()
  const [open, setOpen] = useState(false)
  const graphql = getGraphqlClient()
  const queryClient = useQueryClient()

  const closeModal = () => {
    setOpen(false)
  }

  const deleteEvent = useMutation({
    mutationFn: async (id: string) => {
      return await graphql.request(DELETE_EVENT, {
        id: id,
      })
    },
  })

  const handleDeleteEvent = async () => {
    deleteEvent.mutate(id, {
      onSuccess: () => {
        toast.success("Event deleted successfully")
        queryClient.invalidateQueries({
          queryKey: ["get-events", "event-by-id"],
        })
        navigate("/my-events")
      },
      onError: (error) => {
        toast.error(parseGraphqlError(error))
      },
    })
  }

  return (
    <>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger className={buttonVariants({ variant: "destructive" })}>
          Delete Event
        </DialogTrigger>
        <DialogContent>
          <DialogHeader className="text-left">
            <DialogTitle>Delete event?</DialogTitle>
          </DialogHeader>
          <DialogDescription className="text-destructive">
            Warning this action is irreversible
          </DialogDescription>
          <DialogFooter className="flex flex-row gap-x-4">
            <div>
              <Button
                variant="destructive"
                isLoading={deleteEvent.isPending}
                onClick={handleDeleteEvent}
              >
                Yes
              </Button>
            </div>
            <div>
              <Button variant="secondary" onClick={closeModal}>
                Cancel
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}

export default DeleteEvent
