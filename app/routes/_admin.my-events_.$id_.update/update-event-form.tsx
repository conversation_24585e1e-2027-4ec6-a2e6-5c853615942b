import { zod<PERSON><PERSON><PERSON><PERSON> } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import type { ChangeEvent } from "react"
import dayjs from "dayjs"
import toast from "react-hot-toast"

import CancelEvent from "./cancel-event"
import DeleteEvent from "./delete-event"
import DeleteImage from "./delete-image"
import TicketTypes from "./ticket-types"
import { type FormSchemaType, schema } from "./schema"

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { EVENT_TYPES, baseUrl } from "@/lib/utils"
import type { Event } from "@/__generated__/graphql"
import { But<PERSON> } from "@/components/ui/button"
import { UPDATE_EVENT_MUTATION } from "@/lib/graphql/mutations"
import { CITIES } from "@/lib/utils/constants"
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "./edit-faq-field"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { parseGraphqlError } from "@/lib/utils/parse-graphql-error"
import getGraphqlMultipleClient from "@/lib/utils/graphql-upload-multiple-client"
import { graphqlClient } from "@/lib/hooks/graphql-client"

interface Props {
  data: Event
}

const UpdateEventForm = ({ data }: Props) => {
  const queryClient = useQueryClient()
  // const graphql = getGraphqlMultipleClient({
  //   fieldConfig: [
  //     { fieldName: "eventImages", isMultiple: true },
  //     { fieldName: "seatingPlan", isMultiple: false },
  //   ],
  //   graphqlDocument: UPDATE_EVENT_MUTATION,
  // })

  const form = useForm<FormSchemaType>({
    resolver: zodResolver(schema),
    defaultValues: {
      id: data?.id,
      name: data?.name,
      address: data?.address,
      location: data?.location || "",
      description: data?.description || "",
      orgContactNumber: data?.org_contact_number,
      orgName: data?.organizer_name,
      isPrivateEvent: data?.is_private_event,
      startDate: data?.start_date
        ? dayjs(data.start_date).format("YYYY-MM-DD HH:mm:ss")
        : "",
      endDate: data?.end_date
        ? dayjs(data.end_date).format("YYYY-MM-DD HH:mm:ss")
        : "",
      startBookingDate: data?.start_booking_date
        ? dayjs(data.start_booking_date).format("YYYY-MM-DD HH:mm:ss")
        : "",
      // startBookingDate: dayjs(data.start_booking_date).format("YYYY-MM-DD")  || "",
      openMicLink: data?.open_mic_link || "",
      city: data?.city || "",
      faq: undefined,
    },
  })

  const updateEvent = useMutation({
    mutationFn: async ({
      data,
      isPublished,
    }: {
      data: FormSchemaType
      isPublished: boolean
    }) => {
      const graphql = await graphqlClient()
      const faq = JSON.stringify(data.faq)
      return await graphql.request({
        document: UPDATE_EVENT_MUTATION,
        variables: {
          ...data,
          isPublished: isPublished,
          faq: faq,
        },
      })
      // return await graphql.request(UPDATE_EVENT_MUTATION, {
      //   ...data,
      //   isPublished: isPublished,
      //   faq: faq,
      // })
    },
    onSuccess: () => {
      toast.success("Event updated successfully")
      queryClient.invalidateQueries({
        queryKey: ["event-by-id", data.id],
      })
      queryClient.invalidateQueries({
        queryKey: ["get-events"],
      })
    },
    onError: (error) => {
      toast.error(parseGraphqlError(error))
    },
  })

  const handleUpdatePublished = (data: FormSchemaType) => {
    updateEvent.mutate({ data: data, isPublished: true })
  }

  const handlePublishEvent = (data: FormSchemaType) => {
    updateEvent.mutate({ data: data, isPublished: true })
  }

  const handleUpdateDraft = (data: FormSchemaType) => {
    updateEvent.mutate({ data: data, isPublished: false })
  }

  const handleFileInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files) {
      form.setValue("eventImages", Array.from(files))
    }
  }

  const handleSeatingPlanFileInputChange = (
    e: ChangeEvent<HTMLInputElement>
  ) => {
    const files = e.target.files
    if (files) {
      form.setValue("seatingPlan", files[0])
    }
  }

  return (
    <Form {...form}>
      <form className="mt-4 flex flex-col gap-y-6">
        <FormField
          control={form.control}
          name="city"
          render={({ field }) => (
            <FormItem>
              <FormLabel required>Choose city</FormLabel>
              <Select
                defaultValue={data?.city || ""}
                onValueChange={(city) => {
                  field.onChange(city)
                }}
              >
                <FormControl>
                  <SelectTrigger type="button">
                    <SelectValue placeholder="Choose city" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {CITIES.map((item) => (
                    <SelectItem key={item.id} value={item.value}>
                      {item.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel required>Event name</FormLabel>
              <FormControl>
                <Input placeholder="Enter event name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="grid grid-cols-12 gap-2">
          {data?.images &&
            data.images.map((item) => {
              return (
                item && (
                  <div key={item.id} className="relative col-span-6 h-48">
                    <img
                      className="size-full object-cover"
                      src={`${baseUrl}/image/medium/${item.path}`}
                      alt={item.id}
                    />
                    <DeleteImage imageId={item.id} />
                  </div>
                )
              )
            })}
        </div>
        <FormField
          control={form.control}
          name="eventImages"
          render={() => (
            <FormItem>
              <FormLabel required>
                Event image (4:5 portrait images recommended)
              </FormLabel>
              <FormControl>
                <Input
                  type="file"
                  placeholder="Upload event images"
                  onChange={handleFileInputChange}
                  multiple
                  accept="image/jpg, image/png, image/jpeg"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        {data?.seatingPlan && (
          <div key={data.seatingPlan.id} className="relative h-48">
            <img
              className="size-full object-cover"
              src={`${baseUrl}/image/medium/${data.seatingPlan.path}`}
              alt={data.seatingPlan.id}
            />
            <DeleteImage imageId={data.seatingPlan.id} />
          </div>
        )}
        <FormField
          control={form.control}
          name="seatingPlan"
          render={() => (
            <FormItem>
              <FormLabel>Seating Plans</FormLabel>
              <FormControl>
                <Input
                  type="file"
                  placeholder="Upload seating plans"
                  onChange={handleSeatingPlanFileInputChange}
                  accept="image/jpg, image/png, image/jpeg"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea placeholder="Enter event name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="address"
          render={({ field }) => (
            <FormItem>
              <FormLabel required>Address</FormLabel>
              <FormControl>
                <Textarea placeholder="Enter address" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="location"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Location</FormLabel>
              <FormControl>
                <Input placeholder="Enter location" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="openMicLink"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Open mic link</FormLabel>
              <FormControl>
                <Input placeholder="Enter open mic form link" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="orgName"
          render={({ field }) => (
            <FormItem>
              <FormLabel required>Organizer name</FormLabel>
              <FormControl>
                <Input placeholder="Enter organizer name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="orgContactNumber"
          render={({ field }) => (
            <FormItem>
              <FormLabel required>Organizer contact number </FormLabel>
              <FormControl>
                <Input
                  placeholder="Enter organizer contact number"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <EditFaqField control={form.control} faqData={data?.faq || ""} />
        <FormField
          control={form.control}
          name="startDate"
          render={({ field }) => (
            <FormItem>
              <FormLabel required>Start date</FormLabel>
              <FormControl>
                <Input type="datetime-local" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="endDate"
          render={({ field }) => (
            <FormItem>
              <FormLabel required>End date</FormLabel>
              <FormControl>
                <Input type="datetime-local" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="startBookingDate"
          render={() => (
            <FormItem>
              <FormLabel>Start booking date (Optional)</FormLabel>
              <FormControl>
                <Input
                  type="datetime-local"
                  {...form.register("startBookingDate", {
                    setValueAs: (v) =>
                      v === ""
                        ? undefined
                        : dayjs(v).format("YYYY-MM-DD HH:mm:ss"),
                  })}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="isPrivateEvent"
          render={({ field }) => {
            return (
              <FormItem>
                <FormLabel>Event Type</FormLabel>
                <Select
                  defaultValue={
                    data.is_private_event ? "Private Event" : "Public Event"
                  }
                  onValueChange={(item) => {
                    if (item === "Public Event") {
                      field.onChange(false)
                    } else {
                      field.onChange(true)
                    }
                  }}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {EVENT_TYPES.map((item) => (
                      <SelectItem key={item.id} value={item.name}>
                        {item.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormItem>
            )
          }}
        />
        <TicketTypes data={data} />
        {data?.is_published ? (
          <>
            <div className="mt-6 flex w-full justify-center border-primary-blue">
              <Button
                className="w-full"
                type="button"
                onClick={form.handleSubmit(handleUpdatePublished)}
                isLoading={updateEvent.isPending}
                variant="primaryBlue"
              >
                Update Event
              </Button>
            </div>
            <CancelEvent id={data.id} />
          </>
        ) : (
          <>
            <div className="mt-6 flex w-full justify-center border-primary-blue">
              <Button
                variant="outline"
                className="w-full"
                type="button"
                isLoading={updateEvent.isPending}
                onClick={form.handleSubmit(handleUpdateDraft)}
              >
                Save as Draft
              </Button>
            </div>
            <div className="flex w-full justify-center border-primary-blue">
              <Button
                variant="primaryBlue"
                className="w-full"
                type="button"
                isLoading={updateEvent.isPending}
                onClick={form.handleSubmit(handlePublishEvent)}
              >
                Publish Event
              </Button>
            </div>
            <DeleteEvent id={data.id} />
          </>
        )}
      </form>
    </Form>
  )
}

export default UpdateEventForm
