import toast from "react-hot-toast"
import { useState } from "react"

import { graphql } from "@/__generated__"
import { IconTrash } from "@components/icons"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import { parseGraphqlError } from "@/lib/utils/parse-graphql-error"

const DELETE_IMAGE_BY_ID = graphql(`
  mutation DeleteImageById($id: ID!) {
    deleteImageById(id: $id) {
      message
    }
  }
`)

interface Props {
  imageId: string
}

const DeleteImage = ({ imageId }: Props) => {
  const [open, setOpen] = useState(false)
  const graphql = getGraphqlClient()
  const queryClient = useQueryClient()

  const deleteImage = useMutation({
    mutationFn: async (id: string) => {
      return await graphql.request(DELETE_IMAGE_BY_ID, {
        id: id,
      })
    },
  })

  const handleDeleteImage = async () => {
    deleteImage.mutate(imageId, {
      onSuccess: () => {
        toast.success("Image deleted successfully")
        queryClient.invalidateQueries({
          queryKey: ["event-by-id"],
        })
        queryClient.invalidateQueries({
          queryKey: ["get-events"],
        })
        closeModal()
      },
      onError: (error) => {
        toast.error(parseGraphqlError(error))
        closeModal()
      },
    })
  }

  const closeModal = () => {
    setOpen(false)
  }

  return (
    <>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger className="absolute right-2 top-2 z-10 rounded-full border border-destructive bg-white p-2 text-destructive">
          <IconTrash />
        </DialogTrigger>
        <DialogContent>
          <DialogHeader className="text-left">
            <DialogTitle>Delete image?</DialogTitle>
          </DialogHeader>
          <DialogFooter className="flex flex-row gap-x-4">
            <div>
              <Button
                variant="destructive"
                isLoading={deleteImage.isPending}
                onClick={handleDeleteImage}
              >
                Yes
              </Button>
            </div>
            <div>
              <Button variant="secondary" onClick={closeModal}>
                Cancel
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}

export default DeleteImage
