import dayjs from "dayjs"
import { z } from "zod"

export const faqSchema = z.object({
  question: z.string(),
  answer: z.string(),
})

export const schema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  location: z.string(),
  eventImages: z.array(z.instanceof(File)).nullish(),
  seatingPlan: z.instanceof(File).nullish(),
  orgName: z.string(),
  orgContactNumber: z.string(),
  address: z.string(),
  isPrivateEvent: z.boolean(),
  startDate: z
    .string()
    .refine((v) => v !== "", "Start date is required")
    .transform((v) => dayjs(v).format("YYYY-MM-DD HH:mm:ss")),
  endDate: z
    .string()
    .refine((v) => v !== "", "End date is required")
    .transform((v) => dayjs(v).format("YYYY-MM-DD HH:mm:ss")),
  startBookingDate: z.string().nullish(),
  openMicLink: z.string().optional(),
  city: z.string().optional(),
  faq: z.array(faqSchema).optional(),
})

export type FormSchemaType = z.infer<typeof schema>
