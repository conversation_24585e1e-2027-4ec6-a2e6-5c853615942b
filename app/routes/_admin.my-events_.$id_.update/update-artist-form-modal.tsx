import { useForm } from "react-hook-form"
import { toast } from "react-hot-toast"
import { zodResolver } from "@hookform/resolvers/zod"
import { type ChangeEvent, useState } from "react"

import { baseUrl } from "@lib/utils"
import { graphql } from "@/__generated__"
import type { EventArtist } from "@/__generated__/graphql"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Button, buttonVariants } from "@/components/ui/button"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import type { UpsertArtistType } from "@/lib/types/artist-types"
import { upsertArtistSchema } from "@/lib/types/artist-types"
import { parseGraphqlError } from "@/lib/utils/parse-graphql-error"
import useUpsertEventArtist from "@/lib/hooks/use-upsert-event-artist"

const DELETE_EVENT_ARTIST = graphql(`
  mutation DeleteEventArtist($id: ID!) {
    deleteEventArtist(event_artist_id: $id) {
      message
    }
  }
`)

interface Props {
  artist: EventArtist
  eventId: string
}

const UpdateArtistFormModal = ({ artist, eventId }: Props) => {
  const graphql = getGraphqlClient()
  const queryClient = useQueryClient()

  const [open, setOpen] = useState(false)
  const [openDelete, setOpenDelete] = useState(false)

  const closeModal = () => {
    setOpen(false)
  }

  const closeDeleteModal = () => {
    setOpenDelete(false)
  }

  const form = useForm<UpsertArtistType>({
    resolver: zodResolver(upsertArtistSchema),
    defaultValues: {
      name: artist?.name || "",
      order: artist?.order || 0,
      social_link: artist?.social_link || "",
    },
  })

  const { upsertArtist } = useUpsertEventArtist(eventId, artist.id)

  const deleteArtist = useMutation({
    mutationFn: async (id: string) => {
      return await graphql.request(DELETE_EVENT_ARTIST, {
        id: id,
      })
    },
  })

  const handleUpdateArtist = (data: UpsertArtistType) => {
    upsertArtist.mutate(data, {
      onSuccess: () => {
        toast.success("Artist successfully updated")
        closeModal()
        queryClient.invalidateQueries({
          queryKey: ["get-events", "event-by-id"],
        })
      },
      onError: (error) => {
        toast.error(parseGraphqlError(error))
        form.reset()
        queryClient.refetchQueries({
          queryKey: ["get-events", "event-by-id"],
        })
      },
    })
  }

  const handleDeleteArtist = () => {
    deleteArtist.mutate(artist.id, {
      onSuccess: () => {
        toast.success("Artist successfully deleted")
        closeDeleteModal()
        closeModal()
        queryClient.invalidateQueries({
          queryKey: ["get-events", "event-by-id"],
        })
      },
      onError: (error) => {
        toast.error(parseGraphqlError(error))
      },
    })
  }

  const handleArtistImage = (e: ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files) {
      form.setValue("avatar", files[0])
    }
  }

  const deleteDialog = (
    <Dialog open={openDelete} onOpenChange={setOpenDelete}>
      <DialogTrigger className={buttonVariants({ variant: "destructive" })}>
        Delete
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>Delete Artist?</DialogHeader>
        <div className="flex gap-x-4">
          <Button
            isLoading={deleteArtist.isPending}
            onClick={handleDeleteArtist}
            variant="destructive"
          >
            Yes
          </Button>
          <Button onClick={closeDeleteModal} variant="secondary">
            Cancel
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )

  return (
    <>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger>
          <div className="relative flex h-auto w-full justify-center">
            <div className="absolute inset-0 rounded-full hover:bg-black/30" />
            <img
              className="size-16 rounded-full object-cover xs:size-20 md:size-24"
              src={`${baseUrl}/image/medium/${artist.avatar}`}
              alt={artist.name}
            />
          </div>
          <p className="flex w-full justify-center">{artist.name}</p>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <img
              className="size-32 rounded-full border object-cover"
              src={`${baseUrl}/image/medium/${artist.avatar}`}
              alt={artist.name}
            />
            <DialogTitle className="text-left">Update Info</DialogTitle>
          </DialogHeader>
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(handleUpdateArtist)}
              className="mt-4 flex flex-col gap-y-6"
            >
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input
                        autoComplete="off"
                        placeholder="Coupon name"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="avatar"
                render={() => (
                  <FormItem>
                    <FormLabel>Avatar image</FormLabel>
                    <FormControl>
                      <Input
                        type="file"
                        placeholder="Upload seating plans"
                        onChange={handleArtistImage}
                        accept="image/jpg, image/png, image/jpeg"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="social_link"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Social link</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter social link" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="order"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Order</FormLabel>
                    <FormControl>
                      <Input
                        inputMode="numeric"
                        placeholder="Enter order"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="flex gap-x-4">
                <Button
                  isLoading={upsertArtist.isPending}
                  type="submit"
                  variant="primaryBlue"
                >
                  Update
                </Button>
                <Button type="button" variant="secondary" onClick={closeModal}>
                  Cancel
                </Button>
              </div>
              <div className="flex gap-x-4">{deleteDialog}</div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  )
}

export default UpdateArtistFormModal
