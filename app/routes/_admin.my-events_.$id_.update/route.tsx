import { useParams } from "react-router";

import UpdateEventForm from "./update-event-form"
import AddArtistFormModal from "./add-artist-form-modal"
import UpdateArtistFormModal from "./update-artist-form-modal"

import { BackButton, ErrorComponent } from "@components/common"
import { Loader } from "@components/ui"
import type { EventArtist, Event } from "@/__generated__/graphql"
import useGetEventById from "@/lib/hooks/use-get-event-by-id"

const UpdateEventById = () => {
  const { id } = useParams()

  const { data, isLoading, isError } = useGetEventById(id!)

  return (
    <>
      {isLoading && (
        <div className="my-16 flex justify-center">
          <Loader variant="large" />
        </div>
      )}
      {isError && (
        <div className="my-16">
          <ErrorComponent />
        </div>
      )}{" "}
      {!isLoading && !isError && (
        <div className="mx-auto max-w-md">
          <div className="flex">
            <BackButton />
            <h1 className="w-full text-center font-bold">
              {data?.eventById?.name}
            </h1>
          </div>
          {data?.eventById && (
            <UpdateEventForm data={data.eventById as Event} />
          )}
          {data?.eventById?.artists && (
            <div className="my-8 grid grid-cols-4 gap-4">
              <div className="col-span-4 flex justify-between">
                <p>Artists</p>
                <AddArtistFormModal eventId={id!} />
              </div>
              {data.eventById.artists.map((item) => {
                // using type assertion here since we're not doing anything with item.hash anyway which is required in the grapqhl schema
                return (
                  item && (
                    <UpdateArtistFormModal
                      key={item?.id}
                      artist={item as EventArtist}
                      eventId={id!}
                    />
                  )
                )
              })}
            </div>
          )}
        </div>
      )}
    </>
  )
}

export default UpdateEventById
