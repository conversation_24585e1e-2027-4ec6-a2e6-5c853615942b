import { useState } from "react"
import toast from "react-hot-toast"

import { UPDATE_EVENT_MUTATION } from "@lib/graphql/mutations"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Button, buttonVariants } from "@/components/ui/button"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { parseGraphqlError } from "@/lib/utils/parse-graphql-error"

interface Props {
  id: string
}

const CancelEvent = ({ id }: Props) => {
  const [open, setOpen] = useState(false)
  const graphql = getGraphqlClient()
  const queryClient = useQueryClient()

  const closeModal = () => {
    setOpen(false)
  }

  const updateEvent = useMutation({
    mutationFn: async () => {
      return await graphql.request(UPDATE_EVENT_MUTATION, {
        id: id,
        isPublished: false,
      })
    },
  })

  const handleCancelEvent = async () => {
    updateEvent.mutate(undefined, {
      onSuccess: () => {
        toast.success("Event cancelled successfully")
        queryClient.invalidateQueries({
          queryKey: ["get-events", "event-by-id"],
        })
      },
      onError: (error) => {
        toast.error(parseGraphqlError(error))
      },
    })
  }

  return (
    <>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger className={buttonVariants({ variant: "destructive" })}>
          Cancel Event
        </DialogTrigger>
        <DialogContent>
          <DialogHeader className="text-left">
            <DialogTitle>Cancel event?</DialogTitle>
          </DialogHeader>
          <DialogFooter className="flex flex-row gap-x-4">
            <div>
              <Button
                variant="destructive"
                isLoading={updateEvent.isPending}
                onClick={handleCancelEvent}
              >
                Yes
              </Button>
            </div>
            <div>
              <Button variant="secondary" onClick={closeModal}>
                Cancel
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}

export default CancelEvent
