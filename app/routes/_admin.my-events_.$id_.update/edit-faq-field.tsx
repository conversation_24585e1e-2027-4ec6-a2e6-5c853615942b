import { useEffect, useRef } from "react"
import { type Control, useFieldArray } from "react-hook-form"
import { type z } from "zod"

import { type faqSchema, type FormSchemaType } from "./schema"

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { IconX } from "@/components/icons"

interface Props {
  control: Control<FormSchemaType>
  faqData?: string
}

type FaqType = z.infer<typeof faqSchema>

const EditFaqField = ({ control, faqData }: Props) => {
  const runOnce = useRef(false)
  const { fields, append, remove } = useFieldArray<FormSchemaType>({
    control,
    name: "faq",
  })

  const handleAddFaq = () => {
    append({
      question: "",
      answer: "",
    })
  }

  useEffect(() => {
    if (runOnce.current) return

    if (faqData) {
      const faqArray: FaqType[] = JSON.parse(faqData)
      const itemsToAppend = faqArray.map((item) => ({
        question: item.question,
        answer: item.answer,
      }))
      append(itemsToAppend, { shouldFocus: false })
    }

    runOnce.current = true
  }, [faqData, append])

  return (
    <>
      {fields.map((item, index) => {
        return (
          <div
            key={item.id}
            className="relative flex flex-col gap-y-4 border border-input p-4 pt-12 focus-within:ring-2 focus-within:ring-black"
          >
            <Button
              variant="ghost"
              type="button"
              className="absolute right-0 top-0 text-destructive"
              onClick={() => remove(index)}
            >
              <IconX />
            </Button>
            <FormField
              control={control}
              name={`faq.${index}.question`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Question</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter question" {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name={`faq.${index}.answer`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Answer</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter answer" {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        )
      })}
      <div>
        <Button variant="outline" onClick={handleAddFaq} type="button">
          Add Faq
        </Button>
      </div>
    </>
  )
}

export default EditFaqField
