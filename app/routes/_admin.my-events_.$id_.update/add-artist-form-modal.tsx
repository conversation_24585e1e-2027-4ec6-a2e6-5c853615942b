import { type ChangeEvent, useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import toast from "react-hot-toast"

import { IconAddPerson } from "@components/icons"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useQueryClient } from "@tanstack/react-query"
import { parseGraphqlError } from "@/lib/utils/parse-graphql-error"
import type { UpsertArtistType } from "@/lib/types/artist-types"
import { upsertArtistSchema } from "@/lib/types/artist-types"
import useUpsertEventArtist from "@/lib/hooks/use-upsert-event-artist"

interface Props {
  eventId: string
}

const AddArtistFormModal = ({ eventId }: Props) => {
  const [open, setOpen] = useState(false)

  const queryClient = useQueryClient()

  const closeModal = () => {
    setOpen(false)
  }

  const form = useForm<UpsertArtistType>({
    resolver: zodResolver(upsertArtistSchema),
    defaultValues: {
      avatar: undefined,
      name: "",
      social_link: "",
      order: "" as unknown as number,
    },
  })

  const { upsertArtist } = useUpsertEventArtist(eventId)

  const handleAddArtist = async (data: UpsertArtistType) => {
    upsertArtist.mutate(data, {
      onSuccess: () => {
        toast.success("Artist successfully added")
        closeModal()
        form.reset()
        queryClient.invalidateQueries({
          queryKey: ["event-by-id"],
        })
        queryClient.invalidateQueries({
          queryKey: ["get-events"],
        })
      },
      onError: (error) => {
        toast.error(parseGraphqlError(error))
        form.reset()
        queryClient.invalidateQueries({
          queryKey: ["get-events", "event-by-id"],
        })
      },
    })
  }

  const handleArtistImage = (e: ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files) {
      form.setValue("avatar", files[0])
    }
  }

  return (
    <>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger>
          <IconAddPerson />
        </DialogTrigger>
        <DialogContent>
          <DialogHeader className="text-left font-bold">
            <DialogTitle>Add Artists</DialogTitle>
            <DialogDescription>Enter artist details</DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(handleAddArtist)}
              className="flex flex-col gap-y-6"
            >
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input
                        autoComplete="off"
                        placeholder="Enter artist name"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="avatar"
                render={() => (
                  <FormItem>
                    <FormLabel>Avatar image</FormLabel>
                    <FormControl>
                      <Input
                        type="file"
                        placeholder="Upload avatar"
                        onChange={handleArtistImage}
                        accept="image/jpg, image/png, image/jpeg"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="social_link"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Social link</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter social link" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="order"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Order</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter order"
                        inputMode="numeric"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="flex gap-x-4">
                <Button
                  isLoading={upsertArtist.isPending}
                  type="submit"
                  variant="primaryBlue"
                >
                  Add
                </Button>
                <Button variant="secondary" onClick={closeModal} type="button">
                  Cancel
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  )
}

export default AddArtistFormModal
