import type { Event } from "@/__generated__/graphql"
import { Rupee } from "@/components/common"

const TicketTypes = ({ data }: { data: Event }) => {
  return (
    data?.eventTicketTypes && (
      <div>
        <div>Ticket type(s) (cannot be changed) </div>
        <div className="grid grid-cols-12 gap-4">
          {data?.eventTicketTypes
            ?.filter((item) => item?.ticket_type !== "special_invitee")
            .map((item) => {
              return (
                <div
                  className="relative col-span-6 flex cursor-pointer rounded-lg border px-5 py-4 focus:outline-none"
                  key={item?.id}
                >
                  <>
                    <div className="flex w-full items-center justify-between">
                      <div className="flex items-center">
                        <div className="text-sm">
                          <p className={`font-medium`}>
                            {item?.ticket_type === "default"
                              ? "General"
                              : item?.ticket_type}
                          </p>
                          <p>Maximum capacity: {item?.maximum_capacity}</p>
                          <span className={`inline`}>
                            <span>
                              <Rupee /> {item?.price}
                            </span>{" "}
                          </span>
                        </div>
                      </div>
                    </div>
                  </>
                </div>
              )
            })}
        </div>
      </div>
    )
  )
}

export default TicketTypes
