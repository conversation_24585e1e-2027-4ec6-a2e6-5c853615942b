import { NavLink, Outlet } from "react-router"

import type { Route } from "./+types/route"
import { Footer } from "@/components/common"

export const meta = ({ location }: Route.MetaArgs) => {
  return [
    { title: `Triket` },
    {
      name: "description",
      content:
        "Triket is a user-friendly and secure online platform that connects event organizers, venues, and performers with enthusiasts alike",
    },
    {
      property: "og:url",
      content: `https://triket.in${location.pathname}`,
    },
    {
      property: "og:image",
      itemProp: "image",
      content: "/logo.png",
    },
  ]
}

const LogoOnlyLayout = () => {
  return (
    <div className="flex h-full min-h-screen flex-col bg-white">
      <header className="relative mx-auto flex h-[8vh] w-full items-center justify-between border-black px-2 py-10 md:p-10">
        <NavLink to="/" className="text-4xl font-bold">
          <img src="/logo.png" alt="logo" className="w-24" />
        </NavLink>
      </header>
      <main className="grow px-2">
        <Outlet />
      </main>
      <Footer />
    </div>
  )
}

export default LogoOnlyLayout
