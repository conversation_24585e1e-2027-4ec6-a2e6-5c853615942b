import { Outlet } from "react-router";

import { <PERSON><PERSON>, <PERSON><PERSON> } from "@/components/common"
import Ra<PERSON>payLoader from "@/components/common/razorpay-loader"

const LandingLayout = () => {
  return (
    <div className="flex h-full min-h-screen flex-col bg-white">
      <Header />
      {/* <main className="grow px-2"> NOTE: previous implementation uses px-2*/}
      <main className="grow">
        <Outlet />
      </main>
      <Footer />
      <RazorpayLoader />
    </div>
  )
}

export default LandingLayout
