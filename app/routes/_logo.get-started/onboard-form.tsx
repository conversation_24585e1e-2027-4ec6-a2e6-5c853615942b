import { useState } from "react"
import { useForm } from "react-hook-form"
import { toast } from "react-hot-toast"
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod"

import { graphql } from "@/__generated__"
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON>Title,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog"
import { Button, buttonVariants } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import { parseGraphqlError } from "@/lib/utils/parse-graphql-error"
import { useMutation } from "@tanstack/react-query"

const ONBOARD_MUTATION = graphql(`
  mutation OnBoard(
    $email: String!
    $name: String!
    $address: String
    $mobileNumber: String!
  ) {
    onboardOrganizer(
      email: $email
      name: $name
      address: $address
      mobile_number: $mobileNumber
    ) {
      id
    }
  }
`)

const schema = z.object({
  email: z.string().min(1, "Email is required"),
  name: z.string().min(1, "Name is required"),
  address: z.string().optional(),
  mobileNumber: z.string().min(1, "Mobile number is required"),
})

type FormSchemaType = z.infer<typeof schema>

const OnBoardForm = () => {
  const [open, setOpen] = useState(false)
  const graphql = getGraphqlClient()

  const closeModal = () => {
    setOpen(false)
  }

  const form = useForm<FormSchemaType>({
    resolver: zodResolver(schema),
  })

  const onBoard = useMutation({
    mutationFn: async (data: FormSchemaType) => {
      return graphql.request(ONBOARD_MUTATION, {
        ...data,
      })
    },
  })

  const handleOnboarding = async (data: FormSchemaType) => {
    onBoard.mutate(data, {
      onSuccess: () => {
        toast.success(
          "Thanks for submitting the application. We'll get back to you shortly"
        )
        form.reset()
        closeModal()
      },
      onError: (error) => {
        toast.error(parseGraphqlError(error))
      },
    })
  }

  return (
    <>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger className={buttonVariants({ variant: "primaryBlue" })}>
          Get Started
        </DialogTrigger>
        <DialogContent>
          <DialogTitle>Get Started</DialogTitle>
          <Form {...form}>
            <form
              className="flex flex-col gap-y-6"
              onSubmit={form.handleSubmit(handleOnboarding)}
            >
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Enter Name" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="mobileNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mobile number</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Enter mobile number" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Email" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Address</FormLabel>
                    <FormControl>
                      <Textarea {...field} placeholder="Enter address" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="flex gap-x-4">
                <div>
                  <Button
                    isLoading={onBoard.isPending}
                    type="submit"
                    variant="primaryBlue"
                  >
                    Submit
                  </Button>
                </div>
                <div>
                  <Button
                    type="button"
                    onClick={closeModal}
                    variant="secondary"
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  )
}

export default OnBoardForm
