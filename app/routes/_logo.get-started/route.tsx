import OnBoardForm from "./onboard-form"

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"

const GetStarted = () => {
  return (
    <>
      <div className="mx-auto flex max-w-4xl flex-col gap-y-4 px-4 md:px-10">
        <h1 className="mt-10 text-3xl font-bold ">Welcome to Triket.in!</h1>
        <p>
          Welcome aboard! We're thrilled to have you join the Triket.in
          community. Get ready to experience seamless event management and
          ticketing.
        </p>
        <Accordion collapsible type="single">
          <AccordionItem value="learn-more">
            <AccordionTrigger>Learn more</AccordionTrigger>
            <AccordionContent className="text-base">
              <section className="flex flex-col gap-y-4 pb-10">
                <p>
                  1. Account Setup: Let's kick things off! Create your Triket.in
                  account to unlock a world of event organization possibilities.
                  Ensure your details are accurate for a smooth journey.
                </p>
                <p>
                  2. Create Event: Ready to host a memorable event? Our
                  user-friendly interface helps you create your event
                  effortlessly. Fill in event details, dates, times, and venue
                  information.
                </p>
                <p>
                  3. Ticketing Options: Explore the diverse ticketing options
                  Triket.in offers. Set ticket prices, types, and quantity
                  limits to suit your event needs perfectly.
                </p>
                <p>
                  4. Payment Integration: Say goodbye to manual payment hassles!
                  Integrate our secure payment gateway, making transactions
                  seamless and eliminating the need for manual verification.
                </p>
                <p>
                  5. Guest List Management: Managing your guest list has never
                  been this easy. Utilize our tools for automated tracking,
                  ensuring you stay on top of your attendee list effortlessly.
                </p>
                <p>
                  6. Promotional Tools: Spread the word about your event
                  effortlessly. Triket.in provides robust promotional features,
                  including social media sharing and WhatsApp campaigns.
                </p>
                <p>
                  7. Gate Check-In Solutions: No more chaos at the gate! Utilize
                  our streamlined check-in processes, including QR codes and
                  mobile check-ins, for a hassle-free entry experience.
                </p>
                <p>
                  8. Communication Tools: Keep your attendees informed every
                  step of the way. Triket.in offers communication tools for
                  event updates, last-minute changes, and post-event feedback.
                </p>
                <p>
                  9. Support and Resources: Need assistance? Check out our
                  comprehensive support system, including FAQs and tutorials.
                  Contact our dedicated support team for any queries or
                  concerns.
                </p>
                <p>
                  10. Test Event: Familiarize yourself with Triket.in by running
                  a test event. Identify and resolve any potential issues,
                  ensuring a smooth experience for your actual event.
                </p>
                <p>
                  11. Feedback Loop: Your feedback matters! Help us improve by
                  sharing your experiences and suggestions. We're committed to
                  making Triket.in even better with your input. Embark on your
                  event management journey with Triket.in, where organizing
                  events becomes a breeze!
                </p>
              </section>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
        <div className="my-4">
          <OnBoardForm />
        </div>
      </div>
    </>
  )
}

export default GetStarted
