import { CREATE_SPORT_EVENT_PAYMENT } from "@/lib/graphql/mutations/create-sport-event-payment"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import type { FormType } from "@/routes/_landing.msl_.$slug/schema"
import { useMutation } from "@tanstack/react-query"

const useCreateSportEventPayment = () => {
  const graphql = getGraphqlClient()
  const createPayment = useMutation({
    mutationFn: async (data: FormType) => {
      return graphql.request(CREATE_SPORT_EVENT_PAYMENT, {
        ...data,
      })
    },
  })

  return { createPayment }
}

export default useCreateSportEventPayment
