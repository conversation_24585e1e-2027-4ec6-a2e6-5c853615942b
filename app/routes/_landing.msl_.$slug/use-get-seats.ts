import { GET_SEATS_FOR_SPORTS_EVENT } from "@/lib/graphql/queries/get-seats-for-sports-event"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import { useMutation } from "@tanstack/react-query"

const useGetSeats = () => {
  const graphql = getGraphqlClient()
  const getSeats = useMutation({
    // queryKey: ["seats"],
    mutationFn: async (id: number) => {
      return graphql.request(GET_SEATS_FOR_SPORTS_EVENT, {
        ticketTypeId: id,
      })
    },
  })

  return { getSeats }
}

export default useGetSeats
