import toast from "react-hot-toast"
import { useState } from "react"

import { graphql } from "@/__generated__"
import { Button } from "@/components/ui/button"
import { SelectEvent } from "@/features/SuperAdmin"
import { useMutation } from "@tanstack/react-query"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import { parseGraphqlError } from "@/lib/utils/parse-graphql-error"

const NOTIFY_UPCOMING_EVENT_MUTATION = graphql(`
  mutation NotifyUpcomingEvent($id: ID!) {
    notifyUpcomingEvent(id: $id) {
      id
    }
  }
`)

type SelectedEvent = {
  id: string
  name: string
}

const PromoteEvent = () => {
  const [selectedEvent, setSelectedEvent] = useState<SelectedEvent>({
    id: "",
    name: "",
  })
  const graphql = getGraphqlClient()

  const handleSelectedEvent = ({ id, name }: SelectedEvent) => {
    setSelectedEvent({
      id: id,
      name: name,
    })
  }

  const notifyUpcomingEvent = useMutation({
    mutationFn: async (id: string) => {
      await graphql.request(NOTIFY_UPCOMING_EVENT_MUTATION, {
        id: id,
      })
    },
  })

  const handleNotifyEvent = () => {
    if (selectedEvent.id !== "") {
      notifyUpcomingEvent.mutate(selectedEvent.id, {
        onSuccess: () => {
          toast.success("Notify upcoming event success")
        },
        onError: (error) => {
          toast.error(parseGraphqlError(error))
        },
      })
    } else {
      toast.error("Select event first")
    }
  }

  return (
    <div className="mt-8 w-1/2 px-2">
      <SelectEvent
        selectedEvent={selectedEvent}
        handleSelectedEvent={handleSelectedEvent}
      />
      <div className="mt-8">
        <Button
          isLoading={notifyUpcomingEvent.isPending}
          onClick={handleNotifyEvent}
          variant="primaryBlue"
        >
          Promote Event
        </Button>
      </div>
    </div>
  )
}

export default PromoteEvent
