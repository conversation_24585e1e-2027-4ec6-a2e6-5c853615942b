import { useMutation } from "@tanstack/react-query"

import { graphql } from "@/__generated__"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"

const CHECK_PHONEPE_HEALTH = graphql(`
  query CheckPhonepeHealth {
    checkPhonePeHealth {
      data
    }
  }
`)

const PaymentProviderHealth = () => {
  const graphql = getGraphqlClient()
  // const [checkPhonePeHealth, { data, loading, error }] =
  //   useLazyQuery(CHECK_PHONEPE_HEALTH)

  const checkPhonePeHealth = useMutation({
    mutationFn: async () => {
      return graphql.request(CHECK_PHONEPE_HEALTH)
    },
  })

  const handlePhonePeCheck = () => {
    checkPhonePeHealth.mutate()
  }

  return (
    <div className="mt-8 flex flex-col">
      <h1 className="text-2xl font-bold">Check Payment Provider Health</h1>
      <div className="grid grid-cols-12">
        <div className="col-span-12 space-y-2 border-2 p-4 lg:col-span-4">
          <div className="flex flex-col gap-y-4">
            <Label>Phonepe Provider</Label>
            <div>
              <Button
                onClick={handlePhonePeCheck}
                variant="primaryBlue"
                isLoading={checkPhonePeHealth.isPending}
              >
                Check Status
              </Button>
            </div>
          </div>
          {checkPhonePeHealth.isError && (
            <p className="text-red-500">An error has occurred</p>
          )}
          {!checkPhonePeHealth.isPending && !checkPhonePeHealth.isError && (
            <p className="whitespace-pre-line">
              {checkPhonePeHealth?.data?.checkPhonePeHealth?.data}
            </p>
          )}
        </div>
      </div>
    </div>
  )
}

export default PaymentProviderHealth
