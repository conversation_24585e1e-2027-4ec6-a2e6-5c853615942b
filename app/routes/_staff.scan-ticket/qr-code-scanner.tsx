import { useEffect, useRef, useState } from "react"
import {
  type CameraDevice,
  Html5Qrcode,
  Html5QrcodeScannerState,
} from "html5-qrcode"
import toast from "react-hot-toast"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, DialogContent, DialogHeader } from "@/components/ui/dialog"

interface Props {
  onScanResult: (decodedString: string) => void
}

const QRCodeScanner = ({ onScanResult }: Props) => {
  const qrCodeRef = useRef(null)
  const [openCameraSelect, setOpenCameraSelect] = useState(false)
  const [qrCodeScanner, setQrCodeScanner] = useState<Html5Qrcode | null>(null)
  const [cameraList, setCameraList] = useState<CameraDevice[]>([])

  const openCameraModal = () => {
    setOpenCameraSelect(true)
  }

  const closeCameraModal = () => {
    setOpenCameraSelect(false)
  }

  const selectCamera = async () => {
    if (qrCodeScanner?.getState() !== Html5QrcodeScannerState.SCANNING) {
      const cameras = await Html5Qrcode.getCameras()
      setCameraList(cameras)
      openCameraModal()
    }
  }

  const startScan = async (cameraId: string | MediaTrackConstraints) => {
    if (qrCodeRef.current) {
      try {
        const config = {
          fps: 10, // frame per seconds for qr code scanning
          qrbox: { width: 256, height: 256 },
          videoConstraints: {
            facingMode: "environment",
            // aspectRatio: width < 600 ? mobileAspectRatio : aspectRatio,
          },
        }
        await qrCodeScanner?.start(
          cameraId,
          config,

          (success) => {
            onScanResult(success)
          },
          (err) => {
            console.log("startScan error", err)
          }
        )
      } catch (error) {
        console.error("Error starting QR code scanner:", error)
      }
    }
  }

  const stopScanner = () => {
    if (
      qrCodeScanner &&
      (qrCodeScanner.getState() === Html5QrcodeScannerState.SCANNING ||
        Html5QrcodeScannerState.PAUSED)
    ) {
      qrCodeScanner.stop()
    } else {
      toast.error("Cannot stop camera, please refresh this page")
    }
  }

  useEffect(() => {
    setQrCodeScanner(
      // @ts-expect-error id exists
      new Html5Qrcode(qrCodeRef.current?.id, {
        verbose: false,
      })
    )
    return () => {
      qrCodeScanner ? stopScanner() : null
    }
  }, [])

  return (
    <>
      <div
        ref={qrCodeRef}
        id="scanSurface"
        className={"mx-auto flex h-96 w-72 items-center justify-center lg:h-72"}
      />
      <div className="flex flex-col gap-y-8 pt-8">
        <Button variant="primaryBlue" onClick={selectCamera}>
          Start camera
        </Button>
        <Button variant="destructive" onClick={stopScanner}>
          Stop camera
        </Button>
      </div>
      <Dialog open={openCameraSelect} onOpenChange={setOpenCameraSelect}>
        <DialogContent>
          <DialogHeader>Select Camera</DialogHeader>
          <div className="flex flex-col gap-y-4 pt-4">
            {cameraList.map((item) => {
              return (
                <Button
                  key={item.id}
                  className="w-full"
                  onClick={() => {
                    closeCameraModal()
                    startScan(item.id)
                  }}
                >
                  {item.label}
                </Button>
              )
            })}
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}

export default QRCodeScanner
