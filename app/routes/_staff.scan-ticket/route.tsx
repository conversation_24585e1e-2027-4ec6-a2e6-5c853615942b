import { useRef, useState } from "react"
import toast from "react-hot-toast"
import { useNavigate } from "react-router"

import QRCodeScanner from "./qr-code-scanner"

import { IconLeft } from "@components/icons"
import { Loader } from "@components/ui"
import { parseQRCode } from "@lib/utils"
import { graphql } from "@/__generated__"
import { Dialog, DialogContent } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { useMutation } from "@tanstack/react-query"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import { parseGraphqlError } from "@/lib/utils/parse-graphql-error"

const SUBMIT_QR_CODE = graphql(`
  mutation SubmitQRCode($qrCode: String!) {
    submitQRCode(qr_code: $qrCode) {
      error
    }
  }
`)

const ScanTicket = () => {
  const navigate = useNavigate()
  const graphql = getGraphqlClient()

  const [qrCode, setQRCode] = useState("")
  const dialogInitialFocusRef = useRef(null)
  const [customerData, setCustomerData] = useState({
    qrCode: "",
    phoneNumber: "",
    name: "",
    numberOfTickets: "",
    openModal: false,
  })

  const scanQr = useMutation({
    mutationFn: async (qrCode: string) => {
      return await graphql.request(SUBMIT_QR_CODE, {
        qrCode: qrCode,
      })
    },
  })

  const handleQR = async (qrCode: string) => {
    scanQr.mutate(qrCode, {
      onSuccess: (data) => {
        setCustomerData({
          qrCode: "",
          phoneNumber: "",
          name: "",
          numberOfTickets: "",
          openModal: false,
        })
        data?.submitQRCode?.error
          ? toast.error(data?.submitQRCode?.error)
          : toast.success("Success")
      },
      onError: (error) => {
        toast.error(parseGraphqlError(error))
      },
    })
  }

  const onNewScanResult = (decodedText: string) => {
    const userData = parseQRCode(decodedText)
    setCustomerData(() => {
      return {
        qrCode: userData[0],
        phoneNumber: userData[1],
        name: userData[2],
        numberOfTickets: userData[3],
        openModal: true,
      }
    })
    setQRCode(decodedText)
  }

  const toggleModal = (open: boolean) => {
    setCustomerData((prev) => ({
      ...prev,
      openModal: open,
    }))
  }
  return (
    <>
      <div className="relative mx-auto mb-20 max-w-lg pt-8">
        <button
          className="absolute left-0 top-8"
          type="button"
          onClick={() => navigate(-1)}
        >
          <IconLeft className="text-2xl" />
        </button>
        <QRCodeScanner onScanResult={onNewScanResult} />
      </div>

      <Dialog open={customerData.openModal} onOpenChange={toggleModal}>
        <DialogContent>
          <div className="flex flex-col">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold">Scan Result</h2>
            </div>
            {customerData.qrCode.startsWith("invitee") && (
              <div className="flex">
                <p className="w-full font-bold">Special Guest</p>
              </div>
            )}
            <div className="mt-2 grid grid-cols-12">
              <span className="col-span-2">Name</span>
              <span className="col-span-1">:</span>
              <span className="col-span-9">{customerData.name}</span>
            </div>
            <div className="grid grid-cols-12">
              <span className="col-span-2">Phone</span>
              <span className="col-span-1">:</span>
              <span className="col-span-9">{customerData.phoneNumber}</span>
            </div>
            <div className="grid grid-cols-12">
              <span className="col-span-12">
                Admit {customerData.numberOfTickets} person(s)
              </span>
            </div>
          </div>
          <Button
            onClick={() => handleQR(qrCode)}
            variant="primaryBlue"
            ref={dialogInitialFocusRef}
          >
            {scanQr.isPending ? (
              <Loader variant={"smallWhite"} />
            ) : (
              <>Check-In</>
            )}
          </Button>
        </DialogContent>
      </Dialog>
    </>
  )
}

export default ScanTicket
