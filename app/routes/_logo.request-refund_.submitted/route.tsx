import { NavLink } from "react-router";

const RequestRefundSubmitted = () => {
  return (
    <div className="mx-auto flex max-w-4xl flex-col gap-y-4 px-4 md:px-10">
      <h1 className="mt-10 text-3xl font-bold">Refund request submitted</h1>
      <p>
        Your refund is being processed. It can take up to 7 business days to
        reflect in your account. Thank you for your patience.
      </p>
      <p className="mb-4">
        For more information, please refer to our{" "}
        <NavLink
          className="text-primary-blue"
          to="/terms-and-conditions#refunds"
        >
          refund policy.
        </NavLink>
      </p>
    </div>
  )
}

export default RequestRefundSubmitted
