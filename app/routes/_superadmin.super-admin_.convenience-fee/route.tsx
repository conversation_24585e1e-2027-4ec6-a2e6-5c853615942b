import { zod<PERSON><PERSON><PERSON><PERSON> } from "@hookform/resolvers/zod"
import { useEffect, useRef } from "react"
import { useFieldArray, useForm } from "react-hook-form"
import toast from "react-hot-toast"
import { z } from "zod"

import { graphql } from "@/__generated__"
import { Loader } from "@components/ui"
import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { ErrorComponent } from "@/components/common"
import { useMutation, useQuery } from "@tanstack/react-query"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"

const UPSERT_KEY = "convenience_fee"
const NUMBER_OF_ITEMS = 6

const fieldSchema = z.object({
  start: z.string().optional(),
  end: z.string().optional(),
  value: z.string().optional(),
})

const schema = z.object({
  items: z.array(fieldSchema),
})

type FormSchemaType = z.infer<typeof schema>

const UPSERT_SETTING_MUTATION = graphql(`
  mutation UpsertSetting($key: String!, $value: String) {
    upsertSetting(key: $key, value: $value) {
      id
    }
  }
`)

const GET_SETTING_QUERY = graphql(`
  query GetSettingByKey($key: String) {
    getSettingByKey(key: $key) {
      id
      key
      value
    }
  }
`)

const SuperAdminConvenienceFee = () => {
  const form = useForm<FormSchemaType>({
    resolver: zodResolver(schema),
  })

  const graphql = getGraphqlClient()

  const appendRef = useRef(false)

  const upsertSetting = useMutation({
    mutationFn: async (data: FormSchemaType) => {
      return await graphql.request(UPSERT_SETTING_MUTATION, {
        key: UPSERT_KEY,
        value: JSON.stringify(data),
      })
    },
  })

  // const [upsertSetting, { loading, client }] = useMutation(
  //   UPSERT_SETTING_MUTATION,
  //   {
  //     onCompleted: () => {
  //       toast.success("success")
  //       client.refetchQueries({
  //         include: [GET_SETTING_QUERY],
  //       })
  //     },
  //   }
  // )
  const { isLoading, isError } = useQuery({
    queryKey: ["get-setting", UPSERT_KEY],
    queryFn: async () => {
      return await graphql.request(GET_SETTING_QUERY, {
        key: UPSERT_KEY,
      })
    },
  })

  // const { loading: loadingSetting, error: settingError } = useQuery(
  //   GET_SETTING_QUERY,
  //   {
  //     variables: {
  //       key: UPSERT_KEY,
  //     },
  //     onCompleted: (data) => {
  //       const arrayedData = data?.getSettingByKey?.value
  //         ? JSON.parse(data?.getSettingByKey?.value)
  //         : ""
  //       form.reset({ items: arrayedData.items })
  //     },
  //   }
  // )

  const { fields, append } = useFieldArray<FormSchemaType>({
    control: form.control,
    name: "items",
  })

  const submitForm = async (data: FormSchemaType) => {
    upsertSetting.mutate(data, {
      onSuccess: () => {
        toast.success("Success")
      },
    })
    // await upsertSetting.mutate({
    //   variables: {
    //     key: UPSERT_KEY,
    //     value: JSON.stringify(data),
    //   },
    // })
  }

  useEffect(() => {
    if (appendRef.current) return
    appendRef.current = true
    for (let i = 0; i < NUMBER_OF_ITEMS; i++) {
      append({
        start: undefined,
        end: undefined,
        value: undefined,
      })
    }
  }, [append])

  if (isLoading) {
    return (
      <div className="flex h-36 items-center justify-center">
        <Loader variant="large" />
      </div>
    )
  }

  if (isError) {
    return (
      <div className="flex h-36 items-center justify-center">
        <ErrorComponent />
      </div>
    )
  }

  return (
    <div className="mt-8 size-full px-2">
      <h1 className="text-2xl font-bold">Convenience Fee</h1>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(submitForm)}
          className="flex flex-col gap-y-8 py-8"
        >
          {fields.map((_, index) => {
            return (
              <div
                key={index}
                className="grid w-full grid-cols-12 gap-4 border p-4 focus-within:ring-2 focus-within:ring-black "
              >
                <FormField
                  control={form.control}
                  name={`items.${index}.start`}
                  render={({ field }) => {
                    return (
                      <FormItem className="col-span-4">
                        <FormLabel>Start</FormLabel>
                        <FormControl>
                          <Input
                            className="col-span-4"
                            {...field}
                            placeholder="0"
                          />
                        </FormControl>
                      </FormItem>
                    )
                  }}
                />
                <FormField
                  control={form.control}
                  name={`items.${index}.end`}
                  render={({ field }) => {
                    return (
                      <FormItem className="col-span-4">
                        <FormLabel>End</FormLabel>
                        <FormControl>
                          <Input
                            className="col-span-4"
                            {...field}
                            placeholder="0"
                          />
                        </FormControl>
                      </FormItem>
                    )
                  }}
                />
                <FormField
                  control={form.control}
                  name={`items.${index}.value`}
                  render={({ field }) => {
                    return (
                      <FormItem className="col-span-4">
                        <FormLabel>Value</FormLabel>
                        <FormControl>
                          <Input
                            className="col-span-4"
                            {...field}
                            placeholder="0"
                          />
                        </FormControl>
                      </FormItem>
                    )
                  }}
                />
              </div>
            )
          })}
          <div>
            <Button
              variant="primaryBlue"
              isLoading={upsertSetting.isPending}
              type="submit"
            >
              Submit
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}

export default SuperAdminConvenienceFee
