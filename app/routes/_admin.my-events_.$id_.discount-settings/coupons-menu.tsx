import { useState } from "react"

import DeleteCouponModal from "./delete-coupon-modal"
import UpdateCouponModal from "./update-coupon-modal"

import { IconEdit, IconTrash, IconVertical } from "@/components/icons"
import { Dialog, DialogTrigger } from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import type { Coupon } from "@/__generated__/graphql"

interface Props {
  couponId: string
  eventId: string
  coupon: Partial<Coupon>
}

const CouponsMenu = ({ couponId, eventId, coupon }: Props) => {
  const Dialogs = {
    delete: "delete",
    update: "update",
  }

  const [open, setOpen] = useState(false)
  const [dialog, setDialog] = useState("")

  const closeDialog = () => {
    setOpen(false)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DropdownMenu>
        <DropdownMenuTrigger>
          <IconVertical className="text-slate-500" />
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DialogTrigger
            asChild
            onClick={() => {
              setDialog(Dialogs.delete)
            }}
          >
            <DropdownMenuItem>
              <IconTrash className="mr-2 size-4 text-destructive" /> Delete
            </DropdownMenuItem>
          </DialogTrigger>
          <DialogTrigger
            asChild
            onClick={() => {
              setDialog(Dialogs.update)
            }}
          >
            <DropdownMenuItem>
              <IconEdit className="mr-2 size-4 text-green-700" /> Edit
            </DropdownMenuItem>
          </DialogTrigger>
        </DropdownMenuContent>
      </DropdownMenu>
      {dialog === Dialogs.delete && (
        <DeleteCouponModal couponId={couponId} closeDialog={closeDialog} />
      )}
      {dialog === Dialogs.update && (
        <UpdateCouponModal
          couponId={couponId}
          eventId={eventId}
          coupon={coupon}
          closeDialog={closeDialog}
        />
      )}
    </Dialog>
  )
}

export default CouponsMenu
