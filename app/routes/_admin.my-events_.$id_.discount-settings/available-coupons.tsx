import dayjs from "dayjs"

import CouponsMenu from "./coupons-menu"

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Loader } from "@/components/ui"
import useGetCoupons from "@/lib/hooks/use-get-coupons"

interface Props {
  eventId: string
}

const AvailableCoupons = ({ eventId }: Props) => {
  const { data, isLoading, isError } = useGetCoupons(eventId)

  if (isLoading) {
    return (
      <div className="mt-4 flex w-full justify-center">
        <Loader />
      </div>
    )
  }

  if (isError) {
    return <div className="mt-4">Error fetching coupons.</div>
  }

  return (
    <Accordion
      defaultValue={isLoading ? "loading" : "coupons"}
      collapsible
      type="single"
      className="w-full"
    >
      <AccordionItem value="coupons" className="border-b-0">
        <AccordionTrigger>Available Coupons</AccordionTrigger>
        <AccordionContent className="grid grid-cols-12 gap-4">
          {data?.getCouponsForEvent?.length === 0 && (
            <p className="col-span-12">You haven't created any coupons</p>
          )}
          {data?.getCouponsForEvent?.map((item) => {
            return (
              item && (
                <Card key={item.id} className="col-span-12">
                  <CardHeader className="relative grid grid-cols-12 space-y-0 p-0">
                    <div className="absolute right-2 top-2">
                      <CouponsMenu
                        couponId={item.id}
                        eventId={eventId}
                        coupon={item}
                      />
                    </div>
                    <div className="relative col-span-3 flex items-center justify-center rounded-l-lg border-r bg-green-700">
                      <CardTitle className="flex items-center justify-between text-xl text-white">
                        {item.discount_percent}%
                      </CardTitle>
                    </div>
                    <div className="col-span-9 py-1 pl-4">
                      <CardTitle className="flex items-center justify-between text-lg">
                        {item.name}
                      </CardTitle>
                      <CardDescription>
                        <span className="inline-block w-full">
                          Max usage: {item.max_usage}/{item.coupon_usage_count}
                        </span>
                        <span className="inline-block w-full">
                          Expiry date:{" "}
                          {dayjs(item.expiry_date).format("DD-MMM-YY")}
                        </span>
                        <span className="inline-block w-full">
                          Published: {item.is_published ? "Yes" : "No"}
                        </span>
                      </CardDescription>
                    </div>
                  </CardHeader>
                </Card>
              )
            )
          })}
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  )
}

export default AvailableCoupons
