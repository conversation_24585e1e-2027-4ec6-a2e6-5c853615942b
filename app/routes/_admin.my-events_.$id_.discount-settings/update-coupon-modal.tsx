import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import toast from "react-hot-toast"
import dayjs from "dayjs"

import { graphql } from "@/__generated__"
import type { Coupon } from "@/__generated__/graphql"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { type CouponType, couponSchema } from "@/lib/types/couponTypes"
import { PUBLISH_STATUS } from "@/lib/utils"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import { parseGraphqlError } from "@/lib/utils/parse-graphql-error"

const UPDATE_COUPON = graphql(`
  mutation UpdateCoupon(
    $couponId: ID!
    $name: String
    $expiry_date: DateTime
    $event_id: ID
    $discount_percent: Float
    $max_usage: Int
    $is_published: Boolean
  ) {
    updateCoupon(
      coupon_id: $couponId
      name: $name
      expiry_date: $expiry_date
      event_id: $event_id
      discount_percent: $discount_percent
      max_usage: $max_usage
      is_published: $is_published
    ) {
      id
    }
  }
`)

interface Props {
  couponId: string
  eventId: string
  closeDialog: () => void
  coupon: Partial<Coupon> // Coupon is different from CouponType, Coupon is the type returned by the server
}

const UpdateCouponModal = ({
  closeDialog,
  couponId,
  coupon,
  eventId,
}: Props) => {
  const graphql = getGraphqlClient()
  const queryClient = useQueryClient()

  const form = useForm<CouponType>({
    // CouponType is the zod schema type for creating coupons
    resolver: zodResolver(couponSchema),
    defaultValues: {
      name: coupon.name,
      event_id: eventId,
      max_usage: coupon.max_usage || 0,
      is_published: coupon.is_published,
      expiry_date: dayjs(coupon.expiry_date).format("YYYY-MM-DD"),
      discount_percent: coupon.discount_percent,
    },
  })

  const updateCoupon = useMutation({
    mutationFn: async (data: CouponType) => {
      return await graphql.request(UPDATE_COUPON, {
        ...data,
        couponId: couponId,
      })
    },
  })

  const handleUpdateCoupon = async (data: CouponType) => {
    updateCoupon.mutate(data, {
      onSuccess: () => {
        toast.success("Coupon updated")
        queryClient.invalidateQueries({
          queryKey: ["coupons"],
        })
        closeDialog()
      },
      onError: (error) => {
        toast.error(parseGraphqlError(error))
      },
    })
  }

  return (
    <DialogContent className="">
      <DialogHeader>
        <DialogTitle className="text-left text-base">Update Coupon</DialogTitle>
      </DialogHeader>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(handleUpdateCoupon)}
          className="mt-4 flex flex-col space-y-4"
        >
          <div className="relative flex flex-col gap-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input
                      autoComplete="off"
                      placeholder="Coupon name"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="discount_percent"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Discount Value</FormLabel>
                  <FormControl>
                    <Input placeholder="Discount Value" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="max_usage"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Quantity</FormLabel>
                  <FormControl>
                    <Input placeholder="Quantity" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="expiry_date"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Expiry date</FormLabel>
                  <FormControl>
                    <Input className="block" type="date" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="is_published"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Publish?</FormLabel>
                  <Select
                    onValueChange={(item) => {
                      if (item === "Yes") {
                        field.onChange(true)
                      } else if (item === "No") {
                        field.onChange(false)
                      }
                    }}
                    defaultValue={field.value ? "Yes" : "No"}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select Publish Status" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {PUBLISH_STATUS.map((item) => (
                        <SelectItem key={item.name} value={item.name}>
                          {item.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <div className="space-x-8 pt-4">
            <Button isLoading={updateCoupon.isPending} type="submit">
              Update
            </Button>
            <Button type="button" onClick={closeDialog} variant="secondary">
              Cancel
            </Button>
          </div>
        </form>
      </Form>
    </DialogContent>
  )
}

export default UpdateCouponModal
