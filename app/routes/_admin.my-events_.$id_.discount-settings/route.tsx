import { useParams } from "react-router";

import AvailableCoupons from "./available-coupons"
import CreateCouponForm from "./create-coupon-form"

import { BackButton } from "@components/common"

const DiscountSettings = () => {
  const { id } = useParams()

  return (
    <>
      <div className="flex max-w-md flex-col md:mx-auto">
        <div className="flex">
          <BackButton />
          <h1 className="w-full text-center font-bold">Discount Settings</h1>
        </div>
        <AvailableCoupons eventId={id!} />
        <div className="my-8 w-full">
          <CreateCouponForm eventId={id!} />
        </div>
      </div>
    </>
  )
}

export default DiscountSettings
