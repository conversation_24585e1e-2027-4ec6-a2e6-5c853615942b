import toast from "react-hot-toast"

import { graphql } from "@/__generated__"
import { Button } from "@/components/ui/button"
import {
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import { parseGraphqlError } from "@/lib/utils/parse-graphql-error"

const DELETE_COUPON = graphql(`
  mutation DeleteCoupon($couponId: ID!) {
    deleteCoupon(coupon_id: $couponId) {
      id
    }
  }
`)

interface Props {
  couponId: string
  closeDialog: () => void
}

export default function DeleteCouponModal({ couponId, closeDialog }: Props) {
  const graphql = getGraphqlClient()
  const queryClient = useQueryClient()

  const deleteCoupon = useMutation({
    mutationFn: async (couponId: string) => {
      return graphql.request(DELETE_COUPON, {
        couponId: couponId,
      })
    },
  })

  const handleDeleteCoupon = async () => {
    deleteCoupon.mutate(couponId, {
      onSuccess: () => {
        toast.success("Coupon deleted successfully")
        queryClient.invalidateQueries({
          queryKey: ["coupons"],
        })
        closeDialog()
      },
      onError: (error) => {
        toast.error(parseGraphqlError(error))
      },
    })
  }

  return (
    <DialogContent className="w-full max-w-sm">
      <DialogHeader>
        <DialogTitle className="text-left text-base">
          Delete coupon?
        </DialogTitle>
      </DialogHeader>
      <div className="space-x-8">
        <Button
          isLoading={deleteCoupon.isPending}
          onClick={handleDeleteCoupon}
          variant="destructive"
        >
          Delete
        </Button>
        <Button onClick={closeDialog} variant="secondary">
          Cancel
        </Button>
      </div>
    </DialogContent>
  )
}
