import { useField<PERSON>rray, useForm } from "react-hook-form"
import { zodR<PERSON>olver } from "@hookform/resolvers/zod"
import { useState } from "react"
import toast from "react-hot-toast"

import { graphql } from "@/__generated__"
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils/shadcnUtil"
import { Button, buttonVariants } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { PUBLISH_STATUS } from "@/lib/utils"
import {
  type CreateCouponType,
  createCouponSchema,
} from "@/lib/types/couponTypes"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import { parseGraphqlError } from "@/lib/utils/parse-graphql-error"

const CREATE_COUPON = graphql(`
  mutation CreateCoupon($data: [CreateCouponInput!]!) {
    createCoupon(data: $data) {
      message
    }
  }
`)

interface Props {
  eventId: string // event_id
}

// Grapqhl expects array of objects for the create coupon mutation
// but our modal is gonna submit it one item at a time
export default function CreateCouponForm({ eventId }: Props) {
  const [openModal, setOpenModal] = useState(false)
  const queryClient = useQueryClient()
  const graphql = getGraphqlClient()

  const form = useForm<CreateCouponType>({
    resolver: zodResolver(createCouponSchema),
    defaultValues: {
      data: [
        {
          event_id: eventId,
          name: "",
          is_published: false,
          expiry_date: "",
          max_usage: undefined,
          discount_percent: undefined,
        },
      ],
    },
  })

  const control = form.control

  const { fields, remove, append } = useFieldArray<CreateCouponType>({
    control,
    name: "data",
  })

  const createCoupon = useMutation({
    mutationFn: async (data: CreateCouponType) => {
      return graphql.request(CREATE_COUPON, {
        ...data,
      })
    },
  })

  const handleCreateCoupon = async (data: CreateCouponType) => {
    createCoupon.mutate(data, {
      onSuccess: () => {
        toast.success("Coupon created successfully")
        queryClient.invalidateQueries({
          queryKey: ["coupons"],
        })
        setOpenModal(false)
        remove(0)
      },
      onError: (error) => {
        toast.error(parseGraphqlError(error))
      },
    })
  }

  const handleDialogChange = (open: boolean) => {
    if (fields.length === 0) {
      append({
        event_id: eventId,
        name: "",
        is_published: false,
        expiry_date: "",
        // @ts-expect-error this throws error for some reason cause it's under an array, it works for defaultValues
        max_usage: undefined,
        // @ts-expect-error this throws error for some reason cause it's under an array, it works for defaultValues
        discount_percent: undefined,
      })
    }
    setOpenModal(open)
  }

  return (
    <Dialog open={openModal} onOpenChange={handleDialogChange}>
      <DialogTrigger
        className={cn(buttonVariants({ variant: "outline" }), "w-full")}
      >
        Add Coupon
      </DialogTrigger>
      <DialogContent>
        <DialogHeader className="text-left font-bold">
          Create coupon
        </DialogHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleCreateCoupon)}
            className="flex flex-col gap-y-6"
          >
            {fields.map((_, index) => {
              return (
                <div className="relative flex flex-col gap-y-4" key={index}>
                  <FormField
                    control={form.control}
                    name={`data.${index}.name`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Name</FormLabel>
                        <FormControl>
                          <Input
                            autoComplete="off"
                            placeholder="Coupon name"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name={`data.${index}.discount_percent`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Discount Percent</FormLabel>
                        <FormControl>
                          <Input placeholder="Discount Percent" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name={`data.${index}.max_usage`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Quantity</FormLabel>
                        <FormControl>
                          <Input placeholder="Quantity" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name={`data.${index}.expiry_date`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Expiry date</FormLabel>
                        <FormControl>
                          <Input className="block" type="date" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name={`data.${index}.is_published`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Publish?</FormLabel>
                        <Select
                          onValueChange={(item) => {
                            if (item === "Yes") {
                              field.onChange(true)
                            } else if (item === "No") {
                              field.onChange(false)
                            }
                          }}
                          defaultValue={field.value ? "Yes" : "No"}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select Publish Status" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {PUBLISH_STATUS.map((item) => (
                              <SelectItem key={item.name} value={item.name}>
                                {item.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              )
            })}
            <Button
              isLoading={createCoupon.isPending}
              type="submit"
              variant="primaryBlue"
            >
              Create Coupon
            </Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
