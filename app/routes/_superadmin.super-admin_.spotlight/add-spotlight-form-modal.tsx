import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogDescription,
  <PERSON>alogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { useForm } from "react-hook-form"
import { type FormSchemaType, schema } from "./spotlight-schema"
import { zodResolver } from "@hookform/resolvers/zod"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils/shadcnUtil"
import { Button, buttonVariants } from "@/components/ui/button"
import { type ChangeEvent, useState } from "react"
import { Checkbox } from "@/components/ui/checkbox"
import ADD_SPOTLIGHT_MUTATION from "@/lib/graphql/mutations/add-spotlight"
import toast from "react-hot-toast"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { graphqlClient } from "@/lib/hooks/graphql-client"

const AddSpotlightFormModal = () => {
  const [open, setOpen] = useState(false)
  const form = useForm<FormSchemaType>({
    resolver: zodResolver(schema),
    defaultValues: {
      image: undefined,
      url: "",
      isExternal: false,
      order: undefined,
    },
  })
  const queryClient = useQueryClient()

  const addSpotlight = useMutation({
    mutationFn: async (data: FormSchemaType) => {
      const graphql = await graphqlClient()
      return await graphql.request({
        document: ADD_SPOTLIGHT_MUTATION,
        variables: {
          ...data,
        },
      })
    },
    onSuccess: () => {
      toast.success("Spotlight added")
      form.reset()
      setOpen(false)
      queryClient.invalidateQueries({
        queryKey: ["get-spotlight-list"],
      })
    },
  })

  const handleFileInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files) {
      form.setValue("image", files[0])
    }
  }

  const handleSubmit = (data: FormSchemaType) => {
    addSpotlight.mutate(data)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger className={cn(buttonVariants({ variant: "outline" }))}>
        Add spotlight
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add spotlight</DialogTitle>
          <DialogDescription className="sr-only">
            Add spotlight info
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form
            className="space-y-4"
            onSubmit={form.handleSubmit(handleSubmit)}
          >
            <FormField
              control={form.control}
              name="image"
              render={() => (
                <FormItem>
                  <FormLabel required>Spotlight image</FormLabel>
                  <FormControl>
                    <Input
                      type="file"
                      placeholder="Upload spotlight image"
                      onChange={handleFileInputChange}
                      multiple={false}
                      accept="image/jpg, image/png, image/jpeg"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="url"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Url</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter url for spotlight" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="order"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Order</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter order" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="isExternal"
              render={({ field }) => {
                return (
                  <FormItem className="flex items-center space-x-2">
                    <FormLabel className="flex items-center">
                      External link?
                    </FormLabel>
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )
              }}
            />
            <div className="flex justify-end gap-x-4">
              <Button
                type="button"
                onClick={() => {
                  form.reset()
                  setOpen(false)
                }}
                variant="secondary"
              >
                Cancel
              </Button>
              <Button
                isLoading={addSpotlight.isPending}
                type="submit"
                variant="primaryBlue"
              >
                Submit
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}

export default AddSpotlightFormModal
