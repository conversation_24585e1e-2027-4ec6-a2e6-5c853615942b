import { create } from "zustand"
import type { SpotlightItem } from "./spotlight-schema"

interface Store {
  updateDialog: {
    open: boolean
    spotlight: SpotlightItem | undefined
  }
  setUpdateDialog: (newState: {
    open: boolean
    spotlight: SpotlightItem | undefined
  }) => void

  deleteDialog: {
    id: string
    open: boolean
  }
  setDeleteDialog: (newState: { id: string; open: boolean }) => void
}

const useSpotlightListStore = create<Store>((set) => ({
  updateDialog: {
    open: false,
    spotlight: undefined,
  },
  setUpdateDialog: (newState) =>
    set({
      updateDialog: {
        open: newState.open,
        spotlight: newState.spotlight,
      },
    }),

  deleteDialog: {
    id: "",
    open: false,
  },
  setDeleteDialog: (newState) =>
    set({
      deleteDialog: {
        id: newState.id,
        open: newState.open,
      },
    }),
}))

export default useSpotlightListStore
