import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogDes<PERSON>,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import useSpotlightListStore from "./store"
import { type ChangeEvent } from "react"
import { useForm } from "react-hook-form"
import { updateSchema, type UpdateFormSchemaType } from "./spotlight-schema"
import { zodResolver } from "@hookform/resolvers/zod"
import { Checkbox } from "@/components/ui/checkbox"
import UPDATE_SPOTLIGHT_MUTATION from "@/lib/graphql/mutations/update-spotlight"
import toast from "react-hot-toast"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import getGraphqlUploadClient from "@/lib/hooks/get-graphql-upload-client"
import { parseGraphqlError } from "@/lib/utils/parse-graphql-error"

const UpdateSpotlightFormModal = () => {
  const graphql = getGraphqlUploadClient({
    fieldName: "image",
    graphqlDocument: UPDATE_SPOTLIGHT_MUTATION,
  })
  const queryClient = useQueryClient()

  const { updateDialog, setUpdateDialog } = useSpotlightListStore()

  const updateSpotlight = useMutation({
    mutationFn: async ({
      data,
      id,
    }: {
      data: UpdateFormSchemaType
      id: number
    }) => {
      return await graphql.request(UPDATE_SPOTLIGHT_MUTATION, {
        data,
        id: id,
      })
    },
  })

  const form = useForm<UpdateFormSchemaType>({
    resolver: zodResolver(updateSchema),
    defaultValues: {
      image: undefined,
      url: updateDialog.spotlight?.url || "",
      isExternal: updateDialog.spotlight?.is_external || false,
      order: updateDialog.spotlight?.order || undefined,
    },
  })

  const handleFileInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files) {
      form.setValue("image", files[0])
    }
  }

  const handleDialogChange = (open: boolean) => {
    setUpdateDialog({
      open: open,
      spotlight: undefined,
    })
  }

  const handleSubmit = async (data: UpdateFormSchemaType) => {
    if (updateDialog?.spotlight?.id) {
      updateSpotlight.mutate(
        {
          data: data,
          id: parseInt(updateDialog.spotlight.id),
        },
        {
          onSuccess: () => {
            toast.success("Spotlight updated")
            queryClient.invalidateQueries({
              queryKey: ["get-spotlight-list"],
            })
            handleDialogChange(false)
          },
          onError: (error) => {
            toast.error(parseGraphqlError(error))
          },
        }
      )
    }
  }

  return (
    <Dialog open={updateDialog.open} onOpenChange={handleDialogChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Update spotlight</DialogTitle>
          <DialogDescription className="sr-only">
            Update spotlight info
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form
            className="space-y-4"
            onSubmit={form.handleSubmit(handleSubmit)}
          >
            <FormField
              control={form.control}
              name="image"
              render={() => (
                <FormItem>
                  <FormLabel required>Spotlight image</FormLabel>
                  <FormControl>
                    <Input
                      type="file"
                      placeholder="Upload spotlight image"
                      onChange={handleFileInputChange}
                      multiple={false}
                      accept="image/jpg, image/png, image/jpeg"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="url"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Url</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter url for spotlight" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="order"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Order</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter order" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="isExternal"
              render={({ field }) => {
                return (
                  <FormItem className="flex items-center space-x-2">
                    <FormLabel className="flex items-center">
                      External link?
                    </FormLabel>
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )
              }}
            />
            <div className="flex justify-end gap-x-4">
              <Button
                type="button"
                onClick={() => {
                  form.reset()
                  handleDialogChange(false)
                }}
                variant="secondary"
              >
                Cancel
              </Button>
              <Button
                isLoading={updateSpotlight.isPending}
                type="submit"
                variant="primaryBlue"
              >
                Submit
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}

export default UpdateSpotlightFormModal
