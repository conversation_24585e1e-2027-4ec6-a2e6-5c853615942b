import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>D<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogTitle,
} from "@/components/ui/dialog"
import useSpotlightListStore from "./store"
import { Button } from "@/components/ui/button"
import type { FormEvent } from "react"

import DELETE_SPOTLIGHT from "@/lib/graphql/mutations/delete-spotlight"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import toast from "react-hot-toast"
import { parseGraphqlError } from "@/lib/utils/parse-graphql-error"

const DeleteSpotlightModal = () => {
  const store = useSpotlightListStore()
  const graphql = getGraphqlClient()
  const queryClient = useQueryClient()

  const deleteSpotlight = useMutation({
    mutationFn: async (id: number) => {
      return await graphql.request(DELETE_SPOTLIGHT, {
        id: id,
      })
    },
  })

  const handleDialogChange = (open: boolean) => {
    store.setDeleteDialog({
      id: "",
      open: open,
    })
  }

  const handleDelete = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    deleteSpotlight.mutate(parseInt(store.deleteDialog.id), {
      onSuccess: () => {
        toast.success("Spotlight deleted")
        queryClient.invalidateQueries({
          queryKey: ["get-spotlight-list"],
        })
        handleDialogChange(false)
      },
      onError: (error) => {
        toast.error(parseGraphqlError(error))
      },
    })
  }

  return (
    <Dialog open={store.deleteDialog.open} onOpenChange={handleDialogChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Delete spotlight?</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete this spotlight item?
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="gap-4">
          <Button onClick={() => handleDialogChange(false)} variant="secondary">
            Cancel
          </Button>
          <form onSubmit={handleDelete}>
            <Button
              className="w-full"
              type="submit"
              isLoading={deleteSpotlight.isPending}
              variant="destructive"
            >
              Confirm
            </Button>
          </form>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default DeleteSpotlightModal
