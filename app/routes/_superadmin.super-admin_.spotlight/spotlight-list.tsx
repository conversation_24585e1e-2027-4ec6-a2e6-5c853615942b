import { ErrorComponent } from "@/components/common"
import { Loader } from "@/components/ui"
import GET_SPOTLIGHT_LIST from "@/lib/graphql/queries/get-spotlight-list"
import { baseUrl } from "@/lib/utils"
import SpotlightListSettings from "./spotlight-list-settings"
import type { SpotlightItem } from "./spotlight-schema"
import { useQuery } from "@tanstack/react-query"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"

const SpotlightList = () => {
  const graphql = getGraphqlClient()
  const { data, isLoading, isError } = useQuery({
    queryKey: ["get-spotlight-list"],
    queryFn: async () => {
      return graphql.request(GET_SPOTLIGHT_LIST)
    },
  })

  if (isLoading) {
    return <Loader />
  }

  if (isError) {
    return (
      <div>
        <ErrorComponent />
      </div>
    )
  }

  return (
    <div className="grid grid-cols-12 gap-y-8 lg:gap-8 lg:gap-y-0 ">
      {data?.getSpotlightList?.map((item) => {
        const spotlightItem: SpotlightItem = {
          id: item.id,
          is_external: item.is_external,
          order: item.order,
          url: item.url,
        }
        return (
          <div
            key={item.id}
            className="relative col-span-12 rounded-lg border p-4 lg:col-span-3"
          >
            <div className="flex size-full flex-col">
              <img
                className="aspect-video size-full object-cover"
                alt={item.url || ""}
                src={`${baseUrl}/image/medium/${item.image.path}`}
              />
              <div className="mt-4">Url: {item.url}</div>
              <div>External url: {item?.is_external ? "Yes" : "No"}</div>
              <div>Order: {item.order}</div>
            </div>
            <div className="absolute bottom-2 right-2">
              <SpotlightListSettings spotlightItem={spotlightItem} />
            </div>
          </div>
        )
      })}
    </div>
  )
}

export default SpotlightList
