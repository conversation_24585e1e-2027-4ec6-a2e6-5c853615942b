import type { Spotlight } from "@/__generated__/graphql"
import { z } from "zod"

export const schema = z.object({
  image: z.instanceof(File),
  url: z.string().optional(),
  isExternal: z.boolean(),
  order: z.coerce.number().optional(),
})

export const updateSchema = z.object({
  image: z.instanceof(File).optional(),
  url: z.string().optional(),
  isExternal: z.boolean(),
  order: z.coerce.number().optional(),
})

export type FormSchemaType = z.infer<typeof schema>

export type UpdateFormSchemaType = z.infer<typeof updateSchema>

export type SpotlightItem = Omit<Spotlight, "image" | "app_image_id">
