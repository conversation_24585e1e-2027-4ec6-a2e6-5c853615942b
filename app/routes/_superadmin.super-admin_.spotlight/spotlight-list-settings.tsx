import IconSettings from "@/components/icons/icon-settings"
import { buttonVariants, Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { cn } from "@/lib/utils/shadcnUtil"
import useSpotlightListStore from "./store"
import type { SpotlightItem } from "./spotlight-schema"

interface Props {
  spotlightItem: SpotlightItem
}

const SpotlightListSettings = ({ spotlightItem }: Props) => {
  const store = useSpotlightListStore()

  const handleDelete = () => {
    store.setDeleteDialog({
      id: spotlightItem.id,
      open: true,
    })
  }

  const handleUpdate = () => {
    store.setUpdateDialog({
      open: true,
      spotlight: spotlightItem,
    })
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger
        className={cn(buttonVariants({ variant: "ghost", size: "icon" }))}
      >
        <IconSettings />
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        <DropdownMenuItem asChild>
          <Button className="w-full" variant="ghost" onClick={handleUpdate}>
            Update
          </Button>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Button className="w-full" variant="ghost" onClick={handleDelete}>
            Delete
          </Button>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export default SpotlightListSettings
