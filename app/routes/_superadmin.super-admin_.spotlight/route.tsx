import useSpotlightListStore from "@/routes/_superadmin.super-admin_.spotlight/store"
import AddSpotlightFormModal from "./add-spotlight-form-modal"
import DeleteSpotlightModal from "./delete-spotlight-modal"
import SpotlightList from "./spotlight-list"
import UpdateSpotlightFormModal from "./update-spotlight-form-modal"

const SuperadminSpotlight = () => {
  const { updateDialog } = useSpotlightListStore()
  return (
    <>
      <div className="mt-8 size-full px-2">
        <h1 className="text-2xl font-bold">Spotlight</h1>
        <div className="my-4">
          <AddSpotlightFormModal />
        </div>
        <div className="my-4">
          <SpotlightList />
        </div>
      </div>
      <DeleteSpotlightModal />
      {updateDialog.open && <UpdateSpotlightFormModal />}
    </>
  )
}

export default SuperadminSpotlight
