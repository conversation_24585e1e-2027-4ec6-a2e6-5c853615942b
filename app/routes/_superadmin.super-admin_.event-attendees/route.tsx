import { useNavigate, useSearchParams } from "react-router";
import { useState } from "react"

import AttendeesTable from "./attendees-table"

import { IconRefresh } from "@/components/icons"
import { SelectEvent } from "@/features/SuperAdmin"
import SelectEventType from "@/routes/_superadmin.super-admin_.event-attendees/select-event-type"

const EventAttendees = () => {
  const [searchParams, setSearchParams] = useSearchParams()
  const navigate = useNavigate()
  const [selectedEvent, setSelectedEvent] = useState({
    id: "",
    name: "",
  })

  const handleSelectedEvent = async ({
    id,
    name,
  }: {
    id: string
    name: string
  }) => {
    if (id === selectedEvent.id) return

    setSelectedEvent({
      id: id,
      name: name,
    })

    setSearchParams((params) => {
      params.set("event_id", id)
      params.delete("paymentStatus")
      params.delete("keyword")
      params.delete("page")
      return params
    })

    navigate({ search: searchParams.toString() })
  }

  const handleRefetch = async () => {
    window.location.reload()
  }

  return (
    <div className="mt-8 px-2">
      <div className="flex w-1/2 gap-x-4">
        <div className="flex w-full flex-col justify-center gap-4">
          <SelectEventType />
          <div className="flex items-center">
            <SelectEvent
              selectedEvent={selectedEvent}
              handleSelectedEvent={handleSelectedEvent}
            />
            <button
              onClick={handleRefetch}
              className="flex size-8 items-center justify-center"
            >
              <IconRefresh />
            </button>
          </div>
        </div>
      </div>
      {searchParams.get("event_id") && (
        <AttendeesTable token={""} eventId={searchParams.get("event_id")!} />
      )}
    </div>
  )
}

export default EventAttendees
