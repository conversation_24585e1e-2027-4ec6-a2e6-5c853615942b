import { useState } from "react"
import { toast } from "react-hot-toast"

import { parseRefundStatus } from "@lib/utils"
import { graphql } from "@/__generated__"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  Di<PERSON>Trigger,
} from "@/components/ui/dialog"
import { Button, buttonVariants } from "@/components/ui/button"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import { parseGraphqlError } from "@/lib/utils/parse-graphql-error"

const ISSUE_REFUND = graphql(`
  mutation IssueRefund($amount: Float!, $orderID: String!) {
    issueRefund(amount: $amount, order_id: $orderID) {
      status
      reason
    }
  }
`)

interface Props {
  amountPaid: number
  orderId: string
}

const IssueRefund = ({ amountPaid, orderId }: Props) => {
  const [open, setOpen] = useState(false)
  const graphql = getGraphqlClient()
  const queryClient = useQueryClient()

  const closeModal = () => {
    setOpen(false)
  }

  const issueRefund = useMutation({
    mutationFn: async () => {
      return await graphql.request(ISSUE_REFUND, {
        amount: amountPaid,
        orderID: orderId,
      })
    },
  })

  const handleRefund = () => {
    issueRefund.mutate(undefined, {
      onSuccess: (data) => {
        toast.success(parseRefundStatus(data?.issueRefund?.status || ""))
        queryClient.invalidateQueries({
          queryKey: ["event-attendees"],
        })
        closeModal()
      },
      onError: (error) => {
        toast.error(parseGraphqlError(error))
      },
    })
  }

  return (
    <>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger className={buttonVariants({ variant: "ghost" })}>
          Refund
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>Refund user?</DialogHeader>
          <DialogFooter>
            <div className="flex gap-x-8">
              <Button
                className="border-red-700 bg-red-700 text-white"
                isLoading={issueRefund.isPending}
                onClick={handleRefund}
              >
                Yes
              </Button>
              <Button onClick={closeModal}>Cancel</Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}

export default IssueRefund
