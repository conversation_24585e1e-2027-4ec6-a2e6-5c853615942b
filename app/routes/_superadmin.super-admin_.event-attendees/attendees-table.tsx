import { useSearchParams } from "react-router";
import { useDebounce } from "use-debounce"

import IssueRefund from "./issue-refund"

import { graphql } from "@/__generated__"
import { ErrorComponent, Rupee } from "@/components/common"
import { IconInfo, IconLoader, IconSearch, IconX } from "@/components/icons"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  NUMBER_OF_ITEMS_TO_FETCH,
  SEARCH_DEBOUNCE_VALUE,
  parsePaymentProvider,
  parseRefundStatusForTable,
  parseUserPaymentStatus,
} from "@/lib/utils"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { useQuery } from "@tanstack/react-query"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import PagePagination from "@/components/common/page-pagination"

const EVENT_ATTENDEES = graphql(`
  query EventAttendees(
    $eventID: ID!
    $first: Int!
    $page: Int
    $username: String
    $mobileNumber: String
    $paymentStatus: String
  ) {
    eventAttendees(
      event_id: $eventID
      first: $first
      page: $page
      username: $username
      mobile_number: $mobileNumber
      payment_status: $paymentStatus
    ) {
      data {
        id
        username
        mobile_number
        amount_paid
        paymentOrder {
          paymentable {
            __typename
            ... on PhonePePaymentOrder {
              id
              order_id
              status
              refund_status
              refund_error_detail
            }
            ... on RzpayPaymentOrder {
              id
              order_id
              status
              refund_status
              refund_error_detail
            }
          }
        }
        payment_status
        number_of_tickets
        ticket_path
      }
      paginatorInfo {
        lastPage
        total
        currentPage
      }
    }
  }
`)

interface PaymentStatus {
  id: string
  name: string
  value: string
}

const PAYMENT_STATUS: PaymentStatus[] = [
  { id: "0", name: "All", value: "all" },
  { id: "1", name: "Paid", value: "paid" },
  { id: "2", name: "Failed", value: "failed" },
  { id: "3", name: "Pending payment", value: "pending_payment" },
  { id: "4", name: "Paid but refunded", value: "paid_but_refunded" },
]

interface Props {
  eventId: string
  token?: string
}

const AttendeesTable = ({ eventId, token }: Props) => {
  const graphql = getGraphqlClient(token)
  const [searchParams, setSearchParams] = useSearchParams()

  const page = parseInt(searchParams.get("page") || "1")
  const searchAttendee = searchParams.get("keyword") || ""
  const paymentStatus = searchParams.get("paymentStatus") || ""

  const [keyword] = useDebounce(searchAttendee, SEARCH_DEBOUNCE_VALUE)

  const { data, isLoading, isError } = useQuery({
    queryKey: ["event-attendees", eventId, page, keyword, paymentStatus],
    queryFn: async () => {
      return await graphql.request(EVENT_ATTENDEES, {
        eventID: eventId,
        first: NUMBER_OF_ITEMS_TO_FETCH,
        page: page,
        paymentStatus: paymentStatus === "all" ? "" : paymentStatus,
        mobileNumber: keyword && /^\d+$/.test(keyword) ? `%${keyword}%` : "",
        username: keyword && /[a-zA-Z]/.test(keyword) ? `%${keyword}%` : "",
      });
    },
  })

  const handleFilterByStatus = (id: string) => {
    const status = PAYMENT_STATUS.find((item) => item.id === id)
    if (status) {
      // searchParams.set("paymentStatus", status.value)
      // navigate({ search: searchParams.toString() }, { replace: true })
      setSearchParams(
        (prev) => {
          prev.delete("page", page.toString())
          prev.set("paymentStatus", status.value)
          return prev
        },
        {
          replace: true,
        }
      )
    }
  }

  const handlePage = (page: number) => {
    setSearchParams((prev) => {
      prev.set("page", page.toString())
      return prev
    })
  }

  const handleSearchAttendee = (value: string) => {
    if (value) {
      setSearchParams((prev) => {
        prev.set("keyword", value)
        prev.delete("page")
        return prev
      })
    } else {
      setSearchParams((prev) => {
        prev.delete("keyword")
        prev.delete("page")
        return prev
      })
    }
  }

  return (
    <>
      <div className="mt-8 grid grid-cols-12 gap-4">
        <div className="col-span-12 lg:col-span-4">
          <Label htmlFor="search">Search attendees</Label>
          <Input
            id="search"
            value={searchAttendee}
            onChange={(e) => handleSearchAttendee(e.target.value)}
            placeholder="Search by name or phone number"
            leftSection={
              isLoading ? (
                <IconLoader className="animate-spin" />
              ) : (
                <IconSearch />
              )
            }
            rightSection={
              searchAttendee && (
                <button
                  onClick={() => handleSearchAttendee("")}
                  className="flex items-center justify-center"
                >
                  <IconX />
                </button>
              )
            }
          />
        </div>
        <div className="col-span-12 lg:col-span-4">
          <Select onValueChange={handleFilterByStatus}>
            <Label>Filter payment status</Label>
            <SelectTrigger>
              <SelectValue
                placeholder={
                  PAYMENT_STATUS.find(
                    (item) => item.value === searchParams.get("paymentStatus")
                  )?.name || "Filter payment"
                }
              />
            </SelectTrigger>
            <SelectContent>
              {PAYMENT_STATUS.map((item) => (
                <SelectItem key={item.id} value={item.id}>
                  {item.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      <div className="mt-8 w-full overflow-auto">
        {isError && <ErrorComponent />}
        {!isError && (
          <table className="min-w-full divide-y divide-gray-200">
            <caption className="text-left">
              Total : {data?.eventAttendees?.paginatorInfo?.total}
            </caption>
            <thead className="bg-gray-50">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                >
                  Index
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                >
                  Name
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                >
                  Mobile Number
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                >
                  Amount
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                >
                  No. of tickets
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                >
                  Payment Provider
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                >
                  Refund Status
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                >
                  User Payment Status
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                >
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white">
              {data?.eventAttendees?.data?.map((item, index) => {
                const userPaymentStatus = parseUserPaymentStatus(
                  item?.payment_status || ""
                )
                const paymentProvider = parsePaymentProvider(
                  item?.paymentOrder?.paymentable?.__typename || ""
                )

                const pageMultiplier =
                  data?.eventAttendees?.paginatorInfo?.currentPage &&
                  data?.eventAttendees?.paginatorInfo?.currentPage > 1
                    ? 10 * (data.eventAttendees.paginatorInfo.currentPage - 1)
                    : 1
                const tableIndex =
                  pageMultiplier === 1
                    ? index + 1 * pageMultiplier
                    : index + 1 + pageMultiplier

                return (
                  item && (
                    <tr key={item.id} className="hover:bg-gray-100">
                      <td className="whitespace-nowrap px-6 py-4">
                        {tableIndex}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4">
                        {item.username}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4">
                        {item.mobile_number}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4">
                        <Rupee /> {item.amount_paid}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-center">
                        {item.number_of_tickets}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4">
                        {paymentProvider}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4">
                        {item.paymentOrder?.paymentable?.__typename !==
                          "SGPGPaymentOrder" && (
                          <span>
                            {parseRefundStatusForTable(
                              item.paymentOrder?.paymentable?.refund_status ||
                                ""
                            )}
                            {item.paymentOrder?.paymentable
                              ?.refund_error_detail &&
                              `| ${item.paymentOrder.paymentable.refund_error_detail}`}
                          </span>
                        )}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4">
                        {item.paymentOrder?.paymentable?.__typename !==
                          "SGPGPaymentOrder" && (
                          <span className="flex items-center gap-x-2">
                            {userPaymentStatus}
                            {item.paymentOrder?.paymentable?.status ? (
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger>
                                    <IconInfo />
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>
                                      {item.paymentOrder?.paymentable.status}
                                    </p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            ) : (
                              ""
                            )}
                          </span>
                        )}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4">
                        {/* //TODO: use a single modal */}
                        {item.paymentOrder?.paymentable?.__typename !==
                          "SGPGPaymentOrder" && (
                          <>
                            {item?.paymentOrder?.paymentable?.order_id ? (
                              <IssueRefund
                                orderId={
                                  item.paymentOrder?.paymentable?.order_id
                                }
                                amountPaid={item.amount_paid}
                              />
                            ) : null}
                          </>
                        )}
                      </td>
                    </tr>
                  )
                )
              })}
            </tbody>
          </table>
        )}

        {data?.eventAttendees?.paginatorInfo &&
        data.eventAttendees.paginatorInfo.lastPage > 1 ? (
          <></>
        ) : null}

        {data?.eventAttendees?.paginatorInfo?.lastPage &&
          data.eventAttendees.paginatorInfo.lastPage > 1 &&
          data?.eventAttendees?.paginatorInfo?.currentPage && (
            <PagePagination
              handlePagePagination={handlePage}
              currentPage={page}
              lastPage={data.eventAttendees.paginatorInfo.lastPage}
            />
          )}
      </div>
    </>
  )
}

export default AttendeesTable
