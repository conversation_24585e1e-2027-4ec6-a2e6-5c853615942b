import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useSearchParams } from "react-router";

const SelectEventType = () => {
  const [searchParams, setSearchParams] = useSearchParams()
  const eventType = searchParams.get("event_type") ?? ""

  const handleSelectedType = (value: string) => {
    setSearchParams((prev) => {
      if (prev.has("event_type")) {
        prev.set("event_type", value)
      } else {
        prev.append("event_type", value)
      }
      return prev
    })
  }

  return (
    <Select value={eventType} onValueChange={handleSelectedType}>
      <SelectTrigger className="w-[180px]">
        <SelectValue placeholder="Select Event Type" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="msl">MSL</SelectItem>
        <SelectItem value="general">General</SelectItem>
      </SelectContent>
    </Select>
  )
}

export default SelectEventType
