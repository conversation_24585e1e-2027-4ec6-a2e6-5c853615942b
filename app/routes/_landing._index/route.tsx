import { redirect, useLoaderData } from "react-router"
import dayjs from "dayjs"

import { EVENTS_QUERY } from "@lib/graphql/queries"
import { TicketLine } from "@components/ui"
import {
  BookingSteps,
  HomeDataList,
  PastEventsList,
  Spotlight,
} from "@features/Home"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import GET_SPOTLIGHT_LIST from "@/lib/graphql/queries/get-spotlight-list"
import { ClientError } from "graphql-request"
import { EventType } from "@/__generated__/graphql"

export const meta = () => {
  return [
    { title: "Triket" },
    {
      name: "description",
      content:
        "Triket is a user-friendly and secure online platform that connects event organizers, venues, and performers with enthusiasts alike",
    },
    {
      property: "og:title",
      content: "Triket",
    },
    {
      property: "og:url",
      content: "https://triket.in",
    },
    {
      property: "og:type",
      content: "website",
    },
    {
      property: "og:image",
      itemProp: "image",
      content: "/logo.png",
    },
    {
      property: "og:description",
      content:
        "Triket is a user-friendly and secure online platform that connects event organizers, venues, and performers with enthusiasts like",
    },
    {
      property: "robots",
      content: "index,follow",
    },
    {
      property: "twitter:card",
      content: "summary_large_image",
    },
    {
      property: "twitter:url",
      content: `https://triket.in`,
    },
    {
      property: "twitter:title",
      content: "Triket",
    },
    {
      property: "twitter:description",
      content:
        "Triket is a user-friendly and secure online platform that connects event organizers, venues, and performers with enthusiasts like",
    },
    {
      property: "og:site_name",
      content: "Triket",
    },
  ]
}

export async function loader() {
  const graphqlClient = getGraphqlClient()

  try {
    const homeData = await graphqlClient.request(EVENTS_QUERY, {
      page: 1,
      currentDatetime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
      event_types: [EventType.General, EventType.VanapaHallSeated],
      first: 10,
    })

    const spotlight = await graphqlClient.request(GET_SPOTLIGHT_LIST)

    return {
      data: homeData.getEvents.data,
      spotlight: spotlight.getSpotlightList,
    }
  } catch (error) {
    console.log("error on loader", error)
    if (error instanceof ClientError && error?.response?.status === 429) {
      return redirect(`/too-many-requests`)
    }
    throw error
  }
}

export default function Index() {
  const { data, spotlight } = useLoaderData<typeof loader>()

  return (
    <div className="flex flex-col">
      {/* using grid to set order in different views */}
      <div>
        <Spotlight spotlight={spotlight} />
      </div>

      <div className="md:hidden">
        {data && data.length > 0 && <HomeDataList data={data} />}
        <PastEventsList type="general" />
      </div>

      <div className="hidden md:block">
        <BookingSteps />
      </div>

      <div className="py-6 md:py-12">
        <TicketLine />
      </div>

      {/* <div className="col-span-12 mx-auto my-8 flex w-full max-w-5xl flex-col gap-4 px-4 md:order-2 md:gap-16 md:px-10"> */}
      {/*   <h2 className="px-8 text-center text-3xl md:text-5xl"> */}
      {/*     MIZORAM SUPER LEAGUE | SEASON X */}
      {/*   </h2> */}
      {/*   <Link */}
      {/*     to="msl" */}
      {/*     className="flex aspect-16/9 size-full items-center bg-primary-blue" */}
      {/*   > */}
      {/*     <img alt="MSL" src="/msl_banner.png" /> */}
      {/*   </Link> */}
      {/* </div> */}

      {/* Home data component  */}
      <div className="hidden md:block">
        {data && data.length > 0 && <HomeDataList data={data} />}
        <PastEventsList type="general" />
      </div>

      <div className="hidden md:hidden">
        <BookingSteps />
      </div>
    </div>
  )
}
