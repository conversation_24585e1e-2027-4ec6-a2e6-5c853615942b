import { type UseFormReturn, useFieldArray } from "react-hook-form"
import { useState } from "react"

import { type FormSchemaType } from "./schema"

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { TICKET_CLASSES } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { IconX } from "@/components/icons"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"

interface Props {
  form: UseFormReturn<FormSchemaType>
}

const TicketTypes = ({ form }: Props) => {
  const isPrivateEvent = form.watch("isPrivateEvent")
  const [selectedClassId, setSelectedClassId] = useState("")

  const { fields, append, remove } = useFieldArray<FormSchemaType>({
    control: form.control,
    name: "eventTicketType",
  })

  const handleTicketClassChange = (id: string) => {
    setSelectedClassId(() => {
      if (id === "1") {
        if (fields.length > 0) {
          for (let i = 0; i < fields.length; i++) {
            remove(0)
          }
        }
        append({
          maximum_capacity: undefined,
          price: undefined,
          ticket_type: "default",
          is_gst_incl: false,
          is_convenience_fee_incl: false,
        })
      } else {
        remove(0)
        for (let i = 0; i < 2; i++) {
          append({
            maximum_capacity: undefined,
            price: undefined,
            ticket_type: "",
            is_gst_incl: false,
            is_convenience_fee_incl: false,
          })
        }
      }
      return id
    })
  }

  const handleAddMoreMultipleTier = () => {
    append({
      maximum_capacity: undefined,
      price: undefined,
      ticket_type: "",
      is_gst_incl: false,
      is_convenience_fee_incl: false,
    })
  }

  if (!isPrivateEvent) {
    return (
      <>
        <FormItem>
          <FormLabel required>Ticket Type</FormLabel>
          <Select onValueChange={handleTicketClassChange}>
            <FormControl>
              <SelectTrigger type="button">
                <SelectValue placeholder="Select Tier" />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              {TICKET_CLASSES.map((item) => (
                <SelectItem key={item.id} value={item.id}>
                  {item.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </FormItem>

        {selectedClassId === "1" &&
          fields.map((_, index) => {
            return (
              <div
                key={index}
                className="relative flex flex-col gap-y-4 border border-input p-4 focus-within:ring-2 focus-within:ring-black"
              >
                <FormField
                  control={form.control}
                  name={`eventTicketType.${index}.price`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Entry fee</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter entry fee"
                          inputMode="numeric"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name={`eventTicketType.${index}.maximum_capacity`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Maximum capacity</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter maximum capacity"
                          inputMode="numeric"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name={`eventTicketType.${index}.is_gst_incl`}
                  render={({ field }) => (
                    <FormItem className="flex space-x-2">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <FormLabel>Inclusive of GST</FormLabel>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name={`eventTicketType.${index}.is_convenience_fee_incl`}
                  render={({ field }) => (
                    <FormItem className="flex space-x-3">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <FormLabel>Inclusive of convenience fee</FormLabel>
                    </FormItem>
                  )}
                />
              </div>
            )
          })}

        {selectedClassId === "2" && (
          <>
            {fields.map((_, index) => {
              return (
                <div
                  key={index}
                  className="relative flex flex-col gap-y-4 border border-input p-4 pt-12 focus-within:ring-2 focus-within:ring-black"
                >
                  <Button
                    variant="ghost"
                    type="button"
                    className="absolute right-0 top-0 text-destructive"
                    onClick={() => remove(index)}
                  >
                    <IconX />
                  </Button>
                  <FormField
                    control={form.control}
                    name={`eventTicketType.${index}.ticket_type`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Ticket type</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter ticket type" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name={`eventTicketType.${index}.price`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Entry fee</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter entry fee"
                            inputMode="numeric"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name={`eventTicketType.${index}.maximum_capacity`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Maximum capacity</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter maximum capacity"
                            inputMode="numeric"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name={`eventTicketType.${index}.is_gst_incl`}
                    render={({ field }) => (
                      <FormItem className="flex space-x-2">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <FormLabel>Inclusive of gst</FormLabel>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name={`eventTicketType.${index}.is_convenience_fee_incl`}
                    render={({ field }) => (
                      <FormItem className="flex space-x-2">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <FormLabel>Inclusive of convenience fee</FormLabel>
                      </FormItem>
                    )}
                  />
                </div>
              )
            })}
            <div>
              <Button
                variant="outline"
                type="button"
                onClick={handleAddMoreMultipleTier}
              >
                Add ticket type
              </Button>
            </div>
          </>
        )}
      </>
    )
  }
}

export default TicketTypes
