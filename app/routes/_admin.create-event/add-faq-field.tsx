import type { Control } from "react-hook-form"
import { useFieldArray } from "react-hook-form"

import type { FormSchemaType } from "./schema"

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { IconX } from "@/components/icons"

interface Props {
  control: Control<FormSchemaType>
}

const AddFaqField = ({ control }: Props) => {
  const { fields, append, remove } = useFieldArray<FormSchemaType>({
    control,
    name: "faq",
  })

  const handleAddFaq = () => {
    append({
      question: "",
      answer: "",
    })
  }

  return (
    <>
      {fields.map((_, index) => {
        return (
          <div
            key={index}
            className="relative flex flex-col gap-y-4 border border-input p-4 pt-12 focus-within:ring-2 focus-within:ring-black"
          >
            <Button
              variant="ghost"
              type="button"
              className="absolute right-0 top-0 text-destructive"
              onClick={() => remove(index)}
            >
              <IconX />
            </Button>
            <FormField
              control={control}
              name={`faq.${index}.question`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Question</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter question" {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name={`faq.${index}.answer`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Answer</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter answer" {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        )
      })}
      <div>
        <Button variant="outline" onClick={handleAddFaq} type="button">
          Add Faq
        </Button>
      </div>
    </>
  )
}

export default AddFaqField
