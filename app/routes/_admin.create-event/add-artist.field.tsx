import {
  type Control,
  type UseFormSetValue,
  useFieldArray,
} from "react-hook-form"
import { type ChangeEvent } from "react"

import { type FormSchemaType } from "./schema"

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { IconX } from "@/components/icons"

interface Props {
  control: Control<FormSchemaType>
  setValue: UseFormSetValue<FormSchemaType>
}

const AddArtistField = ({ control, setValue }: Props) => {
  const { fields, append, remove } = useFieldArray<FormSchemaType>({
    control,
    name: "artists",
  })

  const handleAddArtist = () => {
    // @ts-expect-error avatar expects a file but needs to be initiated with undefined
    append({
      artist_name: "",
      avatar: undefined,
      social_link: "",
    })
  }

  const handleArtistImage = (
    e: ChangeEvent<HTMLInputElement>,
    index: number
  ) => {
    const files = e.target.files
    if (files) {
      setValue(`artists.${index}.avatar`, files[0])
    }
  }

  return (
    <>
      {fields.map((_, index) => {
        return (
          <div
            key={index}
            className="relative flex flex-col gap-y-4 border border-input p-4 pt-12 focus-within:ring-2 focus-within:ring-black"
          >
            <Button
              variant="ghost"
              type="button"
              className="absolute right-0 top-0 text-destructive"
              onClick={() => remove(index)}
            >
              <IconX />
            </Button>
            <FormField
              control={control}
              name={`artists.${index}.artist_name`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Artist name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter artist name" {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              key={index}
              control={control}
              name={`artists.${index}.avatar`}
              render={() => (
                <FormItem>
                  <FormLabel>Artist image</FormLabel>
                  <FormControl>
                    <Input
                      onChange={(e) => handleArtistImage(e, index)}
                      type="file"
                      accept="image/jpg, image/png, image/jpeg"
                      placeholder="Enter artist name"
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              key={index}
              control={control}
              name={`artists.${index}.social_link`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Social link</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter social link (eg: insta, fb, x)"
                      {...field}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        )
      })}
      <div>
        <Button variant="outline" onClick={handleAddArtist} type="button">
          Add Artists
        </Button>
      </div>
    </>
  )
}

export default AddArtistField
