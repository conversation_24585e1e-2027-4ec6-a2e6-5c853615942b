import { EventType } from "@/__generated__/graphql"
import dayjs from "dayjs"
import { z } from "zod"

export const eventTicketSchema = z.object({
  ticket_type: z.string().min(1, "Ticket type"),
  price: z.coerce
    .number({
      required_error: "Price is required",
      invalid_type_error: "Price must be a number",
    })
    .int()
    .positive()
    .min(0, { message: "Price should be at least 1" })
    .optional(),
  maximum_capacity: z.coerce
    .number({
      required_error: "Maximum capacity is required",
      invalid_type_error: "Maximum capacity must be a number",
    })
    .int()
    .positive()
    .min(0, { message: "Maximum capacity should be at least 1" })
    .optional(),
  is_gst_incl: z.boolean(),
  is_convenience_fee_incl: z.boolean(),
})

export const artistSchema = z.object({
  artist_name: z.string().min(1, "Artist name required"),
  // avatar: z.instanceof(File).nullish(),
  avatar: z.instanceof(File),
  social_link: z.string().optional(),
})

export const faqSchema = z.object({
  question: z.string(),
  answer: z.string(),
})

export const schema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string(),
  location: z.string().min(1, "Location required"),
  orgName: z.string().min(1, "Organizer name is required"),
  eventTicketType: z.array(eventTicketSchema),
  eventImages: z.array(z.instanceof(File)),
  startBookingDate: z.string().nullish(),
  address: z.string().min(1, "Address is required"),
  orgContactNumber: z.string().min(1, "Contact number is required"),
  isPrivateEvent: z.boolean(),
  artists: z.array(artistSchema),
  faq: z.array(faqSchema),
  openMicLink: z.string().optional(),
  startDate: z
    .string()
    .refine((v) => v !== "", "Start date is required")
    .transform((v) => dayjs(v).format("YYYY-MM-DD HH:mm:ss")),
  endDate: z
    .string()
    .refine((v) => v !== "", "End date is required")
    .transform((v) => dayjs(v).format("YYYY-MM-DD HH:mm:ss")),
  city: z.string().min(1, "City is required"),
  eventType: z.nativeEnum(EventType),
  // zod doesn't need this type, it'll be handled by the submit function handlers
  // isPublished: z.boolean().nullish(),
})

export type FormSchemaType = z.infer<typeof schema>
