import { zod<PERSON><PERSON><PERSON><PERSON> } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import dayjs from "dayjs"
import { type ChangeEvent } from "react"
import { useNavigate } from "react-router"
import toast from "react-hot-toast"

import { type FormSchemaType, schema } from "./schema"
import Add<PERSON><PERSON><PERSON><PERSON> from "./add-faq-field"
import Add<PERSON>rtist<PERSON><PERSON> from "./add-artist.field"
import TicketTypes from "./ticket-types"

import { BackButton } from "@/components/common"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
// import { EVENT_TYPES } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { CREATE_EVENT_MUTATION } from "@lib/graphql/mutations"
import { CITIES } from "@/lib/utils/constants"
import { EventType } from "@/__generated__/graphql"
import { useMutation, useQueryClient } from "@tanstack/react-query"
// import useGetMe from "@/lib/hooks/use-get-me"
import { parseGraphqlError } from "@/lib/utils/parse-graphql-error"
import { graphqlClient } from "@/lib/hooks/graphql-client"
import { Checkbox } from "@/components/ui/checkbox"

const CreateEvent = () => {
  const navigate = useNavigate()

  const queryClient = useQueryClient()

  const form = useForm<FormSchemaType>({
    resolver: zodResolver(schema),
    defaultValues: {
      isPrivateEvent: false,
      address: "",
      artists: undefined,
      city: "",
      description: "",
      endDate: undefined,
      eventImages: undefined,
      eventTicketType: undefined,
      faq: undefined,
      location: "",
      startDate: undefined,
      name: "",
      orgName: "",
      orgContactNumber: "",
      openMicLink: "",
      startBookingDate: undefined,
      eventType: EventType.General,
    },
  })

  // const { data: user } = useGetMe()

  const createEvent = useMutation({
    mutationFn: async ({
      data,
      isPublished,
    }: {
      data: FormSchemaType
      isPublished: boolean
    }) => {
      const graphql = await graphqlClient()
      const faq = JSON.stringify(data.faq)
      return await graphql.request({
        document: CREATE_EVENT_MUTATION,
        variables: {
          name: data.name,
          description: data.description,
          address: data.address,
          location: data.location,
          orgName: data.orgName,
          orgContactNumber: data.orgContactNumber,
          startDate: data.startDate,
          endDate: data.endDate,
          eventImages: data.eventImages,
          isPrivateEvent: data.isPrivateEvent,
          isPublished: isPublished,
          startBookingDate: data.startBookingDate,
          faq: faq,
          openMicLink: data.openMicLink,
          artists: data.artists,
          // @ts-expect-error maximum_capacity field expects number but don't want to initialize with 0
          eventTicketType: data.eventTicketType,
          city: data.city,
          eventType: data.eventType,
          // hard code eventType for msl user
          // eventType:
          //   user?.getMe?.user?.identifier === "msl"
          //     ? EventType.Msl
          //     : EventType.General,
        },
        // data,
      })
    },
    onError: (error) => {
      const parsedError = parseGraphqlError(error)
      toast.error(parsedError)
    },
  })

  const handleFileInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files) {
      form.setValue("eventImages", Array.from(files))
    }
  }

  const handleCreateEvent = async (data: FormSchemaType) => {
    createEvent.mutate(
      {
        data: data,
        isPublished: true,
      },
      {
        onSuccess: () => {
          form.reset()
          queryClient.invalidateQueries({
            queryKey: ["get-events"],
          })
          toast.success("Event published successfully")
          navigate("/my-events")
        },
      }
    )
  }

  const handleSaveAsDraft = async (data: FormSchemaType) => {
    createEvent.mutate(
      {
        data: data,
        isPublished: false,
      },
      {
        onSuccess: () => {
          form.reset()
          queryClient.invalidateQueries({
            queryKey: ["my-events"],
            // include: [MY_EVENTS_QUERY],
          })
          toast.success("Event published successfully")
          navigate("/my-events")
        },
      }
    )
  }

  const ticketType = form.watch("eventType")

  return (
    <>
      <div className="mx-auto flex max-w-lg flex-col">
        <div className="flex">
          <BackButton />
          <h1 className="w-full text-center font-bold">Create Event</h1>
        </div>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleCreateEvent)}
            className="mb-8 mt-4 flex flex-col gap-y-6"
          >
            <FormField
              control={form.control}
              name="city"
              render={({ field }) => (
                <FormItem>
                  <FormLabel required>Choose city</FormLabel>
                  <Select
                    onValueChange={(city) => {
                      field.onChange(city)
                    }}
                  >
                    <FormControl>
                      <SelectTrigger type="button">
                        <SelectValue placeholder="Choose city" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {CITIES.map((item) => (
                        <SelectItem key={item.id} value={item.value}>
                          {item.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel required>Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter event name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="eventImages"
              render={() => (
                <FormItem>
                  <FormLabel required>
                    Event images (4:5 portrait images recommended)
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="file"
                      placeholder="Upload event images"
                      onChange={handleFileInputChange}
                      multiple
                      accept="image/jpg, image/png, image/jpeg"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Enter description" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel required>Address</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Enter address" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel required>Google map link</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter location (Google map link)"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="openMicLink"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Open mic link</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter open mic form link" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="orgName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel required>Organizer name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter organizer name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="orgContactNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel required>Organizer contact number </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter organizer contact number"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <AddFaqField control={form.control} />

            <AddArtistField control={form.control} setValue={form.setValue} />

            <FormField
              control={form.control}
              name="startDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel required>Start date</FormLabel>
                  <FormControl>
                    <Input className="block" type="datetime-local" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="endDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel required>End date</FormLabel>
                  <FormControl>
                    <Input className="block" type="datetime-local" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Cause it's nullish, it's implementation is different over other dates */}
            <FormField
              control={form.control}
              name="startBookingDate"
              render={() => (
                <FormItem>
                  <FormLabel>Start booking date (Optional)</FormLabel>
                  <FormControl>
                    <Input
                      className="block"
                      type="datetime-local"
                      {...form.register("startBookingDate", {
                        setValueAs: (v) =>
                          v === ""
                            ? undefined
                            : dayjs(v).format("YYYY-MM-DD HH:mm:ss"),
                      })}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="eventType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel required>EventType</FormLabel>
                  <Select value={field.value} onValueChange={field.onChange}>
                    <FormControl>
                      <SelectTrigger type="button">
                        <SelectValue placeholder="Event type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value={EventType.General}>General</SelectItem>
                      <SelectItem value={EventType.Msl}>MSL</SelectItem>
                      <SelectItem value={EventType.VanapaHallSeated}>
                        Vanapa Hall Seated
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />

            {ticketType !== EventType.VanapaHallSeated && (
              <TicketTypes form={form} />
            )}

            <FormField
              control={form.control}
              name="isPrivateEvent"
              render={({ field }) => {
                return (
                  <FormItem className="flex items-center space-x-2">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <FormLabel className="flex items-center">
                      Private Event?
                    </FormLabel>
                  </FormItem>
                )
              }}
            />

            <Button
              onClick={form.handleSubmit(handleSaveAsDraft)}
              variant="outline"
              isLoading={createEvent.isPending}
              type="button"
            >
              Save as Draft
            </Button>
            <Button
              type="submit"
              //     {/* onClick={form.handleSubmit(handleCreateEvent)} */}
              variant="primaryBlue"
              isLoading={createEvent.isPending}
            >
              Publish Event
            </Button>
          </form>
        </Form>
      </div>
    </>
  )
}

export default CreateEvent
