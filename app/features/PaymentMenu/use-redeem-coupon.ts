import { REDEEM_COUPON } from "@/lib/graphql/mutations"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import { useMutation } from "@tanstack/react-query"

interface Props {
  eventId: string
  coupon: string
}

const useRedeemCoupon = () => {
  const graphql = getGraphqlClient()
  const redeemCoupon = useMutation({
    mutationFn: async ({ eventId, coupon }: Props) => {
      return await graphql.request(REDEEM_COUPON, {
        eventId,
        coupon,
      })
    },
  })

  return { redeemCoupon }
}

export default useRedeemCoupon
