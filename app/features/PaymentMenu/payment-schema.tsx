import { z } from "zod"

export const schema = z.object({
  username: z.string().min(1, "Required").max(30, "Name is too long"),
  mobileNumber: z
    .string()
    .min(10, "Mobile number must be 10 digits")
    .max(10, "Mobile number must be 10 digits"),
  numberOfTickets: z.number(),
  eventTicketTypeId: z.string(),
  couponHash: z.string().nullish(),
  checkTerms: z.literal<boolean>(true, {
    errorMap: () => ({
      message: "Please accept the terms and conditions to proceed",
    }),
  }),
  isGift: z.boolean(),
  recipientName: z.string().optional(),
  recipientPhone: z.string().optional(),
  recipientMessage: z.string().optional(),
  tipAmount: z.coerce.number().optional(),
  tipMessage: z.string().optional(),
})

export type FormSchemaType = z.infer<typeof schema>
