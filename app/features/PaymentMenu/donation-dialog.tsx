import { But<PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON>alog<PERSON>ooter,
  <PERSON>alogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import type { UseFormReturn } from "react-hook-form"
import type { FormSchemaType } from "./payment-schema"
import { useState } from "react"

interface Props {
  form: UseFormReturn<FormSchemaType>
  callback: () => void
  cancelCallback: () => void
}

const DonationDialog = ({ form, callback, cancelCallback }: Props) => {
  const [open, setOpen] = useState(false)

  const handleCancel = () => {
    setOpen(false)
    cancelCallback()
    // form.resetField("tipAmount")
    // form.resetField("tipMessage")
  }

  const handleAddTip = () => {
    setOpen(false)
    callback()
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          // variant="outline"
          className="my-4 border-2 border-pallet-green bg-pallet-green text-white hover:bg-pallet-green/80"
        >
          Add a tip &nbsp;
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            fill="#fff"
            viewBox="0 0 256 256"
          >
            <path d="M240,102c0,70-103.79,126.66-108.21,129a8,8,0,0,1-7.58,0C119.79,228.66,16,172,16,102A62.07,62.07,0,0,1,78,40c20.65,0,38.73,8.88,50,23.89C139.27,48.88,157.35,40,178,40A62.07,62.07,0,0,1,240,102Z"></path>
          </svg>
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="mx-auto">
            <img src="/logo.png" alt="triket logo" className="w-24" />
          </DialogTitle>
          <DialogDescription className="flex flex-col gap-y-4 pt-4">
            <span>Tips are never expected but always appreciated!</span>
            <span>
              If you'd like to support the Organizers or the Artist / Athlete,
              feel free to leave a little something extra.
            </span>
          </DialogDescription>
        </DialogHeader>
        <div className="flex flex-col gap-y-4">
          <FormField
            control={form.control}
            name="tipAmount"
            render={({ field }) => {
              return (
                <FormItem>
                  <FormLabel>Amount</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Amount in &#8377;" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )
            }}
          />
          <FormField
            control={form.control}
            name="tipMessage"
            render={({ field }) => {
              return (
                <FormItem>
                  <FormLabel>Message</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="You can write a custom message or instructions for this kind gesture."
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )
            }}
          />

          <DialogFooter className="gap-4">
            <Button type="button" onClick={handleCancel} variant="secondary">
              Cancel
            </Button>
            <Button
              className="bg-pallet-green hover:bg-pallet-green/80"
              type="button"
              onClick={handleAddTip}
            >
              Add tip
            </Button>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default DonationDialog
