import { GET_CONVENIENCE_FEE } from "@/lib/graphql/queries"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import { useMutation } from "@tanstack/react-query"

const useGetConvenienceFee = () => {
  const graphql = getGraphqlClient()

  const getConvenienceFee = useMutation({
    mutationFn: async (id: string) => {
      return graphql.request(GET_CONVENIENCE_FEE, {
        ticketTypeId: id,
      })
    },
  })

  return { getConvenienceFee }
}

export default useGetConvenienceFee
