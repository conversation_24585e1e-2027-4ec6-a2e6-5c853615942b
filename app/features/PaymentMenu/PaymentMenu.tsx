import { useState, useEffect, type ChangeEvent } from "react"
import { useForm } from "react-hook-form"
import toast from "react-hot-toast"
import { zodResolver } from "@hookform/resolvers/zod"
import { RadioGroup } from "@headlessui/react"
import { NavLink } from "react-router"

import GiftDialog from "./gift-dialog"

import { Rupee } from "@components/common"
import { IconRadioCheck, IconX } from "@components/icons"
import { ImageButton } from "@components/ui"
import { baseUrl, formatEventTicketStatus } from "@lib/utils"
import type { EventTicketType } from "@/__generated__/graphql"
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import { Sheet, SheetClose, SheetContent } from "@/components/ui/sheet"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import {
  RadioGroup as RadioGroupAlt,
  RadioGroupItem,
} from "@/components/ui/radio-group"
import { cn } from "@/lib/utils/shadcnUtil"
import usePaymentCalculation from "@/lib/hooks/use-payment-calculation"
import { Checkbox } from "@/components/ui/checkbox"
import { type FormSchemaType, schema } from "./payment-schema"
import DonationDialog from "./donation-dialog"
import { handlePhonePe } from "@/lib/utils/handle-phonepe"
import { handleSGPG } from "@/lib/utils/handle-spgp"
import { handleRazorpay } from "@/lib/utils/handle-razorpay"
import razorpayLoaderStore from "@/lib/store/razorpay-loader-store"
import useGetConvenienceFee from "@/features/PaymentMenu/use-get-convenience-fee"
import useCreatePayment from "@/features/PaymentMenu/use-create-payment"
import { parseGraphqlError } from "@/lib/utils/parse-graphql-error"
import useRedeemCoupon from "@/features/PaymentMenu/use-redeem-coupon"

interface Props {
  data: EventTicketType[]
  eventId: string
  seatingPlanImagePath: string | undefined
  showDiscount: boolean
  isMsl?: boolean
}

// TODO: Refactor this component
const PaymentMenu = ({
  data,
  eventId,
  seatingPlanImagePath,
  showDiscount,
  isMsl,
}: Props) => {
  const [open, setOpen] = useState(false)

  const { setLoading } = razorpayLoaderStore()

  const { getConvenienceFee } = useGetConvenienceFee()
  const { createPayment } = useCreatePayment()
  const { redeemCoupon } = useRedeemCoupon()

  const convenienceFeeData = getConvenienceFee.data
  const couponData = redeemCoupon.data

  const openModal = () => {
    setOpen(true)
  }

  const { pricing, calculatePricing } = usePaymentCalculation()

  const [allowCoupon, setAllowCoupon] = useState(true)
  const [coupon, setCoupon] = useState("")

  const defaultEventTicket = data.find(
    (item) =>
      formatEventTicketStatus(item.status) !== "Sold out" &&
      item.ticket_type !== "special_invitee"
  )

  const [selectedEvent, setSelectedEvent] = useState<
    EventTicketType | undefined
  >(undefined)

  const form = useForm<FormSchemaType>({
    resolver: zodResolver(schema),
    defaultValues: {
      eventTicketTypeId: defaultEventTicket?.id || "",
      numberOfTickets: 1,
      username: "",
      mobileNumber: "",
      recipientName: "",
      recipientPhone: "",
      recipientMessage: "",
      isGift: false,
      tipAmount: "" as unknown as number,
      tipMessage: "",
    },
  })

  // const [redeemCoupon, { loading: redeemingCoupon, data: couponData }] =
  //   useMutation(REDEEM_COUPON)

  const handleCreatePayment = async (data: FormSchemaType) => {
    createPayment.mutate(data, {
      onSuccess: (data) => {
        const providerType = data?.createPayment?.provider_type
        switch (providerType) {
          case "razorpay":
            setOpen(false)
            if (data.createPayment)
              handleRazorpay({
                amount: pricing.totalAmountPayable,
                orderId: data.createPayment.order_id,
                mobileNumber: data.createPayment.mobile_number,
                username: data.createPayment.username,
                handlerCallback: () => {
                  form.reset()
                },
                setLoading: setLoading,
              })
            break
          case "phonepe":
            if (data.createPayment)
              handlePhonePe(data.createPayment.goto_url ?? "")
            break
          case "sgpg":
            if (data.createPayment)
              handleSGPG(data.createPayment.goto_url ?? "")
        }
      },
      onError: (error) => {
        toast.error(parseGraphqlError(error))
      },
    })
  }

  const handleNumberOfTickets = (value: number) => {
    const numberOfTickets = value
    const convenienceAmount = selectedEvent?.is_convenience_fee_incl
      ? 0
      : convenienceFeeData?.getConvenienceFee?.amount || 0
    const discountPercent = couponData?.redeemCoupon?.amount || 0
    const ticketPrice = pricing.ticketPrice
    const calculateGst = selectedEvent?.is_gst_incl ? false : true

    calculatePricing({
      convenienceAmount: convenienceAmount,
      selectedNumberOfTickets: numberOfTickets,
      ticketPrice: ticketPrice,
      calculateGst: calculateGst,
      discountPercent: discountPercent,
      tipAmount: pricing.tipAmount,
    })

    form.setValue("numberOfTickets", value)
  }

  const handleTicketType = async (value: EventTicketType) => {
    getConvenienceFee.mutate(value.id, {
      onSuccess: (data) => {
        const numberOfTickets = form.getValues("numberOfTickets")
        const convenienceAmount = value?.is_convenience_fee_incl
          ? 0
          : data?.getConvenienceFee?.amount || 0
        const discountPercent = couponData?.redeemCoupon?.amount || 0
        const ticketPrice = parseFloat(value.price)
        // const calculateGst = GST_EVENTS.includes(eventId)
        const calculateGst = value?.is_gst_incl ? false : true

        calculatePricing({
          convenienceAmount: convenienceAmount,
          selectedNumberOfTickets: numberOfTickets,
          ticketPrice: ticketPrice,
          calculateGst: calculateGst,
          discountPercent: discountPercent,
          tipAmount: pricing.tipAmount,
        })
      },
    })
    setSelectedEvent(value)
    form.setValue("eventTicketTypeId", value.id)
  }

  const handleRedeemCoupon = async () => {
    if (coupon) {
      if (allowCoupon) {
        redeemCoupon.mutate(
          {
            eventId: eventId,
            coupon: coupon,
          },
          {
            onSuccess: (data) => {
              toast.success("Coupon code applied successfully")
              const discountPercent = data.redeemCoupon?.amount || 0
              const numberOfTickets = form.getValues("numberOfTickets")
              // const calculateGst = GST_EVENTS.includes(eventId)
              const ticketPrice = pricing.ticketPrice
              const convenienceAmount = selectedEvent?.is_convenience_fee_incl
                ? 0
                : convenienceFeeData?.getConvenienceFee?.amount || 0
              const calculateGst = selectedEvent?.is_gst_incl ? false : true

              calculatePricing({
                // convenienceAmount: pricing.convenienceFee,
                convenienceAmount: convenienceAmount,
                selectedNumberOfTickets: numberOfTickets,
                ticketPrice: ticketPrice,
                calculateGst: calculateGst,
                discountPercent: discountPercent,
                tipAmount: pricing.tipAmount,
              })
              form.setValue("couponHash", data.redeemCoupon?.rc)
              setAllowCoupon(false)
            },
            onError: (error) => {
              toast.error(parseGraphqlError(error))
            },
          }
        )
      } else {
        toast.error("You have already used this feature.")
      }
    }
  }

  const disableButton = (): boolean => {
    const ticketNumberSelected = form.getValues("numberOfTickets")
      ? true
      : false
    const convenienceFeeAvailable = convenienceFeeData?.getConvenienceFee
      ?.amount
      ? true
      : false
    const ticketTypeSelected = form.getValues("eventTicketTypeId")
      ? true
      : false

    return ticketNumberSelected && convenienceFeeAvailable && ticketTypeSelected
  }

  const handleCouponCode = (e: ChangeEvent<HTMLInputElement>) => {
    if (!allowCoupon) {
      setAllowCoupon(true)
    }
    setCoupon(e.target.value)
  }

  const handleAddTip = () => {
    const ticketPrice = pricing.ticketPrice
    const convenienceAmount = selectedEvent?.is_convenience_fee_incl
      ? 0
      : convenienceFeeData?.getConvenienceFee?.amount || 0

    const numberOfTickets = form.getValues("numberOfTickets")
    const discountPercent = couponData?.redeemCoupon?.amount || 0
    const calculateGst = selectedEvent?.is_gst_incl ? false : true

    const tipAmount = form.getValues("tipAmount")

    if (tipAmount) {
      const parsedAmount = parseInt(tipAmount.toString(), 10)
      calculatePricing({
        convenienceAmount,
        selectedNumberOfTickets: numberOfTickets,
        ticketPrice,
        calculateGst,
        discountPercent,
        tipAmount: parsedAmount,
      })
    }
  }

  const handleCancelTip = () => {
    const ticketPrice = pricing.ticketPrice
    const convenienceAmount = selectedEvent?.is_convenience_fee_incl
      ? 0
      : convenienceFeeData?.getConvenienceFee?.amount || 0

    const numberOfTickets = form.getValues("numberOfTickets")
    const discountPercent = couponData?.redeemCoupon?.amount || 0
    const calculateGst = selectedEvent?.is_gst_incl ? false : true

    calculatePricing({
      convenienceAmount,
      selectedNumberOfTickets: numberOfTickets,
      ticketPrice,
      calculateGst,
      discountPercent,
      tipAmount: 0,
    })

    form.setValue("tipAmount", undefined)
    form.setValue("tipMessage", undefined)
  }

  const handleRemoveCoupon = () => {
    const ticketPrice = pricing.ticketPrice
    const convenienceAmount = selectedEvent?.is_convenience_fee_incl
      ? 0
      : convenienceFeeData?.getConvenienceFee?.amount || 0
    const numberOfTickets = form.getValues("numberOfTickets")
    const discountPercent = 0
    // const calculateGst = GST_EVENTS.includes(eventId)
    const calculateGst = selectedEvent?.is_gst_incl ? false : true

    calculatePricing({
      ticketPrice: ticketPrice,
      convenienceAmount: convenienceAmount,
      selectedNumberOfTickets: numberOfTickets,
      calculateGst: calculateGst,
      discountPercent: discountPercent,
      tipAmount: pricing.tipAmount,
    })

    form.unregister("couponHash")
    setCoupon("")
    setAllowCoupon(true)
  }

  // reset data when menu is toggled
  useEffect(() => {
    if (!open) {
      form.reset()
      setAllowCoupon(true)
      setCoupon("")
    }
  }, [open, form])

  useEffect(() => {
    if (open && defaultEventTicket) {
      handleTicketType(defaultEventTicket)
      setSelectedEvent(defaultEventTicket)
    }
  }, [defaultEventTicket, open])

  return (
    <>
      <div className="flex w-full justify-center border-primary-blue">
        <ImageButton
          onClick={openModal}
          type="button"
          loaderVariant="smallWhite"
          imageSrc="/buy-btn.png"
        >
          Buy Ticket
        </ImageButton>
      </div>
      <Sheet open={open} onOpenChange={setOpen}>
        <SheetContent
          side="bottom"
          className="mx-auto mb-1 h-full max-w-lg overflow-y-scroll px-0 py-8 scrollbar-thin scrollbar-track-slate-300 scrollbar-thumb-slate-700 scrollbar-track-rounded-full scrollbar-thumb-rounded-full"
        >
          <img
            src="/logo.png"
            alt="logo"
            className="mx-auto mt-4 flex w-16 justify-center"
          />
          <SheetClose
            className="absolute right-4 top-4"
            onClick={() => setOpen(false)}
          >
            <IconX />
          </SheetClose>

          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(handleCreatePayment)}
              className="flex flex-col gap-y-4 px-4 pt-8"
            >
              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel required>Name</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Name" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="mobileNumber"
                render={({ field }) => (
                  <FormItem className="relative">
                    <FormLabel required>WhatsApp number</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        inputMode="tel"
                        className="number-input"
                        maxLength={10}
                        placeholder={"WhatsApp number"}
                      />
                    </FormControl>
                    <FormMessage />
                    <div className="absolute right-2 top-9 text-sm text-gray-400">
                      {field.value.length}/10
                    </div>
                  </FormItem>
                )}
              />
              {seatingPlanImagePath && (
                <Accordion
                  collapsible
                  type="single"
                  className="w-full"
                  defaultValue="seating-plans"
                >
                  <AccordionItem value="seating-plans" className="border-b-0">
                    <AccordionTrigger>Seating Plans</AccordionTrigger>
                    <AccordionContent>
                      <img
                        alt="seating-plan"
                        src={`${baseUrl}/image/medium/${seatingPlanImagePath}`}
                      />
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              )}

              <RadioGroup
                // INFO: why can't this be set in defaultValues of useForm
                defaultValue={defaultEventTicket}
                onChange={handleTicketType}
              >
                <RadioGroup.Label>
                  <FormLabel required>Choose ticket type</FormLabel>
                </RadioGroup.Label>
                <div className="grid grid-cols-12 gap-4">
                  {data
                    ?.filter((item) => item.ticket_type !== "special_invitee")
                    .map((item) => {
                      let disableSoldout = false
                      let formattedTicketStatus = undefined
                      if (formatEventTicketStatus(item.status) === "Sold out") {
                        disableSoldout = true
                      }

                      if (
                        formatEventTicketStatus(item.status) !== "available"
                      ) {
                        formattedTicketStatus = `(${formatEventTicketStatus(
                          item.status
                        )})`
                      } else {
                        formattedTicketStatus = ""
                      }

                      if (disableSoldout) {
                        formattedTicketStatus = "(Sold out)"
                      }

                      return (
                        <RadioGroup.Option
                          disabled={disableSoldout}
                          value={item}
                          className={({ active, checked, disabled }) =>
                            cn(
                              "relative col-span-6 flex cursor-pointer rounded-lg border px-5 py-4 shadow-md focus:outline-none",
                              {
                                "ring-2 ring-white ring-opacity-60 ring-offset-2 ring-offset-sky-300":
                                  active,
                                "bg-primary-blue text-white": checked,
                                "bg-white": !checked,
                                "cursor-not-allowed bg-gray-400 text-white shadow-none":
                                  disabled,
                              }
                            )
                          }
                          key={item?.id}
                        >
                          {({ checked }) => (
                            <>
                              <div className="flex w-full items-center justify-between">
                                {disableSoldout && (
                                  <img
                                    src="/sold_out.png"
                                    className="absolute inset-0 size-full object-contain"
                                    alt="Sold out"
                                  />
                                )}
                                <div className="flex items-center">
                                  <div className="text-sm">
                                    <RadioGroup.Label
                                      as="p"
                                      className={`whitespace-pre-line font-medium  ${
                                        checked ? "text-white" : ""
                                      }`}
                                    >
                                      {item?.ticket_type === "default"
                                        ? `General\n${formatEventTicketStatus(item.status) === "Sold out" ? "Sold out" : ""}`
                                        : `${item.ticket_type}\n${formattedTicketStatus}`}
                                    </RadioGroup.Label>
                                    <RadioGroup.Description
                                      as="span"
                                      className={`inline ${
                                        checked ? "text-sky-100" : ""
                                      }`}
                                    >
                                      <span>
                                        <Rupee /> {item?.price}
                                      </span>{" "}
                                    </RadioGroup.Description>
                                  </div>
                                </div>
                                {checked && (
                                  <div className="shrink-0 text-primary-blue">
                                    <IconRadioCheck className="size-6" />
                                  </div>
                                )}
                              </div>
                            </>
                          )}
                        </RadioGroup.Option>
                      )
                    })}
                </div>
                {form.formState.errors?.eventTicketTypeId?.message ? (
                  <p role="alert" className="h-6 text-red-500">
                    {form.formState.errors.eventTicketTypeId.message}
                  </p>
                ) : (
                  <p className=""> </p>
                )}
              </RadioGroup>

              <FormField
                control={form.control}
                name="numberOfTickets"
                render={() => (
                  <FormItem>
                    <FormLabel required>Number of Tickets</FormLabel>
                    <RadioGroupAlt
                      className="grid grid-cols-5 gap-8 py-2"
                      // if defaultEventTicket doesn't exist, don't have default value
                      defaultValue={defaultEventTicket ? "1" : ""}
                      onValueChange={(e) => {
                        handleNumberOfTickets(parseInt(e))
                      }}
                    >
                      {Array.from({ length: 10 }, (_, index) => (
                        <RadioGroupItem
                          className={cn(
                            "col-span-1 rounded-lg shadow-md disabled:bg-gray-400"
                          )}
                          key={index}
                          value={(index + 1).toString()}
                          // if defaultEventTicket doesn't exist, disable all buttons
                          disabled={!defaultEventTicket || (isMsl && index > 0)}
                        >
                          {index + 1}
                        </RadioGroupItem>
                      ))}
                    </RadioGroupAlt>
                  </FormItem>
                )}
              />

              {/* <FormField control={form.control} name="couponHash" /> */}
              {showDiscount && (
                <div className="mb-6 flex items-end justify-between gap-x-4">
                  <div>
                    <Label htmlFor="coupon-code">Coupon code</Label>
                    <Input
                      id="coupon-code"
                      placeholder="Enter coupon code"
                      value={coupon}
                      onChange={handleCouponCode}
                      disabled={!allowCoupon}
                    />
                  </div>
                  {allowCoupon ? (
                    <Button
                      isLoading={redeemCoupon.isPending}
                      onClick={handleRedeemCoupon}
                      variant="outline"
                      className="text-primary-blue shadow-md disabled:bg-gray-500 disabled:text-white"
                      type="button"
                      disabled={!disableButton() || !allowCoupon}
                    >
                      Apply
                    </Button>
                  ) : (
                    <Button
                      type="button"
                      isLoading={redeemCoupon.isPending}
                      onClick={handleRemoveCoupon}
                      variant="destructive"
                      className="shadow-md"
                    >
                      Remove
                    </Button>
                  )}
                </div>
              )}

              <DonationDialog
                form={form}
                callback={handleAddTip}
                cancelCallback={handleCancelTip}
              />

              <div className="flex flex-col">
                <p className="flex justify-between">
                  <span>Total amount: </span>
                  <span>
                    {isNaN(pricing.totalAmount) ? (
                      <>
                        <Rupee /> 0.00
                      </>
                    ) : (
                      <>
                        <Rupee /> {pricing.totalAmount.toFixed(2)}
                      </>
                    )}
                  </span>
                </p>
                {pricing.discountAmount > 0 && (
                  <p className="flex justify-between text-gray-400">
                    <span className="text-green-700">Discount:</span>
                    <span>
                      - <Rupee /> {pricing.discountAmount.toFixed(2)}
                    </span>
                  </p>
                )}
                {pricing.gstAmount > 0 && (
                  <p className="flex justify-between text-gray-400">
                    <span>GST 18%:</span>
                    <span>{pricing.gstAmount.toFixed(2)}</span>
                  </p>
                )}
                {!selectedEvent?.is_convenience_fee_incl && (
                  <p className="flex justify-between text-gray-400">
                    <span>Processing fee: </span>
                    <span>
                      {isNaN(pricing.convenienceFee) ? (
                        <>
                          <Rupee /> 0.00
                        </>
                      ) : (
                        <>
                          <Rupee /> {pricing.convenienceFee.toFixed(2)}
                        </>
                      )}
                    </span>
                  </p>
                )}
                {pricing.tipAmount > 0 && (
                  <p className="flex justify-between text-pallet-green">
                    <span>Tip:</span>
                    <span>
                      <Rupee /> {pricing.tipAmount.toFixed(2)}
                    </span>
                  </p>
                )}
                <p className="mt-2 flex justify-between border-t border-gray-700 pt-2">
                  <span>Total amount payable: </span>
                  <span>
                    {isNaN(pricing.totalAmountPayable) ? (
                      <>
                        <Rupee /> 0.00
                      </>
                    ) : (
                      <>
                        <Rupee /> {pricing.totalAmountPayable.toFixed(2)}
                      </>
                    )}
                  </span>
                </p>
              </div>
              <div className="mt-4">
                Ticket once bought cannot be refunded. In case of payment issues
                contact our customer care. For more information see
                <NavLink
                  target="_blank"
                  to="/terms-and-conditions"
                  className="underline"
                >
                  {" "}
                  Terms & Conditions
                </NavLink>
                <FormField
                  control={form.control}
                  name="checkTerms"
                  render={({ field }) => (
                    <FormItem className="mt-4 flex flex-col">
                      <div className="flex space-x-2">
                        <FormLabel>
                          <span className="text-destructive">* </span>I agree to
                          the terms of use and privacy policy
                        </FormLabel>
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </div>
                      <FormMessage className="text-foreground" />
                    </FormItem>
                  )}
                />
              </div>
              <GiftDialog form={form} />
              <Button
                className="mt-6 w-full shadow-md"
                variant="palletYellow"
                type="submit"
                isLoading={
                  createPayment.isPending || getConvenienceFee.isPending
                }
                disabled={!disableButton() || createPayment.isPending}
              >
                Buy Ticket
              </Button>
            </form>
          </Form>
        </SheetContent>
      </Sheet>
    </>
  )
}

export default PaymentMenu
