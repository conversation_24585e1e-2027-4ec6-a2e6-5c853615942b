import type { FormSchemaType } from "@/features/PaymentMenu/payment-schema"
import { CREATE_PAYMENT_MUTATION } from "@/lib/graphql/mutations"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import { useMutation } from "@tanstack/react-query"

const useCreatePayment = () => {
  const graphql = getGraphqlClient()
  const createPayment = useMutation({
    mutationFn: async (data: FormSchemaType) => {
      return await graphql.request(CREATE_PAYMENT_MUTATION, {
        ...data,
      })
    },
  })

  return { createPayment }
}

export default useCreatePayment
