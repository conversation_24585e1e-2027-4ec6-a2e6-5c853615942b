import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON>alogD<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import type { UseFormReturn } from "react-hook-form"
import type { FormSchemaType } from "./payment-schema"
import { GiftIcon, X } from "lucide-react"
import { Checkbox } from "@/components/ui/checkbox"
import { type MouseEvent, useState } from "react"
import { Button } from "@/components/ui/button"

interface Props {
  open?: boolean
  onOpenChange?: () => void
  form: UseFormReturn<FormSchemaType>
}

const GiftDialog = ({ form }: Props) => {
  const [open, setOpen] = useState(false)

  const [checked, setChecked] = useState(false)

  const message = form.watch("recipientMessage")
  const name = form.watch("username")
  const recipientName = form.watch("recipientName")
  const recipientPhone = form.watch("recipientPhone")
  const recipientMessage = form.watch("recipientMessage")
  const isGift = form.watch("isGift")

  const handleGift = () => {
    const name = form.getValues("recipientName")
    const number = form.getValues("recipientPhone")
    const message = form.getValues("recipientMessage")

    if (!name) {
      form.setError("recipientName", {
        message: "Recipient's name is required",
      })
    }

    if (!number) {
      form.setError("recipientPhone", {
        message: "Recipient's whatsapp number is required",
      })
    }

    if (message && message.length > 100) {
      form.setError("recipientMessage", {
        message: "Message too long",
      })
    }

    if (number && number.length !== 10) {
      form.setError("recipientPhone", {
        message: "Recipient's whatsapp number must be 10 digits",
      })
    }

    if (
      name &&
      number &&
      number.length === 10 &&
      (message?.length ?? 0) <= 100
    ) {
      // set gift as true and checkbox if name and number
      form.setValue("isGift", true)
      setChecked(true)
      handleDialogChange(false)
    }
  }

  const handleDialogChange = (open: boolean) => {
    setOpen(open)
  }

  // const handleCancel = () => {
  //   setOpen(false)
  //   // setChecked(false)
  //   // form.setValue("isGift", false)
  //   // form.resetField("recipientName")
  //   // form.resetField("recipientPhone")
  //   // form.resetField("recipientMessage")
  // }

  const handleCancelGift = (e: MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation()
    setChecked(false)
    form.setValue("isGift", false)
    form.resetField("recipientName")
    form.resetField("recipientPhone")
    form.resetField("recipientMessage")
  }

  return (
    <>
      <FormField
        control={form.control}
        name="isGift"
        render={({ field }) => {
          return (
            <FormItem className="flex items-center space-x-2">
              <FormLabel className="flex items-center">
                <GiftIcon className="size-4" />
                Purchase as gift
              </FormLabel>
              <FormControl>
                <Checkbox
                  checked={checked}
                  onCheckedChange={(checked) => {
                    if (checked === true) {
                      setOpen(true)
                    } else {
                      // if gift is cancelled, remove recipient's info
                      field.onChange(checked)
                      setChecked(false)
                      form.resetField("recipientName")
                      form.resetField("recipientPhone")
                      form.resetField("recipientMessage")
                    }
                  }}
                />
              </FormControl>
            </FormItem>
          )
        }}
      />

      <Dialog open={open} onOpenChange={handleDialogChange}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Purchase ticket as gift</DialogTitle>
            <DialogDescription>{"Enter recipient's info"}</DialogDescription>
          </DialogHeader>
          <FormField
            control={form.control}
            name="recipientName"
            render={({ field }) => (
              <FormItem>
                <FormLabel required>{"Recipient's name"}</FormLabel>
                <FormControl>
                  <Input
                    required
                    {...field}
                    placeholder={"Enter Recipient's name"}
                    inputMode="tel"
                    className="number-input"
                    maxLength={10}

                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="recipientPhone"
            render={({ field }) => (
              <FormItem>
                <FormLabel required>{"Recipient's whatsapp number"}</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    inputMode="numeric"
                    className="number-input"
                    placeholder={"Enter Recipient's whatsapp number"}
                    required
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="recipientMessage"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Message (optional) </FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="Message (eg: Happy Birthday)"
                  />
                </FormControl>
                <div className="flex justify-between">
                  <FormMessage className="w-full" />
                  <span className="flex w-full justify-end text-gray-400">
                    {message?.length ?? 0}/100
                  </span>
                </div>
              </FormItem>
            )}
          />
          <DialogFooter className="gap-4">
            <Button
              onClick={() => handleDialogChange(false)}
              variant="secondary"
            >
              Cancel
            </Button>
            <Button onClick={handleGift} variant="palletYellow">
              Confirm
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      {isGift && (
        <button
          onClick={() => {
            handleDialogChange(true)
          }}
          className="relative flex flex-col rounded-lg border border-black p-2 px-4"
        >
          <button
            type="button"
            onClick={handleCancelGift}
            className="absolute right-2 top-2"
          >
            <X />
          </button>
          <div>Gift to,</div>
          <div>{recipientName ?? "Recipient's name"}</div>
          <div>{recipientPhone ?? "Recipient's phone number"}</div>
          {recipientMessage && (
            <>
              <div>{recipientMessage}</div>
            </>
          )}
          {name && (
            <>
              <div className="pt-4" />
              <div>From,</div>
              <div>{name}</div>
            </>
          )}
        </button>
      )}
    </>
  )
}

export default GiftDialog
