import { useState } from "react"
import dayjs from "dayjs"
import { useDebounce } from "use-debounce"

import { ErrorComponent } from "@components/common"
import { IconDown } from "@components/icons"
import { EVENTS_FOR_SUPER_ADMIN } from "@lib/graphql/queries"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Button } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import { SEARCH_DEBOUNCE_VALUE } from "@/lib/utils"
import { useQuery } from "@tanstack/react-query"
import getGraphqlClient from "@/lib/hooks/get-graphql-client"
import { useSearchParams } from "react-router";

interface Props {
  selectedEvent: {
    id: string
    name: string
  }
  handleSelectedEvent: ({ id, name }: { id: string; name: string }) => void
  token?: string
}

const SelectEvent = ({ selectedEvent, handleSelectedEvent, token }: Props) => {
  const graphql = getGraphqlClient(token)
  const [searchParams] = useSearchParams()
  const [searchEvents, setSearchEvents] = useState("")
  const [searchString] = useDebounce(searchEvents, SEARCH_DEBOUNCE_VALUE)

  const eventType = searchParams.get("event_type") ?? ""

  const [open, setOpen] = useState(false)

  const { data, isLoading, isError } = useQuery({
    queryKey: ["events-for-super-admin", searchString, eventType],
    queryFn: async () => {
      return await graphql.request(EVENTS_FOR_SUPER_ADMIN, {
        page: 1,
        currentDatetime: dayjs("2020-01-01").format("YYYY-MM-DD HH:mm:ss"),
        first: 20,
        type: eventType,
        name: searchString ? `%${searchString}%` : undefined,
      })
    },
    enabled: !!eventType,
  })

  if (isError) {
    return <ErrorComponent />
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline">
          {selectedEvent.name || "Select Event"}
          <IconDown className="ml-2 size-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full">
        <Command>
          <CommandInput
            isLoading={isLoading}
            value={searchEvents}
            onValueChange={(e) => setSearchEvents(e)}
          />
          <CommandEmpty>Event not found</CommandEmpty>
          <CommandList>
            <CommandGroup>
              {data?.events?.data &&
                data.events.data.length > 0 &&
                data.events.data.map((item) => {
                  return (
                    <CommandItem
                      key={item.id}
                      value={item.name}
                      onSelect={(e) => {
                        handleSelectedEvent({ id: item.id, name: e })
                        setOpen(false) // close comobobox
                      }}
                    >
                      {item.name}
                    </CommandItem>
                  )
                })}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}

export default SelectEvent
