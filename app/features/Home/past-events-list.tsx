import dayjs from "dayjs"
import { NavLink } from "react-router";

import { baseUrl } from "@lib/utils"
import { Blurimage } from "@/components/common"
import { Button } from "@/components/ui/button"
import useGetPastEvents from "@/lib/hooks/use-get-past-events"
import { Skeleton } from "@/components/ui/skeleton"
import type { EventTypes } from "@/lib/types/event-types"

interface Props {
  type: EventTypes
}

const PastEventsList = ({ type }: Props) => {
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isError,
    isFetchingNextPage,
    isLoading,
  } = useGetPastEvents({ type })

  const allEvents = data?.pages.flatMap((page) => page.getPastEvents.data) ?? []

  return (
    allEvents.length > 0 && (
      <section className="mx-auto my-8 flex max-w-5xl flex-col gap-4 px-4 md:my-8 md:gap-16 md:px-10">
        <h2 className="px-8 text-center text-3xl md:text-5xl">
          {type === "msl" ? "PAST MATCHES" : "PAST EVENTS"}
        </h2>
        <div className="relative grid grid-cols-2 gap-4 md:gap-16">
          {isLoading && (
            <>
              <div className="col-span-1 aspect-3/4">
                <Skeleton className="h-full" />
              </div>
              <div className="col-span-1 aspect-3/4">
                <Skeleton className="h-full" />
              </div>
            </>
          )}
          {!isError &&
            !isLoading &&
            allEvents.map((item) => (
              <div key={item.id} className="col-span-1">
                <NavLink to={`/event/${item.slug}`}>
                  <div className="mx-auto flex w-full flex-col">
                    <div className="flex cursor-pointer justify-center">
                      {item.images && item.images[0]?.path ? (
                        <div className="relative aspect-[4/5] w-full text-clip">
                          <Blurimage
                            objectFit="cover"
                            src={`${baseUrl}/image/medium/${item.images[0].path}`}
                            alt={`${item.name}`}
                            hash={item.images[0].hash || ""}
                          />
                        </div>
                      ) : null}
                    </div>
                    <h2 className="mt-1 text-left text-base font-semibold tracking-tight md:text-3xl">
                      {item.name}
                    </h2>
                    <p className="text-xs text-gray-500">
                      Event ended on{" "}
                      {dayjs(item?.end_date).format("Do MMM, YYYY")}
                    </p>
                  </div>
                </NavLink>
              </div>
            ))}
          {hasNextPage && (
            <div className="col-span-2 flex justify-center">
              <Button
                onClick={() => fetchNextPage()}
                isLoading={isFetchingNextPage}
                variant="link"
              >
                Load more
              </Button>
            </div>
          )}
        </div>
      </section>
    )
  )
}

export default PastEventsList
