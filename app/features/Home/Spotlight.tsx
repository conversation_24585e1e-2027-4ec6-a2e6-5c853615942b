import { NavLink } from "react-router"
import useEmblaCarousel, {
  type UseEmblaCarouselType,
} from "embla-carousel-react"
import { useCallback, useEffect, useState } from "react"

import type { GetSpotlightListQuery } from "@/__generated__/graphql"

type CarouselApi = UseEmblaCarouselType[1]

import { CarouselDots } from "@components/common/Carousel"
import { baseUrl } from "@/lib/utils"
import { Blurimage } from "@/components/common"

interface Props {
  spotlight: GetSpotlightListQuery["getSpotlightList"]
}

const Spotlight = ({ spotlight }: Props) => {
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true })
  const [scrollSnaps, setScrollSnaps] = useState<number[]>([])

  const scrollPrev = useCallback(
    () => emblaApi && emblaApi.scrollPrev(),
    [emblaApi]
  )
  const scrollNext = useCallback(
    () => emblaApi && emblaApi.scrollNext(),
    [emblaApi]
  )

  const scrollTo = useCallback(
    (index: number) => emblaApi && emblaApi.scrollTo(index),
    [emblaApi]
  )

  const onSelect = useCallback((emblaApi: CarouselApi) => {
    emblaApi && setSelectedIndex(emblaApi.selectedScrollSnap())
  }, [])

  const onInit = useCallback((emblaApi: CarouselApi) => {
    emblaApi && setScrollSnaps(emblaApi.scrollSnapList())
  }, [])

  useEffect(() => {
    if (!emblaApi) return

    onInit(emblaApi)
    emblaApi.on("reInit", onInit)
    emblaApi.on("reInit", onSelect)
    emblaApi.on("select", onSelect)
  }, [emblaApi, onInit, onSelect])

  return (
    <>
      <section className="mx-auto grid w-full max-w-7xl grid-cols-12">
        <div className="relative hidden md:col-span-2 md:block">
          <img
            src="/tl.png"
            alt="top left ticket line"
            className="absolute left-1/4 top-0 z-10"
          />
          <img
            src="/cl2.png"
            alt="center left ticket line"
            className="absolute -right-12 top-[15%]"
          />
          <img
            src="/bl.png"
            alt="bottom left ticket line"
            className="absolute -bottom-4 -right-10"
          />
        </div>

        <div
          className="relative z-20 col-span-12 aspect-16/9 overflow-hidden md:col-span-8"
          ref={emblaRef}
        >
          <div className="flex">
            {spotlight?.map((data, index) => {
              return (
                data && (
                  <div className="size-full shrink-0 grow-0" key={index}>
                    {data.url && !data.is_external ? (
                      <NavLink to={data.url}>
                        <Blurimage
                          variant="carousel"
                          objectFit="cover"
                          alt="spotlight"
                          src={`${baseUrl}/image/medium/${data.image.path}`}
                          hash={data.image.hash || ""}
                        />
                      </NavLink>
                    ) : data.url && data.is_external ? (
                      <a
                        href={data.url}
                        target="_blank"
                        rel="noreferrer noopener"
                      >
                        <Blurimage
                          variant="carousel"
                          objectFit="cover"
                          alt="spotlight"
                          src={`${baseUrl}/image/medium/${data.image.path}`}
                          hash={data.image.hash || ""}
                        />
                      </a>
                    ) : (
                      !data.url && (
                        <>
                          <Blurimage
                            variant="carousel"
                            objectFit="cover"
                            alt="spotlight"
                            src={`${baseUrl}/image/medium/${data.image.path}`}
                            hash={data.image.hash || ""}
                          />
                        </>
                      )
                    )}
                  </div>
                )
              )
            })}
          </div>
        </div>

        <div className="relative hidden md:col-span-2 md:block">
          <img
            src="/tr.png"
            alt="topleft"
            className="absolute -left-12 top-8"
          />
          <img
            src="/cr.png"
            alt="center right ticket line"
            className="absolute -left-16 top-[30%]"
          />
          <img
            src="/br.png"
            alt="bottom right ticket line"
            className="absolute bottom-6 right-12"
          />
        </div>
      </section>
      <div className="mt-2 flex h-4 w-full items-center justify-center gap-x-1">
        {scrollSnaps.length > 1 ? (
          <>
            {scrollSnaps.map((_, index) => {
              return (
                <CarouselDots
                  key={index}
                  onClick={() => scrollTo(index)}
                  selected={index === selectedIndex}
                />
              )
            })}
          </>
        ) : null}
      </div>
    </>
  )
}

export default Spotlight
