const BookingSteps = () => {
  return (
    <div className="mx-auto max-w-5xl px-2 md:px-10">
      <div className="flex flex-col justify-center gap-x-2 py-6 text-center text-2xl font-medium tracking-wide md:py-12 md:text-3xl lg:flex-row lg:text-5xl">
        <span>TICKET BOOKING IN</span>
        <div className="flex flex-row justify-center gap-x-2">
          <span className="text-pallet-pink"> THREE</span>
          <span className="text-pallet-green"> EASY</span>
          <span className="text-primary-blue"> STEPS</span>
        </div>
      </div>
      <div className="grid grid-cols-12 gap-4 text-sm md:gap-y-0 md:text-base">
        <div className="col-span-12 mx-auto flex h-full w-1/2 flex-col items-center md:col-span-4 md:w-full">
          <div className="bg-pallet-red px-2 py-4 text-center text-white md:h-1/2 md:pb-8 md:pt-6">
            <p className="font-bold">DISCOVER & DIVE IN</p>
            <p className="mx-auto mt-2 font-light leading-4 md:w-3/4">
              Browse and explore the vibrant tapestry of upcoming events in your
              city
            </p>
          </div>

          <div>
            <img
              src="/tc1.png"
              alt="ticket-character-1"
              className="h-full w-20 md:w-36"
            />
          </div>
        </div>
        <div className="col-span-12 mx-auto flex h-full w-1/2 flex-col items-center md:col-span-4 md:w-full">
          <div className="bg-pallet-yellow px-2 py-4 text-center md:h-1/2 md:pb-8 md:pt-6">
            <p className="font-bold">NO STRINGS ATTACHED</p>
            <p className="mx-auto mt-2 font-light leading-4 md:w-3/4">
              Say goodbye to the hassle of account creation. Secure your pass in
              a snap
            </p>
          </div>

          <div>
            <img
              src="/tc2.png"
              alt="ticket-character-2"
              className="h-full w-20 md:w-36"
            />
          </div>
        </div>
        <div className="col-span-12 mx-auto flex h-full w-1/2 flex-col items-center md:col-span-4 md:w-full">
          <div className="bg-primary-blue px-2 py-4 text-center text-white md:h-1/2 md:pb-8 md:pt-6">
            <p className="font-bold">INSTANT CONFIRMATION</p>
            <p className="mx-auto mt-2 font-light leading-4 md:w-3/4">
              Your ticket lands straight in your phone the moment your payment
              is through
            </p>
          </div>

          <div>
            <img
              src="/tc3.png"
              alt="ticket-character-3"
              className="h-full w-20 md:w-36"
            />
          </div>
        </div>
      </div>
    </div>
  )
}

export default BookingSteps
