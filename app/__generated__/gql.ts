/* eslint-disable */
import * as types from './graphql';
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';

/**
 * Map of all GraphQL operations in the project.
 *
 * This map has several performance disadvantages:
 * 1. It is not tree-shakeable, so it will include all operations in the project.
 * 2. It is not minifiable, so the string of a GraphQL query will be multiple times inside the bundle.
 * 3. It does not support dead code elimination, so it will add unused operations.
 *
 * Therefore it is highly recommended to use the babel or swc plugin for production.
 * Learn more about it here: https://the-guild.dev/graphql/codegen/plugins/presets/preset-client#reducing-bundle-size
 */
type Documents = {
    "\n  query TicketStats($eventID: ID!) {\n    getTicketStatsForEvent(event_id: $eventID) {\n      total_amount\n      total_tip_amount\n      total_tickets_sold\n      ticket_type_stats {\n        num_of_tickets\n        ticket_type\n      }\n    }\n  }\n": typeof types.TicketStatsDocument,
    "\n  mutation AddSpotlight(\n    $image: Upload!\n    $url: String\n    $isExternal: Boolean\n    $order: Int\n  ) {\n    addSpotlight(\n      image: $image\n      url: $url\n      is_external: $isExternal\n      order: $order\n    ) {\n      message\n    }\n  }\n": typeof types.AddSpotlightDocument,
    "\n  mutation CreateSportEventPayment(\n    $ticketTypeId: Int!\n    $mobileNumber: String!\n    $numberOfTickets: Int\n    $username: String!\n    $seatNumbers: [Int!]!\n    $tipAmount: Float\n    $tipMEssage: String\n  ) {\n    createSportEventPayment(\n      ticket_type_id: $ticketTypeId\n      mobile_number: $mobileNumber\n      number_of_tickets: $numberOfTickets\n      username: $username\n      seat_numbers: $seatNumbers\n      tip_amount: $tipAmount\n      tip_message: $tipMEssage\n    ) {\n      order_id\n      payment_id\n      provider_type\n      goto_url\n      username\n      mobile_number\n    }\n  }\n": typeof types.CreateSportEventPaymentDocument,
    "\n  mutation CreateEvent(\n    $name: String!\n    $description: String\n    $location: String!\n    $eventTicketType: [CreateEventTicketTypeInput!]\n    $eventImages: [Upload]!\n    $startDate: DateTime!\n    $endDate: DateTime!\n    $startBookingDate: DateTime\n    $address: String!\n    $orgName: String!\n    $orgContactNumber: String!\n    $isPublished: Boolean!\n    $isPrivateEvent: Boolean\n    $faq: String\n    $artists: [EventArtistsInput]\n    $openMicLink: String\n    $city: String!\n    $eventType: EventType\n  ) {\n    createEvent(\n      name: $name\n      description: $description\n      location: $location\n      event_ticket_types: $eventTicketType\n      event_images: $eventImages\n      start_date: $startDate\n      end_date: $endDate\n      start_booking_date: $startBookingDate\n      organizer_name: $orgName\n      org_contact_number: $orgContactNumber\n      address: $address\n      is_published: $isPublished\n      is_private_event: $isPrivateEvent\n      faq: $faq\n      artists: $artists\n      open_mic_link: $openMicLink\n      city: $city\n      event_type: $eventType\n    ) {\n      id\n      is_published\n    }\n  }\n": typeof types.CreateEventDocument,
    "\n  mutation CreatePayment(\n    $eventTicketTypeId: ID!\n    $mobileNumber: String!\n    $username: String!\n    $numberOfTickets: Int!\n    $couponHash: String\n    $isGift: Boolean\n    $recipientName: String\n    $recipientPhone: String\n    $recipientMessage: String\n    $tipAmount: Float\n    $tipMessage: String\n  ) {\n    createPayment(\n      event_ticket_type_id: $eventTicketTypeId\n      mobile_number: $mobileNumber\n      username: $username\n      num_of_tickets: $numberOfTickets\n      coupon_hash: $couponHash\n      is_gift: $isGift\n      recipient_name: $recipientName\n      recipient_phone: $recipientPhone\n      recipient_message: $recipientMessage\n      tip_amount: $tipAmount\n      tip_message: $tipMessage\n    ) {\n      order_id\n      payment_id\n      provider_type\n      goto_url\n      mobile_number\n      username\n    }\n  }\n": typeof types.CreatePaymentDocument,
    "\n  mutation DeleteSpotlight($id: Int!) {\n    deleteSpotlight(id: $id) {\n      message\n    }\n  }\n": typeof types.DeleteSpotlightDocument,
    "\n  mutation Logout {\n    logout {\n      message\n    }\n  }\n": typeof types.LogoutDocument,
    "\n  mutation RedeemCoupon($coupon: String!, $eventId: ID!) {\n    redeemCoupon(coupon: $coupon, event_id: $eventId) {\n      rc\n      amount\n    }\n  }\n": typeof types.RedeemCouponDocument,
    "\n  mutation UpdateSpotlight(\n    $id: Int!\n    $image: Upload\n    $url: String\n    $order: Int\n    $isExternal: Boolean\n  ) {\n    updateSpotlight(\n      id: $id\n      image: $image\n      url: $url\n      is_external: $isExternal\n      order: $order\n    ) {\n      message\n    }\n  }\n": typeof types.UpdateSpotlightDocument,
    "\n  mutation UpdateEvent(\n    $id: ID!\n    $name: String\n    $description: String\n    $location: String\n    $eventImages: [Upload]\n    $address: String\n    $orgName: String\n    $orgContactNumber: String\n    $isPublished: Boolean!\n    $isPrivateEvent: Boolean\n    $startDate: DateTime\n    $endDate: DateTime\n    $startBookingDate: DateTime\n    $openMicLink: String\n    $seatingPlan: Upload\n    $city: String\n    $faq: String\n  ) {\n    updateEvent(\n      id: $id\n      name: $name\n      description: $description\n      location: $location\n      event_images: $eventImages\n      organizer_name: $orgName\n      org_contact_number: $orgContactNumber\n      address: $address\n      is_published: $isPublished\n      is_private_event: $isPrivateEvent\n      start_date: $startDate\n      end_date: $endDate\n      start_booking_date: $startBookingDate\n      open_mic_link: $openMicLink\n      seating_plan: $seatingPlan\n      city: $city\n      faq: $faq\n    ) {\n      id\n    }\n  }\n": typeof types.UpdateEventDocument,
    "\n  mutation UpsertEventArtist(\n    $id: ID\n    $name: String\n    $avatar: Upload\n    $order: Int\n    $eventId: ID!\n    $social_link: String\n  ) {\n    upsertEventArtist(\n      id: $id\n      name: $name\n      avatar: $avatar\n      order: $order\n      event_id: $eventId\n      social_link: $social_link\n    ) {\n      id\n    }\n  }\n": typeof types.UpsertEventArtistDocument,
    "\n  query EventBySlug($slug: String!) {\n    eventBySlug(slug: $slug) {\n      id\n      name\n      description\n      type\n      location\n      eventTicketTypes {\n        id\n        ticket_type\n        price\n        maximum_capacity\n        status\n        is_convenience_fee_incl\n        is_gst_incl\n      }\n      seatingPlan {\n        id\n        path\n      }\n      images {\n        id\n        path\n        hash\n        width\n        height\n      }\n      slug\n      start_date\n      end_date\n      start_booking_date\n      address\n      org_contact_number\n      organizer_name\n      faq\n      artists {\n        id\n        name\n        avatar\n        social_link\n      }\n      open_mic_link\n      coupons {\n        id\n      }\n    }\n  }\n": typeof types.EventBySlugDocument,
    "\n  query Events(\n    $page: Int!\n    $currentDatetime: DateTime!\n    $name: String\n    $event_types: [String!]\n    $first: Int!\n  ) {\n    getEvents(\n      page: $page\n      current_datetime: $currentDatetime\n      name: $name\n      event_types: $event_types\n      first: $first\n    ) {\n      data {\n        id\n        name\n        start_date\n        end_date\n        type\n        images {\n          id\n          path\n          hash\n          width\n          height\n        }\n        slug\n        eventTicketTypes {\n          price\n          ticket_type\n        }\n        start_booking_date\n        open_mic_link\n        city\n      }\n      paginatorInfo {\n        lastPage\n        hasMorePages\n        currentPage\n      }\n    }\n  }\n": typeof types.EventsDocument,
    "\n  query EventsForSuperAdmin(\n    $page: Int!\n    $currentDatetime: DateTime!\n    $name: String\n    $first: Int!\n    $type: String! # you can't run codegen without this coz it's required\n  ) {\n    events(\n      page: $page\n      current_datetime: $currentDatetime\n      name: $name\n      first: $first\n      type: $type\n    ) {\n      data {\n        id\n        name\n      }\n    }\n  }\n": typeof types.EventsForSuperAdminDocument,
    "\n  query getEvents(\n    $first: Int!\n    $page: Int\n    $name: String\n    $eventTypes: [String!]\n    $sortOrder: String\n  ) {\n    getEvents(\n      first: $first\n      page: $page\n      name: $name\n      event_types: $eventTypes\n      sort_order: $sortOrder\n    ) {\n      data {\n        id\n        name\n        type\n        start_date\n        end_date\n        images {\n          id\n          path\n          hash\n          width\n          height\n        }\n        slug\n        eventTicketTypes {\n          price\n          ticket_type\n        }\n        sportTicketTypes {\n          price\n          ticket_type\n        }\n        start_booking_date\n        open_mic_link\n        city\n      }\n      paginatorInfo {\n        hasMorePages\n        currentPage\n        lastPage\n      }\n    }\n  }\n": typeof types.GetEventsDocument,
    "\n  query GetSeatsForSportsEvent($ticketTypeId: Int!) {\n    getSeatsForSportsEvent(ticket_type_id: $ticketTypeId) {\n      all_seats\n      available_seats\n      price\n      convenience_fee\n    }\n  }\n": typeof types.GetSeatsForSportsEventDocument,
    "\n  query GetSpotlightList {\n    getSpotlightList {\n      id\n      app_image_id\n      url\n      is_external\n      order\n      image {\n        id\n        path\n        hash\n      }\n    }\n  }\n": typeof types.GetSpotlightListDocument,
    "\n  query GetConvenienceFee($ticketTypeId: ID!) {\n    getConvenienceFee(ticket_type_id: $ticketTypeId) {\n      amount\n    }\n  }\n": typeof types.GetConvenienceFeeDocument,
    "\n  query GetCouponsForEvent($eventId: ID!) {\n    getCouponsForEvent(event_id: $eventId) {\n      id\n      name\n      expiry_date\n      max_usage\n      is_published\n      discount_percent\n      coupon_usage_count\n    }\n  }\n": typeof types.GetCouponsForEventDocument,
    "\n  query EventById($id: ID!) {\n    eventById(id: $id) {\n      id\n      name\n      description\n      location\n      images {\n        id\n        path\n        hash\n        width\n        height\n      }\n      seatingPlan {\n        id\n        path\n        hash\n      }\n      address\n      org_contact_number\n      is_published\n      is_private_event\n      organizer_name\n      eventTicketTypes {\n        id\n        ticket_type\n        price\n        maximum_capacity\n      }\n      slug\n      start_date\n      end_date\n      start_booking_date\n      artists {\n        id\n        name\n        avatar\n        social_link\n        order\n      }\n      open_mic_link\n      city\n      faq\n    }\n  }\n": typeof types.EventByIdDocument,
    "\n  query GetGuestList(\n    $eventId: ID!\n    $first: Int!\n    $page: Int\n    $userName: String\n    $mobileNumber: String\n    $ticketTypeId: ID\n  ) {\n    getGuestList(\n      event_id: $eventId\n      first: $first\n      page: $page\n      username: $userName\n      mobile_number: $mobileNumber\n      ticket_type_id: $ticketTypeId\n    ) {\n      __typename\n      ... on SportGuestList {\n        data {\n          id\n          username\n          mobile_number\n          number_of_tickets\n          seat_numbers\n          scanned_at\n          paymentOrder {\n            paymentable {\n              __typename\n              ... on PhonePePaymentOrder {\n                order_id\n                payment_id\n              }\n              ... on RzpayPaymentOrder {\n                order_id\n                payment_id\n              }\n            }\n          }\n          eventTicketType {\n            id\n            ticket_type\n            slug\n          }\n          tip_amount\n          tip_message\n        }\n        paginatorInfo {\n          lastPage\n          total\n          currentPage\n          hasMorePages\n        }\n      }\n      ... on GeneralGuestList {\n        data {\n          id\n          username\n          mobile_number\n          number_of_tickets\n          scanned_at\n          paymentOrder {\n            paymentable {\n              __typename\n              ... on PhonePePaymentOrder {\n                order_id\n                payment_id\n              }\n              ... on RzpayPaymentOrder {\n                order_id\n                payment_id\n              }\n            }\n          }\n          eventTicketType {\n            id\n            ticket_type\n          }\n          tip_amount\n          tip_message\n        }\n        paginatorInfo {\n          lastPage\n          total\n          currentPage\n          hasMorePages\n        }\n      }\n    }\n  }\n": typeof types.GetGuestListDocument,
    "\n  query GetInvitees(\n    $username: String\n    $mobileNumber: String\n    $eventId: ID!\n    $first: Int!\n    $page: Int\n  ) {\n    getInvitees(\n      username: $username\n      mobile_number: $mobileNumber\n      event_id: $eventId\n      first: $first\n      page: $page\n    ) {\n      data {\n        id\n        username\n        mobile_number\n        whatsapp_sent_status\n        number_of_tickets\n        scanned_at\n        paymentOrder {\n          paymentable {\n            __typename\n            ... on PhonePePaymentOrder {\n              order_id\n              payment_id\n            }\n            ... on RzpayPaymentOrder {\n              order_id\n              payment_id\n            }\n          }\n        }\n      }\n      paginatorInfo {\n        lastPage\n        total\n        hasMorePages\n        currentPage\n      }\n    }\n  }\n": typeof types.GetInviteesDocument,
    "\n  query GetMe {\n    getMe {\n      user {\n        id\n        role\n        identifier\n      }\n      token\n    }\n  }\n": typeof types.GetMeDocument,
    "\n  query MyEvents($page: Int!, $first: Int!) {\n    myEvents(page: $page, first: $first) {\n      data {\n        id\n        name\n        description\n        location\n        eventTicketTypes {\n          id\n          ticket_type\n          price\n          maximum_capacity\n        }\n        images {\n          id\n          path\n          hash\n          width\n          height\n        }\n        slug\n        start_date\n        end_date\n        address\n        org_contact_number\n        is_published\n        is_private_event\n      }\n      paginatorInfo {\n        lastPage\n        hasMorePages\n        currentPage\n      }\n    }\n  }\n": typeof types.MyEventsDocument,
    "\n  query GetOrganizers($page: Int, $name: String) {\n    organizerList(page: $page, name: $name) {\n      hasMorePages\n      currentPage\n      total\n      organizers {\n        id\n        identifier\n      }\n    }\n  }\n": typeof types.GetOrganizersDocument,
    "\n  query PastEvents(\n    $first: Int!\n    $currentDatetime: DateTime!\n    $page: Int\n    $type: String\n  ) {\n    getPastEvents(\n      first: $first\n      current_datetime: $currentDatetime\n      page: $page\n      type: $type\n    ) {\n      data {\n        id\n        name\n        type\n        end_date\n        images {\n          id\n          path\n          hash\n          width\n          height\n        }\n        slug\n      }\n      paginatorInfo {\n        hasMorePages\n        currentPage\n        lastPage\n      }\n    }\n  }\n": typeof types.PastEventsDocument,
    "\n  query SportsBySlug($slug: String!) {\n    eventBySlug(slug: $slug) {\n      id\n      name\n      description\n      location\n      type\n      sportTicketTypes {\n        id\n        price\n        ticket_type\n        start_seat_number\n        end_seat_number\n        slug\n      }\n      seatingPlan {\n        id\n        path\n      }\n      images {\n        id\n        path\n        hash\n        width\n        height\n      }\n      slug\n      start_date\n      end_date\n      start_booking_date\n      address\n      org_contact_number\n      organizer_name\n      faq\n      artists {\n        id\n        name\n        avatar\n        social_link\n      }\n      open_mic_link\n      coupons {\n        id\n      }\n    }\n  }\n": typeof types.SportsBySlugDocument,
    "\n  query Sports(\n    $page: Int!\n    $currentDatetime: DateTime!\n    $name: String\n    $type: String!\n  ) {\n    events(\n      page: $page\n      current_datetime: $currentDatetime\n      name: $name\n      type: $type\n    ) {\n      data {\n        id\n        name\n        start_date\n        images {\n          id\n          path\n          hash\n          width\n          height\n        }\n        slug\n        sportTicketTypes {\n          price\n          ticket_type\n          start_seat_number\n          end_seat_number\n        }\n        start_booking_date\n        open_mic_link\n        city\n      }\n      paginatorInfo {\n        lastPage\n      }\n    }\n  }\n": typeof types.SportsDocument,
    "\n  mutation AddStaff($username: String!, $password: String!) {\n    addStaff(username: $username, password: $password) {\n      id\n    }\n  }\n": typeof types.AddStaffDocument,
    "\n  query GetStaffs {\n    getStaffs {\n      id\n      identifier\n      role\n    }\n  }\n": typeof types.GetStaffsDocument,
    "\n  mutation DeleteStaff($id: ID!) {\n    deleteStaff(id: $id) {\n      message\n    }\n  }\n": typeof types.DeleteStaffDocument,
    "\n  mutation CreateCoupon($data: [CreateCouponInput!]!) {\n    createCoupon(data: $data) {\n      message\n    }\n  }\n": typeof types.CreateCouponDocument,
    "\n  mutation DeleteCoupon($couponId: ID!) {\n    deleteCoupon(coupon_id: $couponId) {\n      id\n    }\n  }\n": typeof types.DeleteCouponDocument,
    "\n  mutation UpdateCoupon(\n    $couponId: ID!\n    $name: String\n    $expiry_date: DateTime\n    $event_id: ID\n    $discount_percent: Float\n    $max_usage: Int\n    $is_published: Boolean\n  ) {\n    updateCoupon(\n      coupon_id: $couponId\n      name: $name\n      expiry_date: $expiry_date\n      event_id: $event_id\n      discount_percent: $discount_percent\n      max_usage: $max_usage\n      is_published: $is_published\n    ) {\n      id\n    }\n  }\n": typeof types.UpdateCouponDocument,
    "\n  mutation AddInvitee(\n    $eventId: ID!\n    $username: String!\n    $mobileNumber: String!\n    $numberOfTickets: Int!\n  ) {\n    addInvitee(\n      event_id: $eventId\n      username: $username\n      mobile_number: $mobileNumber\n      number_of_tickets: $numberOfTickets\n    ) {\n      id\n    }\n  }\n": typeof types.AddInviteeDocument,
    "\n  mutation DeleteEvent($id: ID!) {\n    deleteEvent(id: $id) {\n      id\n    }\n  }\n": typeof types.DeleteEventDocument,
    "\n  mutation DeleteImageById($id: ID!) {\n    deleteImageById(id: $id) {\n      message\n    }\n  }\n": typeof types.DeleteImageByIdDocument,
    "\n  mutation DeleteEventArtist($id: ID!) {\n    deleteEventArtist(event_artist_id: $id) {\n      message\n    }\n  }\n": typeof types.DeleteEventArtistDocument,
    "\n  query TicketAvailable($orderId: String!, $paymentId: String!) {\n    isTicketDownloadable(order_id: $orderId, payment_id: $paymentId) {\n      ticket_path\n      amount\n    }\n  }\n": typeof types.TicketAvailableDocument,
    "\n  query GetBookedSeatsForSeatedEvent($eventId: ID!) {\n    getBookedSeatsForSeatedEvent(event_id: $eventId)\n  }\n": typeof types.GetBookedSeatsForSeatedEventDocument,
    "\n  mutation CreatePaymentForSeatTickets(\n    $eventId: Int!\n    $mobileNumber: String!\n    $seatNumbers: [String!]!\n    $tipAmount: Float\n    $tipMessage: String\n    $username: String!\n  ) {\n    createPaymentForSeatedTicket(\n      event_id: $eventId\n      mobile_number: $mobileNumber\n      seat_numbers: $seatNumbers\n      tip_amount: $tipAmount\n      tip_message: $tipMessage\n      username: $username\n    ) {\n      order_id\n      payment_id\n      provider_type\n      goto_url\n      username\n      mobile_number\n    }\n  }\n": typeof types.CreatePaymentForSeatTicketsDocument,
    "\n  mutation OnBoard(\n    $email: String!\n    $name: String!\n    $address: String\n    $mobileNumber: String!\n  ) {\n    onboardOrganizer(\n      email: $email\n      name: $name\n      address: $address\n      mobile_number: $mobileNumber\n    ) {\n      id\n    }\n  }\n": typeof types.OnBoardDocument,
    "\n  mutation UserRequestRefund($eventId: ID!, $phoneNumber: String!) {\n    userRequestRefund(event_id: $eventId, phone_number: $phoneNumber) {\n      message\n    }\n  }\n": typeof types.UserRequestRefundDocument,
    "\n  mutation SubmitQRCode($qrCode: String!) {\n    submitQRCode(qr_code: $qrCode) {\n      error\n    }\n  }\n": typeof types.SubmitQrCodeDocument,
    "\n  query StaffEvents {\n    adminEvents {\n      id\n      name\n      eventTicketTypes {\n        id\n        ticket_type\n        price\n        maximum_capacity\n      }\n    }\n  }\n": typeof types.StaffEventsDocument,
    "\n  mutation UpsertSetting($key: String!, $value: String) {\n    upsertSetting(key: $key, value: $value) {\n      id\n    }\n  }\n": typeof types.UpsertSettingDocument,
    "\n  query GetSettingByKey($key: String) {\n    getSettingByKey(key: $key) {\n      id\n      key\n      value\n    }\n  }\n": typeof types.GetSettingByKeyDocument,
    "\n  query EventAttendees(\n    $eventID: ID!\n    $first: Int!\n    $page: Int\n    $username: String\n    $mobileNumber: String\n    $paymentStatus: String\n  ) {\n    eventAttendees(\n      event_id: $eventID\n      first: $first\n      page: $page\n      username: $username\n      mobile_number: $mobileNumber\n      payment_status: $paymentStatus\n    ) {\n      data {\n        id\n        username\n        mobile_number\n        amount_paid\n        paymentOrder {\n          paymentable {\n            __typename\n            ... on PhonePePaymentOrder {\n              id\n              order_id\n              status\n              refund_status\n              refund_error_detail\n            }\n            ... on RzpayPaymentOrder {\n              id\n              order_id\n              status\n              refund_status\n              refund_error_detail\n            }\n          }\n        }\n        payment_status\n        number_of_tickets\n        ticket_path\n      }\n      paginatorInfo {\n        lastPage\n        total\n        currentPage\n      }\n    }\n  }\n": typeof types.EventAttendeesDocument,
    "\n  mutation IssueRefund($amount: Float!, $orderID: String!) {\n    issueRefund(amount: $amount, order_id: $orderID) {\n      status\n      reason\n    }\n  }\n": typeof types.IssueRefundDocument,
    "\n  mutation AddOrganizer($name: String!, $password: String!) {\n    addOrganizer(name: $name, password: $password) {\n      message\n    }\n  }\n": typeof types.AddOrganizerDocument,
    "\n  mutation UpdateOrganizer($id: ID!, $name: String, $password: String) {\n    updateOrganizer(id: $id, name: $name, password: $password) {\n      id\n    }\n  }\n": typeof types.UpdateOrganizerDocument,
    "\n  query CheckPhonepeHealth {\n    checkPhonePeHealth {\n      data\n    }\n  }\n": typeof types.CheckPhonepeHealthDocument,
    "\n  mutation NotifyUpcomingEvent($id: ID!) {\n    notifyUpcomingEvent(id: $id) {\n      id\n    }\n  }\n": typeof types.NotifyUpcomingEventDocument,
    "\n  mutation Login($identifier: String!, $password: String!) {\n    adminLogin(username: $identifier, password: $password) {\n      user {\n        id\n        identifier\n        role\n      }\n      token\n    }\n  }\n": typeof types.LoginDocument,
};
const documents: Documents = {
    "\n  query TicketStats($eventID: ID!) {\n    getTicketStatsForEvent(event_id: $eventID) {\n      total_amount\n      total_tip_amount\n      total_tickets_sold\n      ticket_type_stats {\n        num_of_tickets\n        ticket_type\n      }\n    }\n  }\n": types.TicketStatsDocument,
    "\n  mutation AddSpotlight(\n    $image: Upload!\n    $url: String\n    $isExternal: Boolean\n    $order: Int\n  ) {\n    addSpotlight(\n      image: $image\n      url: $url\n      is_external: $isExternal\n      order: $order\n    ) {\n      message\n    }\n  }\n": types.AddSpotlightDocument,
    "\n  mutation CreateSportEventPayment(\n    $ticketTypeId: Int!\n    $mobileNumber: String!\n    $numberOfTickets: Int\n    $username: String!\n    $seatNumbers: [Int!]!\n    $tipAmount: Float\n    $tipMEssage: String\n  ) {\n    createSportEventPayment(\n      ticket_type_id: $ticketTypeId\n      mobile_number: $mobileNumber\n      number_of_tickets: $numberOfTickets\n      username: $username\n      seat_numbers: $seatNumbers\n      tip_amount: $tipAmount\n      tip_message: $tipMEssage\n    ) {\n      order_id\n      payment_id\n      provider_type\n      goto_url\n      username\n      mobile_number\n    }\n  }\n": types.CreateSportEventPaymentDocument,
    "\n  mutation CreateEvent(\n    $name: String!\n    $description: String\n    $location: String!\n    $eventTicketType: [CreateEventTicketTypeInput!]\n    $eventImages: [Upload]!\n    $startDate: DateTime!\n    $endDate: DateTime!\n    $startBookingDate: DateTime\n    $address: String!\n    $orgName: String!\n    $orgContactNumber: String!\n    $isPublished: Boolean!\n    $isPrivateEvent: Boolean\n    $faq: String\n    $artists: [EventArtistsInput]\n    $openMicLink: String\n    $city: String!\n    $eventType: EventType\n  ) {\n    createEvent(\n      name: $name\n      description: $description\n      location: $location\n      event_ticket_types: $eventTicketType\n      event_images: $eventImages\n      start_date: $startDate\n      end_date: $endDate\n      start_booking_date: $startBookingDate\n      organizer_name: $orgName\n      org_contact_number: $orgContactNumber\n      address: $address\n      is_published: $isPublished\n      is_private_event: $isPrivateEvent\n      faq: $faq\n      artists: $artists\n      open_mic_link: $openMicLink\n      city: $city\n      event_type: $eventType\n    ) {\n      id\n      is_published\n    }\n  }\n": types.CreateEventDocument,
    "\n  mutation CreatePayment(\n    $eventTicketTypeId: ID!\n    $mobileNumber: String!\n    $username: String!\n    $numberOfTickets: Int!\n    $couponHash: String\n    $isGift: Boolean\n    $recipientName: String\n    $recipientPhone: String\n    $recipientMessage: String\n    $tipAmount: Float\n    $tipMessage: String\n  ) {\n    createPayment(\n      event_ticket_type_id: $eventTicketTypeId\n      mobile_number: $mobileNumber\n      username: $username\n      num_of_tickets: $numberOfTickets\n      coupon_hash: $couponHash\n      is_gift: $isGift\n      recipient_name: $recipientName\n      recipient_phone: $recipientPhone\n      recipient_message: $recipientMessage\n      tip_amount: $tipAmount\n      tip_message: $tipMessage\n    ) {\n      order_id\n      payment_id\n      provider_type\n      goto_url\n      mobile_number\n      username\n    }\n  }\n": types.CreatePaymentDocument,
    "\n  mutation DeleteSpotlight($id: Int!) {\n    deleteSpotlight(id: $id) {\n      message\n    }\n  }\n": types.DeleteSpotlightDocument,
    "\n  mutation Logout {\n    logout {\n      message\n    }\n  }\n": types.LogoutDocument,
    "\n  mutation RedeemCoupon($coupon: String!, $eventId: ID!) {\n    redeemCoupon(coupon: $coupon, event_id: $eventId) {\n      rc\n      amount\n    }\n  }\n": types.RedeemCouponDocument,
    "\n  mutation UpdateSpotlight(\n    $id: Int!\n    $image: Upload\n    $url: String\n    $order: Int\n    $isExternal: Boolean\n  ) {\n    updateSpotlight(\n      id: $id\n      image: $image\n      url: $url\n      is_external: $isExternal\n      order: $order\n    ) {\n      message\n    }\n  }\n": types.UpdateSpotlightDocument,
    "\n  mutation UpdateEvent(\n    $id: ID!\n    $name: String\n    $description: String\n    $location: String\n    $eventImages: [Upload]\n    $address: String\n    $orgName: String\n    $orgContactNumber: String\n    $isPublished: Boolean!\n    $isPrivateEvent: Boolean\n    $startDate: DateTime\n    $endDate: DateTime\n    $startBookingDate: DateTime\n    $openMicLink: String\n    $seatingPlan: Upload\n    $city: String\n    $faq: String\n  ) {\n    updateEvent(\n      id: $id\n      name: $name\n      description: $description\n      location: $location\n      event_images: $eventImages\n      organizer_name: $orgName\n      org_contact_number: $orgContactNumber\n      address: $address\n      is_published: $isPublished\n      is_private_event: $isPrivateEvent\n      start_date: $startDate\n      end_date: $endDate\n      start_booking_date: $startBookingDate\n      open_mic_link: $openMicLink\n      seating_plan: $seatingPlan\n      city: $city\n      faq: $faq\n    ) {\n      id\n    }\n  }\n": types.UpdateEventDocument,
    "\n  mutation UpsertEventArtist(\n    $id: ID\n    $name: String\n    $avatar: Upload\n    $order: Int\n    $eventId: ID!\n    $social_link: String\n  ) {\n    upsertEventArtist(\n      id: $id\n      name: $name\n      avatar: $avatar\n      order: $order\n      event_id: $eventId\n      social_link: $social_link\n    ) {\n      id\n    }\n  }\n": types.UpsertEventArtistDocument,
    "\n  query EventBySlug($slug: String!) {\n    eventBySlug(slug: $slug) {\n      id\n      name\n      description\n      type\n      location\n      eventTicketTypes {\n        id\n        ticket_type\n        price\n        maximum_capacity\n        status\n        is_convenience_fee_incl\n        is_gst_incl\n      }\n      seatingPlan {\n        id\n        path\n      }\n      images {\n        id\n        path\n        hash\n        width\n        height\n      }\n      slug\n      start_date\n      end_date\n      start_booking_date\n      address\n      org_contact_number\n      organizer_name\n      faq\n      artists {\n        id\n        name\n        avatar\n        social_link\n      }\n      open_mic_link\n      coupons {\n        id\n      }\n    }\n  }\n": types.EventBySlugDocument,
    "\n  query Events(\n    $page: Int!\n    $currentDatetime: DateTime!\n    $name: String\n    $event_types: [String!]\n    $first: Int!\n  ) {\n    getEvents(\n      page: $page\n      current_datetime: $currentDatetime\n      name: $name\n      event_types: $event_types\n      first: $first\n    ) {\n      data {\n        id\n        name\n        start_date\n        end_date\n        type\n        images {\n          id\n          path\n          hash\n          width\n          height\n        }\n        slug\n        eventTicketTypes {\n          price\n          ticket_type\n        }\n        start_booking_date\n        open_mic_link\n        city\n      }\n      paginatorInfo {\n        lastPage\n        hasMorePages\n        currentPage\n      }\n    }\n  }\n": types.EventsDocument,
    "\n  query EventsForSuperAdmin(\n    $page: Int!\n    $currentDatetime: DateTime!\n    $name: String\n    $first: Int!\n    $type: String! # you can't run codegen without this coz it's required\n  ) {\n    events(\n      page: $page\n      current_datetime: $currentDatetime\n      name: $name\n      first: $first\n      type: $type\n    ) {\n      data {\n        id\n        name\n      }\n    }\n  }\n": types.EventsForSuperAdminDocument,
    "\n  query getEvents(\n    $first: Int!\n    $page: Int\n    $name: String\n    $eventTypes: [String!]\n    $sortOrder: String\n  ) {\n    getEvents(\n      first: $first\n      page: $page\n      name: $name\n      event_types: $eventTypes\n      sort_order: $sortOrder\n    ) {\n      data {\n        id\n        name\n        type\n        start_date\n        end_date\n        images {\n          id\n          path\n          hash\n          width\n          height\n        }\n        slug\n        eventTicketTypes {\n          price\n          ticket_type\n        }\n        sportTicketTypes {\n          price\n          ticket_type\n        }\n        start_booking_date\n        open_mic_link\n        city\n      }\n      paginatorInfo {\n        hasMorePages\n        currentPage\n        lastPage\n      }\n    }\n  }\n": types.GetEventsDocument,
    "\n  query GetSeatsForSportsEvent($ticketTypeId: Int!) {\n    getSeatsForSportsEvent(ticket_type_id: $ticketTypeId) {\n      all_seats\n      available_seats\n      price\n      convenience_fee\n    }\n  }\n": types.GetSeatsForSportsEventDocument,
    "\n  query GetSpotlightList {\n    getSpotlightList {\n      id\n      app_image_id\n      url\n      is_external\n      order\n      image {\n        id\n        path\n        hash\n      }\n    }\n  }\n": types.GetSpotlightListDocument,
    "\n  query GetConvenienceFee($ticketTypeId: ID!) {\n    getConvenienceFee(ticket_type_id: $ticketTypeId) {\n      amount\n    }\n  }\n": types.GetConvenienceFeeDocument,
    "\n  query GetCouponsForEvent($eventId: ID!) {\n    getCouponsForEvent(event_id: $eventId) {\n      id\n      name\n      expiry_date\n      max_usage\n      is_published\n      discount_percent\n      coupon_usage_count\n    }\n  }\n": types.GetCouponsForEventDocument,
    "\n  query EventById($id: ID!) {\n    eventById(id: $id) {\n      id\n      name\n      description\n      location\n      images {\n        id\n        path\n        hash\n        width\n        height\n      }\n      seatingPlan {\n        id\n        path\n        hash\n      }\n      address\n      org_contact_number\n      is_published\n      is_private_event\n      organizer_name\n      eventTicketTypes {\n        id\n        ticket_type\n        price\n        maximum_capacity\n      }\n      slug\n      start_date\n      end_date\n      start_booking_date\n      artists {\n        id\n        name\n        avatar\n        social_link\n        order\n      }\n      open_mic_link\n      city\n      faq\n    }\n  }\n": types.EventByIdDocument,
    "\n  query GetGuestList(\n    $eventId: ID!\n    $first: Int!\n    $page: Int\n    $userName: String\n    $mobileNumber: String\n    $ticketTypeId: ID\n  ) {\n    getGuestList(\n      event_id: $eventId\n      first: $first\n      page: $page\n      username: $userName\n      mobile_number: $mobileNumber\n      ticket_type_id: $ticketTypeId\n    ) {\n      __typename\n      ... on SportGuestList {\n        data {\n          id\n          username\n          mobile_number\n          number_of_tickets\n          seat_numbers\n          scanned_at\n          paymentOrder {\n            paymentable {\n              __typename\n              ... on PhonePePaymentOrder {\n                order_id\n                payment_id\n              }\n              ... on RzpayPaymentOrder {\n                order_id\n                payment_id\n              }\n            }\n          }\n          eventTicketType {\n            id\n            ticket_type\n            slug\n          }\n          tip_amount\n          tip_message\n        }\n        paginatorInfo {\n          lastPage\n          total\n          currentPage\n          hasMorePages\n        }\n      }\n      ... on GeneralGuestList {\n        data {\n          id\n          username\n          mobile_number\n          number_of_tickets\n          scanned_at\n          paymentOrder {\n            paymentable {\n              __typename\n              ... on PhonePePaymentOrder {\n                order_id\n                payment_id\n              }\n              ... on RzpayPaymentOrder {\n                order_id\n                payment_id\n              }\n            }\n          }\n          eventTicketType {\n            id\n            ticket_type\n          }\n          tip_amount\n          tip_message\n        }\n        paginatorInfo {\n          lastPage\n          total\n          currentPage\n          hasMorePages\n        }\n      }\n    }\n  }\n": types.GetGuestListDocument,
    "\n  query GetInvitees(\n    $username: String\n    $mobileNumber: String\n    $eventId: ID!\n    $first: Int!\n    $page: Int\n  ) {\n    getInvitees(\n      username: $username\n      mobile_number: $mobileNumber\n      event_id: $eventId\n      first: $first\n      page: $page\n    ) {\n      data {\n        id\n        username\n        mobile_number\n        whatsapp_sent_status\n        number_of_tickets\n        scanned_at\n        paymentOrder {\n          paymentable {\n            __typename\n            ... on PhonePePaymentOrder {\n              order_id\n              payment_id\n            }\n            ... on RzpayPaymentOrder {\n              order_id\n              payment_id\n            }\n          }\n        }\n      }\n      paginatorInfo {\n        lastPage\n        total\n        hasMorePages\n        currentPage\n      }\n    }\n  }\n": types.GetInviteesDocument,
    "\n  query GetMe {\n    getMe {\n      user {\n        id\n        role\n        identifier\n      }\n      token\n    }\n  }\n": types.GetMeDocument,
    "\n  query MyEvents($page: Int!, $first: Int!) {\n    myEvents(page: $page, first: $first) {\n      data {\n        id\n        name\n        description\n        location\n        eventTicketTypes {\n          id\n          ticket_type\n          price\n          maximum_capacity\n        }\n        images {\n          id\n          path\n          hash\n          width\n          height\n        }\n        slug\n        start_date\n        end_date\n        address\n        org_contact_number\n        is_published\n        is_private_event\n      }\n      paginatorInfo {\n        lastPage\n        hasMorePages\n        currentPage\n      }\n    }\n  }\n": types.MyEventsDocument,
    "\n  query GetOrganizers($page: Int, $name: String) {\n    organizerList(page: $page, name: $name) {\n      hasMorePages\n      currentPage\n      total\n      organizers {\n        id\n        identifier\n      }\n    }\n  }\n": types.GetOrganizersDocument,
    "\n  query PastEvents(\n    $first: Int!\n    $currentDatetime: DateTime!\n    $page: Int\n    $type: String\n  ) {\n    getPastEvents(\n      first: $first\n      current_datetime: $currentDatetime\n      page: $page\n      type: $type\n    ) {\n      data {\n        id\n        name\n        type\n        end_date\n        images {\n          id\n          path\n          hash\n          width\n          height\n        }\n        slug\n      }\n      paginatorInfo {\n        hasMorePages\n        currentPage\n        lastPage\n      }\n    }\n  }\n": types.PastEventsDocument,
    "\n  query SportsBySlug($slug: String!) {\n    eventBySlug(slug: $slug) {\n      id\n      name\n      description\n      location\n      type\n      sportTicketTypes {\n        id\n        price\n        ticket_type\n        start_seat_number\n        end_seat_number\n        slug\n      }\n      seatingPlan {\n        id\n        path\n      }\n      images {\n        id\n        path\n        hash\n        width\n        height\n      }\n      slug\n      start_date\n      end_date\n      start_booking_date\n      address\n      org_contact_number\n      organizer_name\n      faq\n      artists {\n        id\n        name\n        avatar\n        social_link\n      }\n      open_mic_link\n      coupons {\n        id\n      }\n    }\n  }\n": types.SportsBySlugDocument,
    "\n  query Sports(\n    $page: Int!\n    $currentDatetime: DateTime!\n    $name: String\n    $type: String!\n  ) {\n    events(\n      page: $page\n      current_datetime: $currentDatetime\n      name: $name\n      type: $type\n    ) {\n      data {\n        id\n        name\n        start_date\n        images {\n          id\n          path\n          hash\n          width\n          height\n        }\n        slug\n        sportTicketTypes {\n          price\n          ticket_type\n          start_seat_number\n          end_seat_number\n        }\n        start_booking_date\n        open_mic_link\n        city\n      }\n      paginatorInfo {\n        lastPage\n      }\n    }\n  }\n": types.SportsDocument,
    "\n  mutation AddStaff($username: String!, $password: String!) {\n    addStaff(username: $username, password: $password) {\n      id\n    }\n  }\n": types.AddStaffDocument,
    "\n  query GetStaffs {\n    getStaffs {\n      id\n      identifier\n      role\n    }\n  }\n": types.GetStaffsDocument,
    "\n  mutation DeleteStaff($id: ID!) {\n    deleteStaff(id: $id) {\n      message\n    }\n  }\n": types.DeleteStaffDocument,
    "\n  mutation CreateCoupon($data: [CreateCouponInput!]!) {\n    createCoupon(data: $data) {\n      message\n    }\n  }\n": types.CreateCouponDocument,
    "\n  mutation DeleteCoupon($couponId: ID!) {\n    deleteCoupon(coupon_id: $couponId) {\n      id\n    }\n  }\n": types.DeleteCouponDocument,
    "\n  mutation UpdateCoupon(\n    $couponId: ID!\n    $name: String\n    $expiry_date: DateTime\n    $event_id: ID\n    $discount_percent: Float\n    $max_usage: Int\n    $is_published: Boolean\n  ) {\n    updateCoupon(\n      coupon_id: $couponId\n      name: $name\n      expiry_date: $expiry_date\n      event_id: $event_id\n      discount_percent: $discount_percent\n      max_usage: $max_usage\n      is_published: $is_published\n    ) {\n      id\n    }\n  }\n": types.UpdateCouponDocument,
    "\n  mutation AddInvitee(\n    $eventId: ID!\n    $username: String!\n    $mobileNumber: String!\n    $numberOfTickets: Int!\n  ) {\n    addInvitee(\n      event_id: $eventId\n      username: $username\n      mobile_number: $mobileNumber\n      number_of_tickets: $numberOfTickets\n    ) {\n      id\n    }\n  }\n": types.AddInviteeDocument,
    "\n  mutation DeleteEvent($id: ID!) {\n    deleteEvent(id: $id) {\n      id\n    }\n  }\n": types.DeleteEventDocument,
    "\n  mutation DeleteImageById($id: ID!) {\n    deleteImageById(id: $id) {\n      message\n    }\n  }\n": types.DeleteImageByIdDocument,
    "\n  mutation DeleteEventArtist($id: ID!) {\n    deleteEventArtist(event_artist_id: $id) {\n      message\n    }\n  }\n": types.DeleteEventArtistDocument,
    "\n  query TicketAvailable($orderId: String!, $paymentId: String!) {\n    isTicketDownloadable(order_id: $orderId, payment_id: $paymentId) {\n      ticket_path\n      amount\n    }\n  }\n": types.TicketAvailableDocument,
    "\n  query GetBookedSeatsForSeatedEvent($eventId: ID!) {\n    getBookedSeatsForSeatedEvent(event_id: $eventId)\n  }\n": types.GetBookedSeatsForSeatedEventDocument,
    "\n  mutation CreatePaymentForSeatTickets(\n    $eventId: Int!\n    $mobileNumber: String!\n    $seatNumbers: [String!]!\n    $tipAmount: Float\n    $tipMessage: String\n    $username: String!\n  ) {\n    createPaymentForSeatedTicket(\n      event_id: $eventId\n      mobile_number: $mobileNumber\n      seat_numbers: $seatNumbers\n      tip_amount: $tipAmount\n      tip_message: $tipMessage\n      username: $username\n    ) {\n      order_id\n      payment_id\n      provider_type\n      goto_url\n      username\n      mobile_number\n    }\n  }\n": types.CreatePaymentForSeatTicketsDocument,
    "\n  mutation OnBoard(\n    $email: String!\n    $name: String!\n    $address: String\n    $mobileNumber: String!\n  ) {\n    onboardOrganizer(\n      email: $email\n      name: $name\n      address: $address\n      mobile_number: $mobileNumber\n    ) {\n      id\n    }\n  }\n": types.OnBoardDocument,
    "\n  mutation UserRequestRefund($eventId: ID!, $phoneNumber: String!) {\n    userRequestRefund(event_id: $eventId, phone_number: $phoneNumber) {\n      message\n    }\n  }\n": types.UserRequestRefundDocument,
    "\n  mutation SubmitQRCode($qrCode: String!) {\n    submitQRCode(qr_code: $qrCode) {\n      error\n    }\n  }\n": types.SubmitQrCodeDocument,
    "\n  query StaffEvents {\n    adminEvents {\n      id\n      name\n      eventTicketTypes {\n        id\n        ticket_type\n        price\n        maximum_capacity\n      }\n    }\n  }\n": types.StaffEventsDocument,
    "\n  mutation UpsertSetting($key: String!, $value: String) {\n    upsertSetting(key: $key, value: $value) {\n      id\n    }\n  }\n": types.UpsertSettingDocument,
    "\n  query GetSettingByKey($key: String) {\n    getSettingByKey(key: $key) {\n      id\n      key\n      value\n    }\n  }\n": types.GetSettingByKeyDocument,
    "\n  query EventAttendees(\n    $eventID: ID!\n    $first: Int!\n    $page: Int\n    $username: String\n    $mobileNumber: String\n    $paymentStatus: String\n  ) {\n    eventAttendees(\n      event_id: $eventID\n      first: $first\n      page: $page\n      username: $username\n      mobile_number: $mobileNumber\n      payment_status: $paymentStatus\n    ) {\n      data {\n        id\n        username\n        mobile_number\n        amount_paid\n        paymentOrder {\n          paymentable {\n            __typename\n            ... on PhonePePaymentOrder {\n              id\n              order_id\n              status\n              refund_status\n              refund_error_detail\n            }\n            ... on RzpayPaymentOrder {\n              id\n              order_id\n              status\n              refund_status\n              refund_error_detail\n            }\n          }\n        }\n        payment_status\n        number_of_tickets\n        ticket_path\n      }\n      paginatorInfo {\n        lastPage\n        total\n        currentPage\n      }\n    }\n  }\n": types.EventAttendeesDocument,
    "\n  mutation IssueRefund($amount: Float!, $orderID: String!) {\n    issueRefund(amount: $amount, order_id: $orderID) {\n      status\n      reason\n    }\n  }\n": types.IssueRefundDocument,
    "\n  mutation AddOrganizer($name: String!, $password: String!) {\n    addOrganizer(name: $name, password: $password) {\n      message\n    }\n  }\n": types.AddOrganizerDocument,
    "\n  mutation UpdateOrganizer($id: ID!, $name: String, $password: String) {\n    updateOrganizer(id: $id, name: $name, password: $password) {\n      id\n    }\n  }\n": types.UpdateOrganizerDocument,
    "\n  query CheckPhonepeHealth {\n    checkPhonePeHealth {\n      data\n    }\n  }\n": types.CheckPhonepeHealthDocument,
    "\n  mutation NotifyUpcomingEvent($id: ID!) {\n    notifyUpcomingEvent(id: $id) {\n      id\n    }\n  }\n": types.NotifyUpcomingEventDocument,
    "\n  mutation Login($identifier: String!, $password: String!) {\n    adminLogin(username: $identifier, password: $password) {\n      user {\n        id\n        identifier\n        role\n      }\n      token\n    }\n  }\n": types.LoginDocument,
};

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 *
 *
 * @example
 * ```ts
 * const query = graphql(`query GetUser($id: ID!) { user(id: $id) { name } }`);
 * ```
 *
 * The query argument is unknown!
 * Please regenerate the types.
 */
export function graphql(source: string): unknown;

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query TicketStats($eventID: ID!) {\n    getTicketStatsForEvent(event_id: $eventID) {\n      total_amount\n      total_tip_amount\n      total_tickets_sold\n      ticket_type_stats {\n        num_of_tickets\n        ticket_type\n      }\n    }\n  }\n"): (typeof documents)["\n  query TicketStats($eventID: ID!) {\n    getTicketStatsForEvent(event_id: $eventID) {\n      total_amount\n      total_tip_amount\n      total_tickets_sold\n      ticket_type_stats {\n        num_of_tickets\n        ticket_type\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation AddSpotlight(\n    $image: Upload!\n    $url: String\n    $isExternal: Boolean\n    $order: Int\n  ) {\n    addSpotlight(\n      image: $image\n      url: $url\n      is_external: $isExternal\n      order: $order\n    ) {\n      message\n    }\n  }\n"): (typeof documents)["\n  mutation AddSpotlight(\n    $image: Upload!\n    $url: String\n    $isExternal: Boolean\n    $order: Int\n  ) {\n    addSpotlight(\n      image: $image\n      url: $url\n      is_external: $isExternal\n      order: $order\n    ) {\n      message\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation CreateSportEventPayment(\n    $ticketTypeId: Int!\n    $mobileNumber: String!\n    $numberOfTickets: Int\n    $username: String!\n    $seatNumbers: [Int!]!\n    $tipAmount: Float\n    $tipMEssage: String\n  ) {\n    createSportEventPayment(\n      ticket_type_id: $ticketTypeId\n      mobile_number: $mobileNumber\n      number_of_tickets: $numberOfTickets\n      username: $username\n      seat_numbers: $seatNumbers\n      tip_amount: $tipAmount\n      tip_message: $tipMEssage\n    ) {\n      order_id\n      payment_id\n      provider_type\n      goto_url\n      username\n      mobile_number\n    }\n  }\n"): (typeof documents)["\n  mutation CreateSportEventPayment(\n    $ticketTypeId: Int!\n    $mobileNumber: String!\n    $numberOfTickets: Int\n    $username: String!\n    $seatNumbers: [Int!]!\n    $tipAmount: Float\n    $tipMEssage: String\n  ) {\n    createSportEventPayment(\n      ticket_type_id: $ticketTypeId\n      mobile_number: $mobileNumber\n      number_of_tickets: $numberOfTickets\n      username: $username\n      seat_numbers: $seatNumbers\n      tip_amount: $tipAmount\n      tip_message: $tipMEssage\n    ) {\n      order_id\n      payment_id\n      provider_type\n      goto_url\n      username\n      mobile_number\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation CreateEvent(\n    $name: String!\n    $description: String\n    $location: String!\n    $eventTicketType: [CreateEventTicketTypeInput!]\n    $eventImages: [Upload]!\n    $startDate: DateTime!\n    $endDate: DateTime!\n    $startBookingDate: DateTime\n    $address: String!\n    $orgName: String!\n    $orgContactNumber: String!\n    $isPublished: Boolean!\n    $isPrivateEvent: Boolean\n    $faq: String\n    $artists: [EventArtistsInput]\n    $openMicLink: String\n    $city: String!\n    $eventType: EventType\n  ) {\n    createEvent(\n      name: $name\n      description: $description\n      location: $location\n      event_ticket_types: $eventTicketType\n      event_images: $eventImages\n      start_date: $startDate\n      end_date: $endDate\n      start_booking_date: $startBookingDate\n      organizer_name: $orgName\n      org_contact_number: $orgContactNumber\n      address: $address\n      is_published: $isPublished\n      is_private_event: $isPrivateEvent\n      faq: $faq\n      artists: $artists\n      open_mic_link: $openMicLink\n      city: $city\n      event_type: $eventType\n    ) {\n      id\n      is_published\n    }\n  }\n"): (typeof documents)["\n  mutation CreateEvent(\n    $name: String!\n    $description: String\n    $location: String!\n    $eventTicketType: [CreateEventTicketTypeInput!]\n    $eventImages: [Upload]!\n    $startDate: DateTime!\n    $endDate: DateTime!\n    $startBookingDate: DateTime\n    $address: String!\n    $orgName: String!\n    $orgContactNumber: String!\n    $isPublished: Boolean!\n    $isPrivateEvent: Boolean\n    $faq: String\n    $artists: [EventArtistsInput]\n    $openMicLink: String\n    $city: String!\n    $eventType: EventType\n  ) {\n    createEvent(\n      name: $name\n      description: $description\n      location: $location\n      event_ticket_types: $eventTicketType\n      event_images: $eventImages\n      start_date: $startDate\n      end_date: $endDate\n      start_booking_date: $startBookingDate\n      organizer_name: $orgName\n      org_contact_number: $orgContactNumber\n      address: $address\n      is_published: $isPublished\n      is_private_event: $isPrivateEvent\n      faq: $faq\n      artists: $artists\n      open_mic_link: $openMicLink\n      city: $city\n      event_type: $eventType\n    ) {\n      id\n      is_published\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation CreatePayment(\n    $eventTicketTypeId: ID!\n    $mobileNumber: String!\n    $username: String!\n    $numberOfTickets: Int!\n    $couponHash: String\n    $isGift: Boolean\n    $recipientName: String\n    $recipientPhone: String\n    $recipientMessage: String\n    $tipAmount: Float\n    $tipMessage: String\n  ) {\n    createPayment(\n      event_ticket_type_id: $eventTicketTypeId\n      mobile_number: $mobileNumber\n      username: $username\n      num_of_tickets: $numberOfTickets\n      coupon_hash: $couponHash\n      is_gift: $isGift\n      recipient_name: $recipientName\n      recipient_phone: $recipientPhone\n      recipient_message: $recipientMessage\n      tip_amount: $tipAmount\n      tip_message: $tipMessage\n    ) {\n      order_id\n      payment_id\n      provider_type\n      goto_url\n      mobile_number\n      username\n    }\n  }\n"): (typeof documents)["\n  mutation CreatePayment(\n    $eventTicketTypeId: ID!\n    $mobileNumber: String!\n    $username: String!\n    $numberOfTickets: Int!\n    $couponHash: String\n    $isGift: Boolean\n    $recipientName: String\n    $recipientPhone: String\n    $recipientMessage: String\n    $tipAmount: Float\n    $tipMessage: String\n  ) {\n    createPayment(\n      event_ticket_type_id: $eventTicketTypeId\n      mobile_number: $mobileNumber\n      username: $username\n      num_of_tickets: $numberOfTickets\n      coupon_hash: $couponHash\n      is_gift: $isGift\n      recipient_name: $recipientName\n      recipient_phone: $recipientPhone\n      recipient_message: $recipientMessage\n      tip_amount: $tipAmount\n      tip_message: $tipMessage\n    ) {\n      order_id\n      payment_id\n      provider_type\n      goto_url\n      mobile_number\n      username\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation DeleteSpotlight($id: Int!) {\n    deleteSpotlight(id: $id) {\n      message\n    }\n  }\n"): (typeof documents)["\n  mutation DeleteSpotlight($id: Int!) {\n    deleteSpotlight(id: $id) {\n      message\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation Logout {\n    logout {\n      message\n    }\n  }\n"): (typeof documents)["\n  mutation Logout {\n    logout {\n      message\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation RedeemCoupon($coupon: String!, $eventId: ID!) {\n    redeemCoupon(coupon: $coupon, event_id: $eventId) {\n      rc\n      amount\n    }\n  }\n"): (typeof documents)["\n  mutation RedeemCoupon($coupon: String!, $eventId: ID!) {\n    redeemCoupon(coupon: $coupon, event_id: $eventId) {\n      rc\n      amount\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdateSpotlight(\n    $id: Int!\n    $image: Upload\n    $url: String\n    $order: Int\n    $isExternal: Boolean\n  ) {\n    updateSpotlight(\n      id: $id\n      image: $image\n      url: $url\n      is_external: $isExternal\n      order: $order\n    ) {\n      message\n    }\n  }\n"): (typeof documents)["\n  mutation UpdateSpotlight(\n    $id: Int!\n    $image: Upload\n    $url: String\n    $order: Int\n    $isExternal: Boolean\n  ) {\n    updateSpotlight(\n      id: $id\n      image: $image\n      url: $url\n      is_external: $isExternal\n      order: $order\n    ) {\n      message\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdateEvent(\n    $id: ID!\n    $name: String\n    $description: String\n    $location: String\n    $eventImages: [Upload]\n    $address: String\n    $orgName: String\n    $orgContactNumber: String\n    $isPublished: Boolean!\n    $isPrivateEvent: Boolean\n    $startDate: DateTime\n    $endDate: DateTime\n    $startBookingDate: DateTime\n    $openMicLink: String\n    $seatingPlan: Upload\n    $city: String\n    $faq: String\n  ) {\n    updateEvent(\n      id: $id\n      name: $name\n      description: $description\n      location: $location\n      event_images: $eventImages\n      organizer_name: $orgName\n      org_contact_number: $orgContactNumber\n      address: $address\n      is_published: $isPublished\n      is_private_event: $isPrivateEvent\n      start_date: $startDate\n      end_date: $endDate\n      start_booking_date: $startBookingDate\n      open_mic_link: $openMicLink\n      seating_plan: $seatingPlan\n      city: $city\n      faq: $faq\n    ) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation UpdateEvent(\n    $id: ID!\n    $name: String\n    $description: String\n    $location: String\n    $eventImages: [Upload]\n    $address: String\n    $orgName: String\n    $orgContactNumber: String\n    $isPublished: Boolean!\n    $isPrivateEvent: Boolean\n    $startDate: DateTime\n    $endDate: DateTime\n    $startBookingDate: DateTime\n    $openMicLink: String\n    $seatingPlan: Upload\n    $city: String\n    $faq: String\n  ) {\n    updateEvent(\n      id: $id\n      name: $name\n      description: $description\n      location: $location\n      event_images: $eventImages\n      organizer_name: $orgName\n      org_contact_number: $orgContactNumber\n      address: $address\n      is_published: $isPublished\n      is_private_event: $isPrivateEvent\n      start_date: $startDate\n      end_date: $endDate\n      start_booking_date: $startBookingDate\n      open_mic_link: $openMicLink\n      seating_plan: $seatingPlan\n      city: $city\n      faq: $faq\n    ) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpsertEventArtist(\n    $id: ID\n    $name: String\n    $avatar: Upload\n    $order: Int\n    $eventId: ID!\n    $social_link: String\n  ) {\n    upsertEventArtist(\n      id: $id\n      name: $name\n      avatar: $avatar\n      order: $order\n      event_id: $eventId\n      social_link: $social_link\n    ) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation UpsertEventArtist(\n    $id: ID\n    $name: String\n    $avatar: Upload\n    $order: Int\n    $eventId: ID!\n    $social_link: String\n  ) {\n    upsertEventArtist(\n      id: $id\n      name: $name\n      avatar: $avatar\n      order: $order\n      event_id: $eventId\n      social_link: $social_link\n    ) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query EventBySlug($slug: String!) {\n    eventBySlug(slug: $slug) {\n      id\n      name\n      description\n      type\n      location\n      eventTicketTypes {\n        id\n        ticket_type\n        price\n        maximum_capacity\n        status\n        is_convenience_fee_incl\n        is_gst_incl\n      }\n      seatingPlan {\n        id\n        path\n      }\n      images {\n        id\n        path\n        hash\n        width\n        height\n      }\n      slug\n      start_date\n      end_date\n      start_booking_date\n      address\n      org_contact_number\n      organizer_name\n      faq\n      artists {\n        id\n        name\n        avatar\n        social_link\n      }\n      open_mic_link\n      coupons {\n        id\n      }\n    }\n  }\n"): (typeof documents)["\n  query EventBySlug($slug: String!) {\n    eventBySlug(slug: $slug) {\n      id\n      name\n      description\n      type\n      location\n      eventTicketTypes {\n        id\n        ticket_type\n        price\n        maximum_capacity\n        status\n        is_convenience_fee_incl\n        is_gst_incl\n      }\n      seatingPlan {\n        id\n        path\n      }\n      images {\n        id\n        path\n        hash\n        width\n        height\n      }\n      slug\n      start_date\n      end_date\n      start_booking_date\n      address\n      org_contact_number\n      organizer_name\n      faq\n      artists {\n        id\n        name\n        avatar\n        social_link\n      }\n      open_mic_link\n      coupons {\n        id\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query Events(\n    $page: Int!\n    $currentDatetime: DateTime!\n    $name: String\n    $event_types: [String!]\n    $first: Int!\n  ) {\n    getEvents(\n      page: $page\n      current_datetime: $currentDatetime\n      name: $name\n      event_types: $event_types\n      first: $first\n    ) {\n      data {\n        id\n        name\n        start_date\n        end_date\n        type\n        images {\n          id\n          path\n          hash\n          width\n          height\n        }\n        slug\n        eventTicketTypes {\n          price\n          ticket_type\n        }\n        start_booking_date\n        open_mic_link\n        city\n      }\n      paginatorInfo {\n        lastPage\n        hasMorePages\n        currentPage\n      }\n    }\n  }\n"): (typeof documents)["\n  query Events(\n    $page: Int!\n    $currentDatetime: DateTime!\n    $name: String\n    $event_types: [String!]\n    $first: Int!\n  ) {\n    getEvents(\n      page: $page\n      current_datetime: $currentDatetime\n      name: $name\n      event_types: $event_types\n      first: $first\n    ) {\n      data {\n        id\n        name\n        start_date\n        end_date\n        type\n        images {\n          id\n          path\n          hash\n          width\n          height\n        }\n        slug\n        eventTicketTypes {\n          price\n          ticket_type\n        }\n        start_booking_date\n        open_mic_link\n        city\n      }\n      paginatorInfo {\n        lastPage\n        hasMorePages\n        currentPage\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query EventsForSuperAdmin(\n    $page: Int!\n    $currentDatetime: DateTime!\n    $name: String\n    $first: Int!\n    $type: String! # you can't run codegen without this coz it's required\n  ) {\n    events(\n      page: $page\n      current_datetime: $currentDatetime\n      name: $name\n      first: $first\n      type: $type\n    ) {\n      data {\n        id\n        name\n      }\n    }\n  }\n"): (typeof documents)["\n  query EventsForSuperAdmin(\n    $page: Int!\n    $currentDatetime: DateTime!\n    $name: String\n    $first: Int!\n    $type: String! # you can't run codegen without this coz it's required\n  ) {\n    events(\n      page: $page\n      current_datetime: $currentDatetime\n      name: $name\n      first: $first\n      type: $type\n    ) {\n      data {\n        id\n        name\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query getEvents(\n    $first: Int!\n    $page: Int\n    $name: String\n    $eventTypes: [String!]\n    $sortOrder: String\n  ) {\n    getEvents(\n      first: $first\n      page: $page\n      name: $name\n      event_types: $eventTypes\n      sort_order: $sortOrder\n    ) {\n      data {\n        id\n        name\n        type\n        start_date\n        end_date\n        images {\n          id\n          path\n          hash\n          width\n          height\n        }\n        slug\n        eventTicketTypes {\n          price\n          ticket_type\n        }\n        sportTicketTypes {\n          price\n          ticket_type\n        }\n        start_booking_date\n        open_mic_link\n        city\n      }\n      paginatorInfo {\n        hasMorePages\n        currentPage\n        lastPage\n      }\n    }\n  }\n"): (typeof documents)["\n  query getEvents(\n    $first: Int!\n    $page: Int\n    $name: String\n    $eventTypes: [String!]\n    $sortOrder: String\n  ) {\n    getEvents(\n      first: $first\n      page: $page\n      name: $name\n      event_types: $eventTypes\n      sort_order: $sortOrder\n    ) {\n      data {\n        id\n        name\n        type\n        start_date\n        end_date\n        images {\n          id\n          path\n          hash\n          width\n          height\n        }\n        slug\n        eventTicketTypes {\n          price\n          ticket_type\n        }\n        sportTicketTypes {\n          price\n          ticket_type\n        }\n        start_booking_date\n        open_mic_link\n        city\n      }\n      paginatorInfo {\n        hasMorePages\n        currentPage\n        lastPage\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetSeatsForSportsEvent($ticketTypeId: Int!) {\n    getSeatsForSportsEvent(ticket_type_id: $ticketTypeId) {\n      all_seats\n      available_seats\n      price\n      convenience_fee\n    }\n  }\n"): (typeof documents)["\n  query GetSeatsForSportsEvent($ticketTypeId: Int!) {\n    getSeatsForSportsEvent(ticket_type_id: $ticketTypeId) {\n      all_seats\n      available_seats\n      price\n      convenience_fee\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetSpotlightList {\n    getSpotlightList {\n      id\n      app_image_id\n      url\n      is_external\n      order\n      image {\n        id\n        path\n        hash\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetSpotlightList {\n    getSpotlightList {\n      id\n      app_image_id\n      url\n      is_external\n      order\n      image {\n        id\n        path\n        hash\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetConvenienceFee($ticketTypeId: ID!) {\n    getConvenienceFee(ticket_type_id: $ticketTypeId) {\n      amount\n    }\n  }\n"): (typeof documents)["\n  query GetConvenienceFee($ticketTypeId: ID!) {\n    getConvenienceFee(ticket_type_id: $ticketTypeId) {\n      amount\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetCouponsForEvent($eventId: ID!) {\n    getCouponsForEvent(event_id: $eventId) {\n      id\n      name\n      expiry_date\n      max_usage\n      is_published\n      discount_percent\n      coupon_usage_count\n    }\n  }\n"): (typeof documents)["\n  query GetCouponsForEvent($eventId: ID!) {\n    getCouponsForEvent(event_id: $eventId) {\n      id\n      name\n      expiry_date\n      max_usage\n      is_published\n      discount_percent\n      coupon_usage_count\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query EventById($id: ID!) {\n    eventById(id: $id) {\n      id\n      name\n      description\n      location\n      images {\n        id\n        path\n        hash\n        width\n        height\n      }\n      seatingPlan {\n        id\n        path\n        hash\n      }\n      address\n      org_contact_number\n      is_published\n      is_private_event\n      organizer_name\n      eventTicketTypes {\n        id\n        ticket_type\n        price\n        maximum_capacity\n      }\n      slug\n      start_date\n      end_date\n      start_booking_date\n      artists {\n        id\n        name\n        avatar\n        social_link\n        order\n      }\n      open_mic_link\n      city\n      faq\n    }\n  }\n"): (typeof documents)["\n  query EventById($id: ID!) {\n    eventById(id: $id) {\n      id\n      name\n      description\n      location\n      images {\n        id\n        path\n        hash\n        width\n        height\n      }\n      seatingPlan {\n        id\n        path\n        hash\n      }\n      address\n      org_contact_number\n      is_published\n      is_private_event\n      organizer_name\n      eventTicketTypes {\n        id\n        ticket_type\n        price\n        maximum_capacity\n      }\n      slug\n      start_date\n      end_date\n      start_booking_date\n      artists {\n        id\n        name\n        avatar\n        social_link\n        order\n      }\n      open_mic_link\n      city\n      faq\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetGuestList(\n    $eventId: ID!\n    $first: Int!\n    $page: Int\n    $userName: String\n    $mobileNumber: String\n    $ticketTypeId: ID\n  ) {\n    getGuestList(\n      event_id: $eventId\n      first: $first\n      page: $page\n      username: $userName\n      mobile_number: $mobileNumber\n      ticket_type_id: $ticketTypeId\n    ) {\n      __typename\n      ... on SportGuestList {\n        data {\n          id\n          username\n          mobile_number\n          number_of_tickets\n          seat_numbers\n          scanned_at\n          paymentOrder {\n            paymentable {\n              __typename\n              ... on PhonePePaymentOrder {\n                order_id\n                payment_id\n              }\n              ... on RzpayPaymentOrder {\n                order_id\n                payment_id\n              }\n            }\n          }\n          eventTicketType {\n            id\n            ticket_type\n            slug\n          }\n          tip_amount\n          tip_message\n        }\n        paginatorInfo {\n          lastPage\n          total\n          currentPage\n          hasMorePages\n        }\n      }\n      ... on GeneralGuestList {\n        data {\n          id\n          username\n          mobile_number\n          number_of_tickets\n          scanned_at\n          paymentOrder {\n            paymentable {\n              __typename\n              ... on PhonePePaymentOrder {\n                order_id\n                payment_id\n              }\n              ... on RzpayPaymentOrder {\n                order_id\n                payment_id\n              }\n            }\n          }\n          eventTicketType {\n            id\n            ticket_type\n          }\n          tip_amount\n          tip_message\n        }\n        paginatorInfo {\n          lastPage\n          total\n          currentPage\n          hasMorePages\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetGuestList(\n    $eventId: ID!\n    $first: Int!\n    $page: Int\n    $userName: String\n    $mobileNumber: String\n    $ticketTypeId: ID\n  ) {\n    getGuestList(\n      event_id: $eventId\n      first: $first\n      page: $page\n      username: $userName\n      mobile_number: $mobileNumber\n      ticket_type_id: $ticketTypeId\n    ) {\n      __typename\n      ... on SportGuestList {\n        data {\n          id\n          username\n          mobile_number\n          number_of_tickets\n          seat_numbers\n          scanned_at\n          paymentOrder {\n            paymentable {\n              __typename\n              ... on PhonePePaymentOrder {\n                order_id\n                payment_id\n              }\n              ... on RzpayPaymentOrder {\n                order_id\n                payment_id\n              }\n            }\n          }\n          eventTicketType {\n            id\n            ticket_type\n            slug\n          }\n          tip_amount\n          tip_message\n        }\n        paginatorInfo {\n          lastPage\n          total\n          currentPage\n          hasMorePages\n        }\n      }\n      ... on GeneralGuestList {\n        data {\n          id\n          username\n          mobile_number\n          number_of_tickets\n          scanned_at\n          paymentOrder {\n            paymentable {\n              __typename\n              ... on PhonePePaymentOrder {\n                order_id\n                payment_id\n              }\n              ... on RzpayPaymentOrder {\n                order_id\n                payment_id\n              }\n            }\n          }\n          eventTicketType {\n            id\n            ticket_type\n          }\n          tip_amount\n          tip_message\n        }\n        paginatorInfo {\n          lastPage\n          total\n          currentPage\n          hasMorePages\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetInvitees(\n    $username: String\n    $mobileNumber: String\n    $eventId: ID!\n    $first: Int!\n    $page: Int\n  ) {\n    getInvitees(\n      username: $username\n      mobile_number: $mobileNumber\n      event_id: $eventId\n      first: $first\n      page: $page\n    ) {\n      data {\n        id\n        username\n        mobile_number\n        whatsapp_sent_status\n        number_of_tickets\n        scanned_at\n        paymentOrder {\n          paymentable {\n            __typename\n            ... on PhonePePaymentOrder {\n              order_id\n              payment_id\n            }\n            ... on RzpayPaymentOrder {\n              order_id\n              payment_id\n            }\n          }\n        }\n      }\n      paginatorInfo {\n        lastPage\n        total\n        hasMorePages\n        currentPage\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetInvitees(\n    $username: String\n    $mobileNumber: String\n    $eventId: ID!\n    $first: Int!\n    $page: Int\n  ) {\n    getInvitees(\n      username: $username\n      mobile_number: $mobileNumber\n      event_id: $eventId\n      first: $first\n      page: $page\n    ) {\n      data {\n        id\n        username\n        mobile_number\n        whatsapp_sent_status\n        number_of_tickets\n        scanned_at\n        paymentOrder {\n          paymentable {\n            __typename\n            ... on PhonePePaymentOrder {\n              order_id\n              payment_id\n            }\n            ... on RzpayPaymentOrder {\n              order_id\n              payment_id\n            }\n          }\n        }\n      }\n      paginatorInfo {\n        lastPage\n        total\n        hasMorePages\n        currentPage\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetMe {\n    getMe {\n      user {\n        id\n        role\n        identifier\n      }\n      token\n    }\n  }\n"): (typeof documents)["\n  query GetMe {\n    getMe {\n      user {\n        id\n        role\n        identifier\n      }\n      token\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query MyEvents($page: Int!, $first: Int!) {\n    myEvents(page: $page, first: $first) {\n      data {\n        id\n        name\n        description\n        location\n        eventTicketTypes {\n          id\n          ticket_type\n          price\n          maximum_capacity\n        }\n        images {\n          id\n          path\n          hash\n          width\n          height\n        }\n        slug\n        start_date\n        end_date\n        address\n        org_contact_number\n        is_published\n        is_private_event\n      }\n      paginatorInfo {\n        lastPage\n        hasMorePages\n        currentPage\n      }\n    }\n  }\n"): (typeof documents)["\n  query MyEvents($page: Int!, $first: Int!) {\n    myEvents(page: $page, first: $first) {\n      data {\n        id\n        name\n        description\n        location\n        eventTicketTypes {\n          id\n          ticket_type\n          price\n          maximum_capacity\n        }\n        images {\n          id\n          path\n          hash\n          width\n          height\n        }\n        slug\n        start_date\n        end_date\n        address\n        org_contact_number\n        is_published\n        is_private_event\n      }\n      paginatorInfo {\n        lastPage\n        hasMorePages\n        currentPage\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetOrganizers($page: Int, $name: String) {\n    organizerList(page: $page, name: $name) {\n      hasMorePages\n      currentPage\n      total\n      organizers {\n        id\n        identifier\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetOrganizers($page: Int, $name: String) {\n    organizerList(page: $page, name: $name) {\n      hasMorePages\n      currentPage\n      total\n      organizers {\n        id\n        identifier\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query PastEvents(\n    $first: Int!\n    $currentDatetime: DateTime!\n    $page: Int\n    $type: String\n  ) {\n    getPastEvents(\n      first: $first\n      current_datetime: $currentDatetime\n      page: $page\n      type: $type\n    ) {\n      data {\n        id\n        name\n        type\n        end_date\n        images {\n          id\n          path\n          hash\n          width\n          height\n        }\n        slug\n      }\n      paginatorInfo {\n        hasMorePages\n        currentPage\n        lastPage\n      }\n    }\n  }\n"): (typeof documents)["\n  query PastEvents(\n    $first: Int!\n    $currentDatetime: DateTime!\n    $page: Int\n    $type: String\n  ) {\n    getPastEvents(\n      first: $first\n      current_datetime: $currentDatetime\n      page: $page\n      type: $type\n    ) {\n      data {\n        id\n        name\n        type\n        end_date\n        images {\n          id\n          path\n          hash\n          width\n          height\n        }\n        slug\n      }\n      paginatorInfo {\n        hasMorePages\n        currentPage\n        lastPage\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query SportsBySlug($slug: String!) {\n    eventBySlug(slug: $slug) {\n      id\n      name\n      description\n      location\n      type\n      sportTicketTypes {\n        id\n        price\n        ticket_type\n        start_seat_number\n        end_seat_number\n        slug\n      }\n      seatingPlan {\n        id\n        path\n      }\n      images {\n        id\n        path\n        hash\n        width\n        height\n      }\n      slug\n      start_date\n      end_date\n      start_booking_date\n      address\n      org_contact_number\n      organizer_name\n      faq\n      artists {\n        id\n        name\n        avatar\n        social_link\n      }\n      open_mic_link\n      coupons {\n        id\n      }\n    }\n  }\n"): (typeof documents)["\n  query SportsBySlug($slug: String!) {\n    eventBySlug(slug: $slug) {\n      id\n      name\n      description\n      location\n      type\n      sportTicketTypes {\n        id\n        price\n        ticket_type\n        start_seat_number\n        end_seat_number\n        slug\n      }\n      seatingPlan {\n        id\n        path\n      }\n      images {\n        id\n        path\n        hash\n        width\n        height\n      }\n      slug\n      start_date\n      end_date\n      start_booking_date\n      address\n      org_contact_number\n      organizer_name\n      faq\n      artists {\n        id\n        name\n        avatar\n        social_link\n      }\n      open_mic_link\n      coupons {\n        id\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query Sports(\n    $page: Int!\n    $currentDatetime: DateTime!\n    $name: String\n    $type: String!\n  ) {\n    events(\n      page: $page\n      current_datetime: $currentDatetime\n      name: $name\n      type: $type\n    ) {\n      data {\n        id\n        name\n        start_date\n        images {\n          id\n          path\n          hash\n          width\n          height\n        }\n        slug\n        sportTicketTypes {\n          price\n          ticket_type\n          start_seat_number\n          end_seat_number\n        }\n        start_booking_date\n        open_mic_link\n        city\n      }\n      paginatorInfo {\n        lastPage\n      }\n    }\n  }\n"): (typeof documents)["\n  query Sports(\n    $page: Int!\n    $currentDatetime: DateTime!\n    $name: String\n    $type: String!\n  ) {\n    events(\n      page: $page\n      current_datetime: $currentDatetime\n      name: $name\n      type: $type\n    ) {\n      data {\n        id\n        name\n        start_date\n        images {\n          id\n          path\n          hash\n          width\n          height\n        }\n        slug\n        sportTicketTypes {\n          price\n          ticket_type\n          start_seat_number\n          end_seat_number\n        }\n        start_booking_date\n        open_mic_link\n        city\n      }\n      paginatorInfo {\n        lastPage\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation AddStaff($username: String!, $password: String!) {\n    addStaff(username: $username, password: $password) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation AddStaff($username: String!, $password: String!) {\n    addStaff(username: $username, password: $password) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetStaffs {\n    getStaffs {\n      id\n      identifier\n      role\n    }\n  }\n"): (typeof documents)["\n  query GetStaffs {\n    getStaffs {\n      id\n      identifier\n      role\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation DeleteStaff($id: ID!) {\n    deleteStaff(id: $id) {\n      message\n    }\n  }\n"): (typeof documents)["\n  mutation DeleteStaff($id: ID!) {\n    deleteStaff(id: $id) {\n      message\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation CreateCoupon($data: [CreateCouponInput!]!) {\n    createCoupon(data: $data) {\n      message\n    }\n  }\n"): (typeof documents)["\n  mutation CreateCoupon($data: [CreateCouponInput!]!) {\n    createCoupon(data: $data) {\n      message\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation DeleteCoupon($couponId: ID!) {\n    deleteCoupon(coupon_id: $couponId) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation DeleteCoupon($couponId: ID!) {\n    deleteCoupon(coupon_id: $couponId) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdateCoupon(\n    $couponId: ID!\n    $name: String\n    $expiry_date: DateTime\n    $event_id: ID\n    $discount_percent: Float\n    $max_usage: Int\n    $is_published: Boolean\n  ) {\n    updateCoupon(\n      coupon_id: $couponId\n      name: $name\n      expiry_date: $expiry_date\n      event_id: $event_id\n      discount_percent: $discount_percent\n      max_usage: $max_usage\n      is_published: $is_published\n    ) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation UpdateCoupon(\n    $couponId: ID!\n    $name: String\n    $expiry_date: DateTime\n    $event_id: ID\n    $discount_percent: Float\n    $max_usage: Int\n    $is_published: Boolean\n  ) {\n    updateCoupon(\n      coupon_id: $couponId\n      name: $name\n      expiry_date: $expiry_date\n      event_id: $event_id\n      discount_percent: $discount_percent\n      max_usage: $max_usage\n      is_published: $is_published\n    ) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation AddInvitee(\n    $eventId: ID!\n    $username: String!\n    $mobileNumber: String!\n    $numberOfTickets: Int!\n  ) {\n    addInvitee(\n      event_id: $eventId\n      username: $username\n      mobile_number: $mobileNumber\n      number_of_tickets: $numberOfTickets\n    ) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation AddInvitee(\n    $eventId: ID!\n    $username: String!\n    $mobileNumber: String!\n    $numberOfTickets: Int!\n  ) {\n    addInvitee(\n      event_id: $eventId\n      username: $username\n      mobile_number: $mobileNumber\n      number_of_tickets: $numberOfTickets\n    ) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation DeleteEvent($id: ID!) {\n    deleteEvent(id: $id) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation DeleteEvent($id: ID!) {\n    deleteEvent(id: $id) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation DeleteImageById($id: ID!) {\n    deleteImageById(id: $id) {\n      message\n    }\n  }\n"): (typeof documents)["\n  mutation DeleteImageById($id: ID!) {\n    deleteImageById(id: $id) {\n      message\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation DeleteEventArtist($id: ID!) {\n    deleteEventArtist(event_artist_id: $id) {\n      message\n    }\n  }\n"): (typeof documents)["\n  mutation DeleteEventArtist($id: ID!) {\n    deleteEventArtist(event_artist_id: $id) {\n      message\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query TicketAvailable($orderId: String!, $paymentId: String!) {\n    isTicketDownloadable(order_id: $orderId, payment_id: $paymentId) {\n      ticket_path\n      amount\n    }\n  }\n"): (typeof documents)["\n  query TicketAvailable($orderId: String!, $paymentId: String!) {\n    isTicketDownloadable(order_id: $orderId, payment_id: $paymentId) {\n      ticket_path\n      amount\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetBookedSeatsForSeatedEvent($eventId: ID!) {\n    getBookedSeatsForSeatedEvent(event_id: $eventId)\n  }\n"): (typeof documents)["\n  query GetBookedSeatsForSeatedEvent($eventId: ID!) {\n    getBookedSeatsForSeatedEvent(event_id: $eventId)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation CreatePaymentForSeatTickets(\n    $eventId: Int!\n    $mobileNumber: String!\n    $seatNumbers: [String!]!\n    $tipAmount: Float\n    $tipMessage: String\n    $username: String!\n  ) {\n    createPaymentForSeatedTicket(\n      event_id: $eventId\n      mobile_number: $mobileNumber\n      seat_numbers: $seatNumbers\n      tip_amount: $tipAmount\n      tip_message: $tipMessage\n      username: $username\n    ) {\n      order_id\n      payment_id\n      provider_type\n      goto_url\n      username\n      mobile_number\n    }\n  }\n"): (typeof documents)["\n  mutation CreatePaymentForSeatTickets(\n    $eventId: Int!\n    $mobileNumber: String!\n    $seatNumbers: [String!]!\n    $tipAmount: Float\n    $tipMessage: String\n    $username: String!\n  ) {\n    createPaymentForSeatedTicket(\n      event_id: $eventId\n      mobile_number: $mobileNumber\n      seat_numbers: $seatNumbers\n      tip_amount: $tipAmount\n      tip_message: $tipMessage\n      username: $username\n    ) {\n      order_id\n      payment_id\n      provider_type\n      goto_url\n      username\n      mobile_number\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation OnBoard(\n    $email: String!\n    $name: String!\n    $address: String\n    $mobileNumber: String!\n  ) {\n    onboardOrganizer(\n      email: $email\n      name: $name\n      address: $address\n      mobile_number: $mobileNumber\n    ) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation OnBoard(\n    $email: String!\n    $name: String!\n    $address: String\n    $mobileNumber: String!\n  ) {\n    onboardOrganizer(\n      email: $email\n      name: $name\n      address: $address\n      mobile_number: $mobileNumber\n    ) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UserRequestRefund($eventId: ID!, $phoneNumber: String!) {\n    userRequestRefund(event_id: $eventId, phone_number: $phoneNumber) {\n      message\n    }\n  }\n"): (typeof documents)["\n  mutation UserRequestRefund($eventId: ID!, $phoneNumber: String!) {\n    userRequestRefund(event_id: $eventId, phone_number: $phoneNumber) {\n      message\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation SubmitQRCode($qrCode: String!) {\n    submitQRCode(qr_code: $qrCode) {\n      error\n    }\n  }\n"): (typeof documents)["\n  mutation SubmitQRCode($qrCode: String!) {\n    submitQRCode(qr_code: $qrCode) {\n      error\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query StaffEvents {\n    adminEvents {\n      id\n      name\n      eventTicketTypes {\n        id\n        ticket_type\n        price\n        maximum_capacity\n      }\n    }\n  }\n"): (typeof documents)["\n  query StaffEvents {\n    adminEvents {\n      id\n      name\n      eventTicketTypes {\n        id\n        ticket_type\n        price\n        maximum_capacity\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpsertSetting($key: String!, $value: String) {\n    upsertSetting(key: $key, value: $value) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation UpsertSetting($key: String!, $value: String) {\n    upsertSetting(key: $key, value: $value) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetSettingByKey($key: String) {\n    getSettingByKey(key: $key) {\n      id\n      key\n      value\n    }\n  }\n"): (typeof documents)["\n  query GetSettingByKey($key: String) {\n    getSettingByKey(key: $key) {\n      id\n      key\n      value\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query EventAttendees(\n    $eventID: ID!\n    $first: Int!\n    $page: Int\n    $username: String\n    $mobileNumber: String\n    $paymentStatus: String\n  ) {\n    eventAttendees(\n      event_id: $eventID\n      first: $first\n      page: $page\n      username: $username\n      mobile_number: $mobileNumber\n      payment_status: $paymentStatus\n    ) {\n      data {\n        id\n        username\n        mobile_number\n        amount_paid\n        paymentOrder {\n          paymentable {\n            __typename\n            ... on PhonePePaymentOrder {\n              id\n              order_id\n              status\n              refund_status\n              refund_error_detail\n            }\n            ... on RzpayPaymentOrder {\n              id\n              order_id\n              status\n              refund_status\n              refund_error_detail\n            }\n          }\n        }\n        payment_status\n        number_of_tickets\n        ticket_path\n      }\n      paginatorInfo {\n        lastPage\n        total\n        currentPage\n      }\n    }\n  }\n"): (typeof documents)["\n  query EventAttendees(\n    $eventID: ID!\n    $first: Int!\n    $page: Int\n    $username: String\n    $mobileNumber: String\n    $paymentStatus: String\n  ) {\n    eventAttendees(\n      event_id: $eventID\n      first: $first\n      page: $page\n      username: $username\n      mobile_number: $mobileNumber\n      payment_status: $paymentStatus\n    ) {\n      data {\n        id\n        username\n        mobile_number\n        amount_paid\n        paymentOrder {\n          paymentable {\n            __typename\n            ... on PhonePePaymentOrder {\n              id\n              order_id\n              status\n              refund_status\n              refund_error_detail\n            }\n            ... on RzpayPaymentOrder {\n              id\n              order_id\n              status\n              refund_status\n              refund_error_detail\n            }\n          }\n        }\n        payment_status\n        number_of_tickets\n        ticket_path\n      }\n      paginatorInfo {\n        lastPage\n        total\n        currentPage\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation IssueRefund($amount: Float!, $orderID: String!) {\n    issueRefund(amount: $amount, order_id: $orderID) {\n      status\n      reason\n    }\n  }\n"): (typeof documents)["\n  mutation IssueRefund($amount: Float!, $orderID: String!) {\n    issueRefund(amount: $amount, order_id: $orderID) {\n      status\n      reason\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation AddOrganizer($name: String!, $password: String!) {\n    addOrganizer(name: $name, password: $password) {\n      message\n    }\n  }\n"): (typeof documents)["\n  mutation AddOrganizer($name: String!, $password: String!) {\n    addOrganizer(name: $name, password: $password) {\n      message\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdateOrganizer($id: ID!, $name: String, $password: String) {\n    updateOrganizer(id: $id, name: $name, password: $password) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation UpdateOrganizer($id: ID!, $name: String, $password: String) {\n    updateOrganizer(id: $id, name: $name, password: $password) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query CheckPhonepeHealth {\n    checkPhonePeHealth {\n      data\n    }\n  }\n"): (typeof documents)["\n  query CheckPhonepeHealth {\n    checkPhonePeHealth {\n      data\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation NotifyUpcomingEvent($id: ID!) {\n    notifyUpcomingEvent(id: $id) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation NotifyUpcomingEvent($id: ID!) {\n    notifyUpcomingEvent(id: $id) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation Login($identifier: String!, $password: String!) {\n    adminLogin(username: $identifier, password: $password) {\n      user {\n        id\n        identifier\n        role\n      }\n      token\n    }\n  }\n"): (typeof documents)["\n  mutation Login($identifier: String!, $password: String!) {\n    adminLogin(username: $identifier, password: $password) {\n      user {\n        id\n        identifier\n        role\n      }\n      token\n    }\n  }\n"];

export function graphql(source: string) {
  return (documents as any)[source] ?? {};
}

export type DocumentType<TDocumentNode extends DocumentNode<any, any>> = TDocumentNode extends DocumentNode<  infer TType,  any>  ? TType  : never;