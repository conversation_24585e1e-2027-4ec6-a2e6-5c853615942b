import { createRequestHand<PERSON> } from "@react-router/express";
import { installGlobals } from "react-router";
import compression from "compression"
import express from "express"
import morgan from "morgan"
import { config } from "dotenv"
import sourceMapSupport from "source-map-support"

sourceMapSupport.install()
installGlobals()

const viteDevServer =
  process.env.NODE_ENV === "production" || process.env.NODE_ENV === "test"
    ? undefined
    : await import("vite").then((vite) =>
        vite.createServer({
          server: { middlewareMode: true },
        })
      )

const app = express()
config()

app.use(compression())

// http://expressjs.com/en/advanced/best-practice-security.html#at-a-minimum-disable-x-powered-by-header
app.disable("x-powered-by")

// Remix fingerprints its assets so we can cache forever.
if (viteDevServer) {
  app.use(viteDevServer.middlewares)
} else {
  app.use(
    "/assets",
    express.static("build/client/assets", {
      immutable: true,
      maxAge: "1y",
    })
  )
  // app.use(
  //   "/build",
  //   express.static("public/build", { immutable: true, maxAge: "1y" })
  // )
}

// Everything else (like favicon.ico) is cached for an hour. You may want to be
// more aggressive with this caching.
app.use(express.static("build/client", { maxAge: "1m" }))

app.use(morgan("tiny"))

app.all(
  "*",
  createRequestHandler({
    build: viteDevServer
      ? () => viteDevServer.ssrLoadModule("virtual:react-router/server-build")
      : await import("./build/server/index.js"),
    // getLoadContext() {
    //   return {
    //     rzpay_live_key: rzpay_key,
    //   }
    // },
  })
  // ? createDevRequestHandler()
  // : createProdRequestHandler()
)

const port = process.env.PORT || 3002
// const baseUrl = process.env.API_BASE_URL || "http://localhost:8002"

// const rzpay_key = process.env.RZPAY_LIVE_KEY

app.listen(port, async () => {
  console.log(`Express server listening on port ${port}`)

  // if (process.env.NODE_ENV === "development") {
  //   broadcastDevReady(build)
  // }
})

// function createDevRequestHandler() {
//   return async (req, res, next) => {
//     console.log("deve request handler")
//     try {
//       //
//       return createRequestHandler({
//         build: viteDevServer.ssrLoadModule("virtual:remix/server-build"),
//         // mode: "development",
//         // getLoadContext() {
//         //   return {
//         //     rzpay_live_key: rzpay_key,
//         //   }
//         // },
//       })(req, res, next)
//     } catch (error) {
//       next(error)
//     }
//   }
// }
//
// function createProdRequestHandler() {
//   return async (req, res, next) => {
//     try {
//       return createRequestHandler({
//         // build,
//         build: await import("./build/server/index.js"),
//         mode: process.env.NODE_ENV,
//         getLoadContext() {
//           return {
//             rzpay_live_key: rzpay_key,
//           }
//         },
//       })(req, res, next)
//     } catch (error) {
//       next(error)
//     }
//   }
// }
